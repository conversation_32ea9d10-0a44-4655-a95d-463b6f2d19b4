# NATS Server Configuration - Node 1
# FlowCustomV1 集群配置

# 服务器基本配置
server_name: "flowcustom-nats-1"
listen: 0.0.0.0:4222
http_port: 8222

# 集群配置
cluster {
    name: "flowcustom-nats-cluster"
    listen: 0.0.0.0:6222
    
    # 集群路由
    routes = [
        "nats://nats-2:6222"
        "nats://nats-3:6222"
    ]
    
    # 集群认证 (可选)
    # authorization {
    #     user: "cluster_user"
    #     password: "cluster_pass"
    # }
}

# JetStream 配置
jetstream {
    # 存储目录
    store_dir: "/data/jetstream"
    
    # 最大存储限制
    max_memory_store: 1GB
    max_file_store: 10GB
    
    # 集群配置
    unique_tag: "flowcustom-node-1"
}

# 日志配置
log_file: "/data/nats.log"
logtime: true
debug: false
trace: false

# 系统账户配置
system_account: SYS

# 账户配置
accounts {
    # 系统账户
    SYS {
        users = [
            { user: "admin", pass: "flowcustom_admin_123" }
        ]
    }
    
    # 应用账户
    APP {
        users = [
            { user: "flowcustom", pass: "flowcustom_app_123" }
        ]
        
        # 启用 JetStream
        jetstream: enabled
        
        # 资源限制
        limits: {
            max_connections: 1000
            max_subscriptions: 10000
            max_payload: 1MB
            max_data: 10GB
            max_ack_pending: 10000
        }
    }
}

# 连接限制
max_connections: 64K
max_control_line: 4KB
max_payload: 1MB
max_pending: 64MB

# 写入超时
write_deadline: "10s"

# 慢消费者检测
slow_consumer_threshold: "1s"

# TLS 配置 (生产环境建议启用)
# tls {
#     cert_file: "/etc/nats/certs/server.crt"
#     key_file: "/etc/nats/certs/server.key"
#     ca_file: "/etc/nats/certs/ca.crt"
#     verify: true
#     timeout: 5
# }

# 监控配置
monitoring {
    # HTTP 监控端点
    http_port: 8222
    
    # 监控路径
    # /varz - 服务器变量
    # /connz - 连接信息
    # /routez - 路由信息
    # /subsz - 订阅信息
    # /jsz - JetStream 信息
}

# 性能优化
# 禁用客户端广告
no_advertise: false

# 启用压缩
compression: s2_auto

# 内存优化
max_traced_msg_len: 32KB
