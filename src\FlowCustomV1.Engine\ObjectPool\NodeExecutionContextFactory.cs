using System;
using System.Collections.Generic;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// NodeExecutionContext工厂接口
    /// </summary>
    public interface INodeExecutionContextFactory : IDisposable
    {
        /// <summary>
        /// 创建配置好的NodeExecutionContext实例
        /// </summary>
        /// <param name="workflowExecutionId">工作流执行ID</param>
        /// <param name="node">节点定义</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="variables">工作流变量</param>
        /// <param name="logger">执行日志记录器</param>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>配置好的NodeExecutionContext实例</returns>
        NodeExecutionContext Create(
            Guid workflowExecutionId,
            WorkflowNode node,
            Dictionary<string, object> inputData,
            Dictionary<string, object> variables,
            IExecutionLogger logger,
            IServiceProvider serviceProvider,
            System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// 返回NodeExecutionContext实例到池中
        /// </summary>
        /// <param name="context">要返回的实例</param>
        void Return(NodeExecutionContext context);

        /// <summary>
        /// 获取工厂统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        NodeExecutionContextFactoryStatistics GetStatistics();
    }

    /// <summary>
    /// NodeExecutionContext工厂统计信息
    /// </summary>
    public class NodeExecutionContextFactoryStatistics
    {
        /// <summary>
        /// 总创建次数
        /// </summary>
        public int TotalCreations { get; set; }

        /// <summary>
        /// 总返回次数
        /// </summary>
        public int TotalReturns { get; set; }

        /// <summary>
        /// 当前活跃实例数量
        /// </summary>
        public int ActiveInstances { get; set; }

        /// <summary>
        /// 对象池统计信息
        /// </summary>
        public NodeExecutionContextPoolStatistics PoolStatistics { get; set; } = new();
    }

    /// <summary>
    /// NodeExecutionContext工厂实现
    /// </summary>
    public class NodeExecutionContextFactory : INodeExecutionContextFactory, IDisposable
    {
        private readonly INodeExecutionContextPool _pool;
        private readonly ILogger<NodeExecutionContextFactory>? _logger;
        private volatile int _totalCreations;
        private volatile int _totalReturns;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pool">NodeExecutionContext对象池</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="loggerFactory">日志工厂</param>
        public NodeExecutionContextFactory(
            INodeExecutionContextPool? pool = null,
            ILogger<NodeExecutionContextFactory>? logger = null,
            ILoggerFactory? loggerFactory = null)
        {
            _logger = logger;
            _pool = pool ?? new NodeExecutionContextPool(50, loggerFactory?.CreateLogger<NodeExecutionContextPool>());
        }

        /// <summary>
        /// 创建配置好的NodeExecutionContext实例
        /// </summary>
        public NodeExecutionContext Create(
            Guid workflowExecutionId,
            WorkflowNode node,
            Dictionary<string, object> inputData,
            Dictionary<string, object> variables,
            IExecutionLogger logger,
            IServiceProvider serviceProvider,
            System.Threading.CancellationToken cancellationToken)
        {
            if (node == null) throw new ArgumentNullException(nameof(node));
            if (inputData == null) throw new ArgumentNullException(nameof(inputData));
            if (variables == null) throw new ArgumentNullException(nameof(variables));
            if (logger == null) throw new ArgumentNullException(nameof(logger));
            if (serviceProvider == null) throw new ArgumentNullException(nameof(serviceProvider));

            System.Threading.Interlocked.Increment(ref _totalCreations);

            // 从对象池获取实例
            var context = _pool.Get();

            // 配置实例
            context.WorkflowExecutionId = workflowExecutionId;
            context.Node = node;
            context.Logger = logger;
            context.ServiceProvider = serviceProvider;
            context.CancellationToken = cancellationToken;

            // 复制输入数据和变量（避免共享引用）
            context.InputData.Clear();
            foreach (var kvp in inputData)
            {
                context.InputData[kvp.Key] = kvp.Value;
            }

            context.Variables.Clear();
            foreach (var kvp in variables)
            {
                context.Variables[kvp.Key] = kvp.Value;
            }

            // 确保Context字典是空的
            context.Context.Clear();

            _logger?.LogDebug(
                "创建NodeExecutionContext - 节点: {NodeId}, 工作流: {WorkflowExecutionId}, 执行ID: {NodeExecutionId}",
                node.Id, workflowExecutionId, context.NodeExecutionId);

            return context;
        }

        /// <summary>
        /// 返回NodeExecutionContext实例到池中
        /// </summary>
        public void Return(NodeExecutionContext context)
        {
            if (context == null)
            {
                _logger?.LogWarning("尝试返回null的NodeExecutionContext");
                return;
            }

            System.Threading.Interlocked.Increment(ref _totalReturns);

            _logger?.LogDebug(
                "返回NodeExecutionContext到池中 - 节点: {NodeId}, 执行ID: {NodeExecutionId}",
                context.Node?.Id, context.NodeExecutionId);

            _pool.Return(context);
        }

        /// <summary>
        /// 获取工厂统计信息
        /// </summary>
        public NodeExecutionContextFactoryStatistics GetStatistics()
        {
            var totalCreations = _totalCreations;
            var totalReturns = _totalReturns;

            return new NodeExecutionContextFactoryStatistics
            {
                TotalCreations = totalCreations,
                TotalReturns = totalReturns,
                ActiveInstances = totalCreations - totalReturns,
                PoolStatistics = _pool.GetStatistics()
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            var statistics = GetStatistics();
            
            _logger?.LogInformation(
                "NodeExecutionContextFactory已释放 - 总创建: {TotalCreations}, 总返回: {TotalReturns}, 活跃实例: {ActiveInstances}",
                statistics.TotalCreations, statistics.TotalReturns, statistics.ActiveInstances);

            _pool?.Dispose();
        }
    }

    /// <summary>
    /// NodeExecutionContext工厂扩展方法
    /// </summary>
    public static class NodeExecutionContextFactoryExtensions
    {
        /// <summary>
        /// 使用NodeExecutionContext执行操作
        /// </summary>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="factory">工厂实例</param>
        /// <param name="workflowExecutionId">工作流执行ID</param>
        /// <param name="node">节点定义</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="variables">工作流变量</param>
        /// <param name="logger">执行日志记录器</param>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="action">要执行的操作</param>
        /// <returns>操作结果</returns>
        public static TResult Use<TResult>(
            this INodeExecutionContextFactory factory,
            Guid workflowExecutionId,
            WorkflowNode node,
            Dictionary<string, object> inputData,
            Dictionary<string, object> variables,
            IExecutionLogger logger,
            IServiceProvider serviceProvider,
            System.Threading.CancellationToken cancellationToken,
            Func<NodeExecutionContext, TResult> action)
        {
            if (factory == null) throw new ArgumentNullException(nameof(factory));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var context = factory.Create(workflowExecutionId, node, inputData, variables, logger, serviceProvider, cancellationToken);
            try
            {
                return action(context);
            }
            finally
            {
                factory.Return(context);
            }
        }

        /// <summary>
        /// 使用NodeExecutionContext执行异步操作
        /// </summary>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="factory">工厂实例</param>
        /// <param name="workflowExecutionId">工作流执行ID</param>
        /// <param name="node">节点定义</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="variables">工作流变量</param>
        /// <param name="logger">执行日志记录器</param>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="action">要执行的异步操作</param>
        /// <returns>操作结果</returns>
        public static async System.Threading.Tasks.Task<TResult> UseAsync<TResult>(
            this INodeExecutionContextFactory factory,
            Guid workflowExecutionId,
            WorkflowNode node,
            Dictionary<string, object> inputData,
            Dictionary<string, object> variables,
            IExecutionLogger logger,
            IServiceProvider serviceProvider,
            System.Threading.CancellationToken cancellationToken,
            Func<NodeExecutionContext, System.Threading.Tasks.Task<TResult>> action)
        {
            if (factory == null) throw new ArgumentNullException(nameof(factory));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var context = factory.Create(workflowExecutionId, node, inputData, variables, logger, serviceProvider, cancellationToken);
            try
            {
                return await action(context);
            }
            finally
            {
                factory.Return(context);
            }
        }
    }
}
