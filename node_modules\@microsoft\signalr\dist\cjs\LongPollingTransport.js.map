{"version": 3, "file": "LongPollingTransport.js", "sourceRoot": "", "sources": ["../../src/LongPollingTransport.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,uDAAoD;AACpD,qCAAmD;AAEnD,uCAA8C;AAC9C,6CAA0D;AAC1D,mCAA8E;AAG9E,oDAAoD;AACpD,eAAe;AACf,MAAa,oBAAoB;IAc7B,uFAAuF;IACvF,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,YAAY,UAAsB,EAAE,MAAe,EAAE,OAA+B;QAChF,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,iCAAe,EAAE,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,WAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,WAAG,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,WAAG,CAAC,IAAI,CAAC,cAAc,EAAE,2BAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,qCAAqC,CAAC,CAAC;QAExE,4HAA4H;QAC5H,IAAI,cAAc,KAAK,2BAAc,CAAC,MAAM;YACxC,CAAC,OAAO,cAAc,KAAK,WAAW,IAAI,OAAO,IAAI,cAAc,EAAE,CAAC,YAAY,KAAK,QAAQ,CAAC,EAAE;YAClG,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC,CAAC;SACjH;QAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,IAAA,0BAAkB,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAE5D,MAAM,WAAW,GAAgB;YAC7B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO;YACP,OAAO,EAAE,MAAM;YACf,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;SACjD,CAAC;QAEF,IAAI,cAAc,KAAK,2BAAc,CAAC,MAAM,EAAE;YAC1C,WAAW,CAAC,YAAY,GAAG,aAAa,CAAC;SAC5C;QAED,oCAAoC;QACpC,uGAAuG;QACvG,MAAM,OAAO,GAAG,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oCAAoC,OAAO,GAAG,CAAC,CAAC;QACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,qDAAqD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;YAE9G,mFAAmF;YACnF,IAAI,CAAC,WAAW,GAAG,IAAI,kBAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACjF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,GAAW,EAAE,WAAwB;QACrD,IAAI;YACA,OAAO,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI;oBACA,MAAM,OAAO,GAAG,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;oBACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oCAAoC,OAAO,GAAG,CAAC,CAAC;oBACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAElE,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;wBAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,WAAW,EAAE,oDAAoD,CAAC,CAAC;wBAE7F,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACzB;yBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;wBACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,qDAAqD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;wBAE9G,yBAAyB;wBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,kBAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;wBACjF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACzB;yBAAM;wBACH,uBAAuB;wBACvB,IAAI,QAAQ,CAAC,OAAO,EAAE;4BAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,0CAA0C,IAAA,qBAAa,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;4BACjJ,IAAI,IAAI,CAAC,SAAS,EAAE;gCAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;6BACpC;yBACJ;6BAAM;4BACH,wCAAwC;4BACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oDAAoD,CAAC,CAAC;yBAC1F;qBACJ;iBACJ;gBAAC,OAAO,CAAC,EAAE;oBACR,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAChB,qDAAqD;wBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,wDAAyD,CAAS,CAAC,OAAO,EAAE,CAAC,CAAC;qBAClH;yBAAM;wBACH,IAAI,CAAC,YAAY,qBAAY,EAAE;4BAC3B,wCAAwC;4BACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oDAAoD,CAAC,CAAC;yBAC1F;6BAAM;4BACH,qDAAqD;4BACrD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;4BACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;yBACzB;qBACJ;iBACJ;aACJ;SACJ;gBAAS;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,2CAA2C,CAAC,CAAC;YAE9E,gHAAgH;YAChH,2HAA2H;YAC3H,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;SACJ;IACL,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAS;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,IAAA,mBAAW,EAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvG,CAAC;IAEM,KAAK,CAAC,IAAI;QACb,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,2CAA2C,CAAC,CAAC;QAE9E,yFAAyF;QACzF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,IAAI;YACA,MAAM,IAAI,CAAC,UAAU,CAAC;YAEtB,qDAAqD;YACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,qDAAqD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAEpG,MAAM,OAAO,GAA0B,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,IAAA,0BAAkB,GAAE,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAEtB,MAAM,aAAa,GAAgB;gBAC/B,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACjD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAC9B,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;aACjD,CAAC;YAEF,IAAI,KAAK,CAAC;YACV,IAAI;gBACA,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAK,EAAE,aAAa,CAAC,CAAC;aAC5D;YAAC,OAAO,GAAG,EAAE;gBACV,KAAK,GAAG,GAAG,CAAC;aACf;YAED,IAAI,KAAK,EAAE;gBACP,IAAI,KAAK,YAAY,kBAAS,EAAE;oBAC5B,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE;wBAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,oFAAoF,CAAC,CAAC;qBAC1H;yBAAM;wBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,2DAA2D,KAAK,EAAE,CAAC,CAAC;qBACxG;iBACJ;aACJ;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,kDAAkD,CAAC,CAAC;aACxF;SAEJ;gBAAS;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,wCAAwC,CAAC,CAAC;YAE3E,+CAA+C;YAC/C,sDAAsD;YACtD,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,UAAU,GAAG,+CAA+C,CAAC;YACjE,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,UAAU,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;aAC/C;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAClC;IACL,CAAC;CACJ;AA3MD,oDA2MC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error | unknown;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error | unknown) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${(e as any).message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n\r\n            let error;\r\n            try {\r\n                await this._httpClient.delete(this._url!, deleteOptions);\r\n            } catch (err) {\r\n                error = err;\r\n            }\r\n\r\n            if (error) {\r\n                if (error instanceof HttpError) {\r\n                    if (error.statusCode === 404) {\r\n                        this._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\r\n                    } else {\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\r\n                    }\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\r\n            }\r\n\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n"]}