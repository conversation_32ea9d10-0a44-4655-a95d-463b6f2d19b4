[2025-07-31 16:31:51.524 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 16:31:51.595 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 16:31:51.601 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 16:31:51.612 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 16:31:51.618 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 16:31:51.622 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 16:31:51.627 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 16:31:51.631 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 16:31:51.634 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 16:31:51.639 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 16:31:51.643 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 16:31:51.649 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 16:31:51.654 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 16:31:51.658 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 16:31:51.661 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 16:31:51.682 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 16:31:51.686 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 16:31:51.700 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 16:31:51.702 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 16:31:51.703 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 16:31:51.710 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 16:31:51.719 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 16:31:51.724 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 16:31:51.727 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 16:31:51.728 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 16:31:51.736 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 16:31:51.742 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,343,880 字节
[2025-07-31 16:31:51.748 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 16:31:51.755 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Worker", NodeId: worker-001
[2025-07-31 16:31:51.761 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Worker", 节点ID: worker-001
[2025-07-31 16:31:51.770 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: system.notification
[2025-07-31 16:31:51.774 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.heartbeat
[2025-07-31 16:31:51.778 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.events
[2025-07-31 16:31:51.785 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.heartbeat
[2025-07-31 16:31:51.785 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.node_registration
[2025-07-31 16:31:51.792 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: worker-001
[2025-07-31 16:31:51.796 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 16:31:51.802 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 16:31:51.807 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 16:31:51.816 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\debug
[2025-07-31 16:31:51.821 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\info
[2025-07-31 16:31:51.826 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\warn
[2025-07-31 16:31:51.831 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\error
[2025-07-31 16:31:51.837 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\trace
[2025-07-31 16:31:51.842 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 16:31:51.854 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 16:31:51.859 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 16:31:52.744 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:31:52.750 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:31:52.758 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:31:52.764 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:31:52.770 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:31:53.135 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 16:31:53.144 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 16:31:53.330 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 16:31:53.339 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 16:31:53.345 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 16:31:53.546 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:31:53.592 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 16:31:53.644 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 16:31:53.652 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 16:31:53.981 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 16:31:53.990 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 16:31:53.997 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 16:31:54.002 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 16:31:54.006 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 16:31:54.011 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 16:31:54.155 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 16:31:54.159 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 16:31:54.166 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 16:31:54.170 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 16:31:54.175 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 16:31:54.179 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 16:31:54.184 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 16:31:54.189 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 16:31:54.198 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 16:31:54.208 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 16:31:54.215 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 16:31:54.222 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 16:31:54.229 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 16:31:54.235 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 16:31:54.242 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 16:31:54.250 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 16:31:54.256 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 16:31:54.266 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 16:31:54.282 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 16:31:54.292 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 16:31:54.298 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 16:31:54.304 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 16:31:54.309 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 16:31:54.314 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 16:31:54.321 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 16:31:54.326 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 16:31:54.332 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 16:31:54.338 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 16:31:54.343 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 16:31:54.350 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 16:31:54.358 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 16:31:54.362 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 16:31:54.373 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 16:31:54.386 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 16:31:54.393 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 16:31:54.400 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 16:31:54.406 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:31:54.412 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:31:54.416 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 16:31:54.422 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 16:31:54.427 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 16:31:54.441 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 16:31:54.450 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 16:31:54.456 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 16:31:54.470 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 16:31:54.726 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 16:31:54.737 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 16:31:54.743 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 16:31:54.749 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 16:31:54.756 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 16:31:54.762 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 16:31:54.786 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 16:31:54.792 +08:00 INF] Program: 后台初始化完成
[2025-07-31 16:31:56.730 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 16:32:01.724 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 16:32:21.618 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 16:32:24.320 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:32:54.316 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:33:24.328 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:33:54.315 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:34:05.820 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 16:34:05.852 +08:00 INF] Program: 请求完成: GET /api/cluster/health - 200 - 22ms
[2025-07-31 16:34:24.321 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:34:54.314 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:36:07.095 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 16:36:07.162 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 16:36:07.171 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 16:36:07.185 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 16:36:07.190 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 16:36:07.195 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 16:36:07.200 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 16:36:07.206 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 16:36:07.211 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 16:36:07.218 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 16:36:07.225 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 16:36:07.235 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 16:36:07.241 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 16:36:07.246 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 16:36:07.251 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 16:36:07.268 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 16:36:07.287 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 16:36:07.289 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 16:36:07.293 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 16:36:07.313 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 16:36:07.317 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 16:36:07.323 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 16:36:07.328 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 16:36:07.342 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 16:36:07.349 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 16:36:07.354 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 16:36:07.379 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,535,896 字节
[2025-07-31 16:36:07.389 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 16:36:07.402 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Master", NodeId: master-001
[2025-07-31 16:36:07.415 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Master", 节点ID: master-001
[2025-07-31 16:36:07.427 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 1/10)
[2025-07-31 16:36:08.434 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 2/10)
[2025-07-31 16:36:08.595 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:36:08.603 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:36:08.612 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:36:08.621 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:36:08.631 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:36:09.032 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 16:36:09.041 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 16:36:09.158 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 16:36:09.168 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 16:36:09.174 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 16:36:09.383 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:36:09.417 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 16:36:09.441 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 3/10)
[2025-07-31 16:36:09.514 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 16:36:09.523 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 16:36:09.875 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 16:36:09.894 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 16:36:09.903 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 16:36:09.909 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 16:36:09.914 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 16:36:09.921 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 16:36:10.082 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 16:36:10.088 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 16:36:10.110 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 16:36:10.118 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 16:36:10.124 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 16:36:10.133 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 16:36:10.141 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 16:36:10.147 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 16:36:10.163 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 16:36:10.175 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 16:36:10.185 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 16:36:10.193 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 16:36:10.201 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 16:36:10.208 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 16:36:10.216 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 16:36:10.224 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 16:36:10.233 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 16:36:10.244 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 16:36:10.270 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 16:36:10.279 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 16:36:10.287 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 16:36:10.294 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 16:36:10.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 16:36:10.310 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 16:36:10.317 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 16:36:10.323 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 16:36:10.329 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 16:36:10.336 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 16:36:10.340 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 16:36:10.345 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 16:36:10.350 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 16:36:10.354 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 16:36:10.369 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 16:36:10.379 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 16:36:10.385 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 16:36:10.389 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 16:36:10.393 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:36:10.397 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:36:10.401 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 16:36:10.404 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 16:36:10.408 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 16:36:10.422 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 16:36:10.428 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 16:36:10.431 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 16:36:10.441 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 16:36:10.508 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 4/10)
[2025-07-31 16:36:10.670 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 16:36:10.678 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 16:36:10.682 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 16:36:10.686 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 16:36:10.691 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 16:36:10.695 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 16:36:10.707 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 16:36:10.713 +08:00 INF] Program: 后台初始化完成
[2025-07-31 16:36:11.518 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.heartbeat
[2025-07-31 16:36:11.526 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.events
[2025-07-31 16:36:11.535 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ NATS订阅初始化成功
[2025-07-31 16:36:11.622 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: master-001
[2025-07-31 16:36:11.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 16:36:11.645 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 16:36:11.656 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 16:36:11.737 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 16:36:11.744 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 16:36:12.346 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 16:36:17.321 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 16:36:37.183 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 16:36:40.317 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:37:10.311 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:37:40.312 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:38:10.320 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:38:40.313 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:38:42.504 +08:00 INF] Program: 请求完成: GET /api/cluster/status - 200 - 61ms
[2025-07-31 16:39:10.321 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:39:40.314 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:40:10.318 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:40:40.316 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:41:10.318 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
