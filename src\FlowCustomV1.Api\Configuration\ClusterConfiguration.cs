using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Api.Configuration;

/// <summary>
/// 集群配置
/// </summary>
public class ClusterConfiguration
{
    /// <summary>
    /// 是否启用集群模式
    /// </summary>
    public bool EnableClusterMode { get; set; } = false;

    /// <summary>
    /// 节点运行模式
    /// </summary>
    public NodeMode NodeMode { get; set; } = NodeMode.Standalone;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = Environment.MachineName;

    /// <summary>
    /// 节点显示名称
    /// </summary>
    public string NodeDisplayName { get; set; } = "FlowCustomV1节点";

    /// <summary>
    /// 集群名称
    /// </summary>
    public string ClusterName { get; set; } = "flowcustom-cluster";

    /// <summary>
    /// Master节点配置
    /// </summary>
    public MasterNodeConfiguration Master { get; set; } = new();

    /// <summary>
    /// Worker节点配置
    /// </summary>
    public WorkerNodeConfiguration Worker { get; set; } = new();

    /// <summary>
    /// NATS通信配置
    /// </summary>
    public NatsCommunicationConfiguration Communication { get; set; } = new();

    /// <summary>
    /// 验证配置
    /// </summary>
    public void Validate()
    {
        if (EnableClusterMode)
        {
            if (string.IsNullOrWhiteSpace(NodeId))
                throw new ValidationException("集群模式下NodeId不能为空");

            if (string.IsNullOrWhiteSpace(ClusterName))
                throw new ValidationException("集群模式下ClusterName不能为空");

            Communication.Validate();

            if (NodeMode == NodeMode.Master)
                Master.Validate();
            else if (NodeMode == NodeMode.Worker)
                Worker.Validate();
        }
    }
}

/// <summary>
/// 节点运行模式
/// </summary>
public enum NodeMode
{
    /// <summary>
    /// 独立模式（原有模式）
    /// </summary>
    Standalone,

    /// <summary>
    /// Master节点模式
    /// </summary>
    Master,

    /// <summary>
    /// Worker节点模式
    /// </summary>
    Worker
}

/// <summary>
/// Master节点配置
/// </summary>
public class MasterNodeConfiguration
{
    /// <summary>
    /// API端口
    /// </summary>
    public int ApiPort { get; set; } = 5279;

    /// <summary>
    /// 是否启用Swagger
    /// </summary>
    public bool EnableSwagger { get; set; } = true;

    /// <summary>
    /// 是否启用CORS
    /// </summary>
    public bool EnableCors { get; set; } = true;

    /// <summary>
    /// CORS允许的源
    /// </summary>
    public string[] CorsOrigins { get; set; } = { "http://localhost:5173", "http://localhost:3000" };

    /// <summary>
    /// 任务调度配置
    /// </summary>
    public TaskSchedulingConfiguration TaskScheduling { get; set; } = new();

    /// <summary>
    /// 验证配置
    /// </summary>
    public void Validate()
    {
        if (ApiPort <= 0 || ApiPort > 65535)
            throw new ValidationException("ApiPort必须在1-65535范围内");

        TaskScheduling.Validate();
    }
}

/// <summary>
/// Worker节点配置
/// </summary>
public class WorkerNodeConfiguration
{
    /// <summary>
    /// 最大并发执行数
    /// </summary>
    public int MaxConcurrentExecutions { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Worker类型
    /// </summary>
    public string WorkerType { get; set; } = "general";

    /// <summary>
    /// 支持的节点类型
    /// </summary>
    public string[] SupportedNodeTypes { get; set; } = { "script", "http", "timer", "webhook", "condition", "loop" };

    /// <summary>
    /// 健康检查间隔
    /// </summary>
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// 任务超时时间
    /// </summary>
    public TimeSpan TaskTimeout { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// 验证配置
    /// </summary>
    public void Validate()
    {
        if (MaxConcurrentExecutions <= 0)
            throw new ValidationException("MaxConcurrentExecutions必须大于0");

        if (string.IsNullOrWhiteSpace(WorkerType))
            throw new ValidationException("WorkerType不能为空");
    }
}

/// <summary>
/// 任务调度配置
/// </summary>
public class TaskSchedulingConfiguration
{
    /// <summary>
    /// 负载均衡策略
    /// </summary>
    public string LoadBalancingStrategy { get; set; } = "round_robin";

    /// <summary>
    /// 任务超时时间
    /// </summary>
    public TimeSpan TaskTimeout { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 重试延迟
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// 验证配置
    /// </summary>
    public void Validate()
    {
        if (MaxRetryAttempts < 0)
            throw new ValidationException("MaxRetryAttempts不能小于0");
    }
}

/// <summary>
/// NATS通信配置
/// </summary>
public class NatsCommunicationConfiguration
{
    /// <summary>
    /// NATS连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = "nats://localhost:4222";

    /// <summary>
    /// 连接名称
    /// </summary>
    public string ConnectionName { get; set; } = "FlowCustomV1-Node";

    /// <summary>
    /// 心跳间隔
    /// </summary>
    public TimeSpan HeartbeatInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// 连接超时
    /// </summary>
    public TimeSpan ConnectTimeout { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary>
    /// 最大重连次数
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = -1; // -1表示无限重连

    /// <summary>
    /// 重连等待时间
    /// </summary>
    public TimeSpan ReconnectWait { get; set; } = TimeSpan.FromSeconds(2);

    /// <summary>
    /// 验证配置
    /// </summary>
    public void Validate()
    {
        if (string.IsNullOrWhiteSpace(ConnectionString))
            throw new ValidationException("NATS ConnectionString不能为空");

        if (string.IsNullOrWhiteSpace(ConnectionName))
            throw new ValidationException("NATS ConnectionName不能为空");
    }
}

/// <summary>
/// NATS主题常量
/// </summary>
public static class NatsSubjects
{
    /// <summary>
    /// 工作流任务分发
    /// </summary>
    public const string WorkflowTasks = "workflow.tasks";

    /// <summary>
    /// 工作流状态更新
    /// </summary>
    public const string WorkflowStatus = "workflow.status";

    /// <summary>
    /// 节点状态更新
    /// </summary>
    public const string NodeStatus = "node.status";

    /// <summary>
    /// 集群心跳
    /// </summary>
    public const string ClusterHeartbeat = "cluster.heartbeat";

    /// <summary>
    /// 集群事件
    /// </summary>
    public const string ClusterEvents = "cluster.events";

    /// <summary>
    /// 系统通知
    /// </summary>
    public const string SystemNotifications = "system.notifications";
}
