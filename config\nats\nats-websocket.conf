# NATS Server Configuration with WebSocket support for FlowCustomV1
# 支持WebSocket连接的NATS服务器配置

# 服务器基本设置
server_name: "FlowCustomV1-NATS-WebSocket"
port: 4222

# HTTP监控端口
http_port: 8222

# WebSocket配置 - 关键配置！
websocket {
  # WebSocket监听端口 (避免与Worker节点8080端口冲突)
  port: 8081
  # 不使用TLS（开发环境）
  no_tls: true
  # 允许跨域（开发环境）
  same_origin: false
  # 允许的来源
  allowed_origins: [
    "http://localhost:5173",
    "http://localhost:3000", 
    "http://localhost:5279"
  ]
  # 压缩支持
  compression: true
  # 握手超时
  handshake_timeout: "5s"
}

# JetStream配置
jetstream {
  # 存储目录
  store_dir: "/data"
  # 内存存储限制
  max_memory_store: 256MB
  # 文件存储限制
  max_file_store: 2GB
}

# 日志设置
debug: false
trace: false
logtime: true
log_file: "/tmp/nats-server.log"

# 连接限制
max_connections: 1000
max_subscriptions: 0
max_payload: 1MB
max_pending: 64MB

# 心跳设置
ping_interval: "2m"
ping_max: 2

# 写入超时
write_deadline: "10s"

# 客户端认证（开发环境暂时禁用）
# authorization {
#   user: "flowcustom"
#   password: "flowcustom123"
# }

# 集群设置（可选，用于多节点部署）
# cluster {
#   name: "FlowCustomV1"
#   listen: 0.0.0.0:6222
#   routes: [
#     nats-route://nats1:6222
#     nats-route://nats2:6222
#   ]
# }

# 监控设置
monitor_port: 8222

# 开发环境：禁用认证和授权
# 系统账户（用于JetStream管理）
# system_account: SYS

# 账户配置（开发环境禁用）
# accounts {
#   SYS {
#     users = [
#       { user: "admin", pass: "admin123" }
#     ]
#   }
#
#   FLOWCUSTOM {
#     users = [
#       { user: "flowcustom", pass: "flowcustom123" }
#     ]
#     jetstream: enabled
#   }
# }

# 默认权限（开发环境禁用）
# authorization {
#   default_permissions: {
#     publish: {
#       allow: ["workflow.>", "node.>", "cluster.>", "system.>"]
#     }
#     subscribe: {
#       allow: ["workflow.>", "node.>", "cluster.>", "system.>"]
#     }
#   }
# }
