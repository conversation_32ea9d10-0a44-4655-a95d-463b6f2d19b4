<!DOCTYPE html>
<html>
<head>
    <title>NATS WebSocket Test</title>
</head>
<body>
    <h1>NATS WebSocket Connection Test</h1>
    <div>状态: <span id="status">准备中...</span></div>
    <div id="log" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap;"></div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        // 测试原生WebSocket连接到NATS
        function testWebSocketConnection() {
            addLog('🔌 开始测试原生WebSocket连接到 ws://localhost:8081');
            status.textContent = '正在连接...';
            status.style.color = 'orange';
            
            const ws = new WebSocket('ws://localhost:8081');
            
            ws.onopen = function(event) {
                addLog('✅ WebSocket连接成功！');
                status.textContent = '连接成功';
                status.style.color = 'green';
                
                // 发送NATS CONNECT消息
                const connectMsg = 'CONNECT {"verbose":false,"pedantic":false,"tls_required":false,"name":"test-client","lang":"javascript","version":"1.0.0"}\r\n';
                ws.send(connectMsg);
                addLog('📤 发送CONNECT消息');
            };
            
            ws.onerror = function(error) {
                addLog('❌ WebSocket连接错误: ' + error);
                status.textContent = '连接失败';
                status.style.color = 'red';
            };
            
            ws.onclose = function(event) {
                addLog(`🔌 WebSocket连接关闭: 代码=${event.code}, 原因="${event.reason}"`);
                status.textContent = '连接关闭';
                status.style.color = 'orange';
            };
            
            ws.onmessage = function(event) {
                addLog('📨 收到NATS消息: ' + event.data);
            };
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testWebSocketConnection);
    </script>
</body>
</html>
