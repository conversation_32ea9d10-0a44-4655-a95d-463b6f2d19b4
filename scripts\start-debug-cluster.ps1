# FlowCustomV1 集群调试环境启动脚本
# 作者: FlowCustomV1 Team
# 创建时间: 2025-07-31

param(
    [switch]$Build,
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status
)

$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-ColorOutput Magenta "🚀 $message"
    Write-ColorOutput Magenta ("=" * 50)
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 显示服务状态
function Show-Status {
    Write-Header "FlowCustomV1 集群调试环境状态"
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker未运行，请先启动Docker"
        return
    }
    
    Write-Info "检查服务状态..."
    docker-compose -f docker-compose.debug.yml ps
    
    Write-Info "`n检查服务健康状态..."
    $services = @("flowcustom-mysql-debug", "flowcustom-redis-debug", "flowcustom-nats-debug", "flowcustom-master-debug", "flowcustom-worker1-debug", "flowcustom-worker2-debug", "flowcustom-frontend-debug")
    
    foreach ($service in $services) {
        $health = docker inspect --format='{{.State.Health.Status}}' $service 2>$null
        if ($health) {
            if ($health -eq "healthy") {
                Write-Success "$service : 健康"
            } elseif ($health -eq "starting") {
                Write-Warning "$service : 启动中"
            } else {
                Write-Error "$service : $health"
            }
        } else {
            $status = docker inspect --format='{{.State.Status}}' $service 2>$null
            if ($status) {
                if ($status -eq "running") {
                    Write-Success "$service : 运行中"
                } else {
                    Write-Warning "$service : $status"
                }
            } else {
                Write-Warning "$service : 未找到"
            }
        }
    }
    
    Write-Info "`n访问地址:"
    Write-Info "• Master API: http://localhost:5279"
    Write-Info "• Swagger UI: http://localhost:5279/swagger"
    Write-Info "• 前端界面: http://localhost:5173"
    Write-Info "• NATS监控: http://localhost:8222"
    Write-Info "• MySQL: localhost:3306 (用户: flowcustom, 密码: flowcustom123)"
    Write-Info "• Redis: localhost:6379"
}

# 停止服务
function Stop-Services {
    Write-Header "停止FlowCustomV1集群调试环境"
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker未运行"
        return
    }
    
    Write-Info "停止所有服务..."
    docker-compose -f docker-compose.debug.yml down
    
    Write-Success "所有服务已停止"
}

# 清理环境
function Clean-Environment {
    Write-Header "清理FlowCustomV1集群调试环境"
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker未运行"
        return
    }
    
    Write-Warning "这将删除所有容器、网络和数据卷！"
    $confirm = Read-Host "确认继续? (y/N)"
    
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Write-Info "停止并删除所有服务..."
        docker-compose -f docker-compose.debug.yml down -v --remove-orphans
        
        Write-Info "删除相关镜像..."
        docker images | Select-String "flowcustom.*debug" | ForEach-Object {
            $imageId = ($_ -split "\s+")[2]
            docker rmi $imageId -f 2>$null
        }
        
        Write-Success "环境清理完成"
    } else {
        Write-Info "取消清理操作"
    }
}

# 查看日志
function Show-Logs {
    Write-Header "FlowCustomV1集群调试环境日志"
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker未运行"
        return
    }
    
    Write-Info "显示所有服务日志 (按Ctrl+C退出)..."
    docker-compose -f docker-compose.debug.yml logs -f
}

# 构建并启动服务
function Start-Services {
    param([bool]$rebuild = $false)
    
    Write-Header "启动FlowCustomV1集群调试环境"
    
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker未运行，请先启动Docker"
        return
    }
    
    # 检查必要文件
    $requiredFiles = @(
        "docker-compose.debug.yml",
        "config/cluster/master-debug.json",
        "config/cluster/worker-debug.json",
        "config/mysql/init.sql"
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "缺少必要文件: $file"
            return
        }
    }
    
    if ($rebuild) {
        Write-Info "重新构建镜像..."
        docker-compose -f docker-compose.debug.yml build --no-cache
    }
    
    Write-Info "启动基础设施服务 (MySQL, Redis, NATS)..."
    docker-compose -f docker-compose.debug.yml up -d mysql redis nats
    
    Write-Info "等待基础设施服务就绪..."
    Start-Sleep -Seconds 30
    
    Write-Info "启动集群节点..."
    docker-compose -f docker-compose.debug.yml up -d flowcustom-master flowcustom-worker1 flowcustom-worker2
    
    Write-Info "等待集群节点就绪..."
    Start-Sleep -Seconds 20
    
    Write-Info "启动前端服务..."
    docker-compose -f docker-compose.debug.yml up -d flowcustom-frontend
    
    Write-Success "所有服务启动完成！"
    Write-Info "`n等待服务完全就绪..."
    Start-Sleep -Seconds 10
    
    Show-Status
}

# 主逻辑
try {
    if ($Status) {
        Show-Status
    }
    elseif ($Stop) {
        Stop-Services
    }
    elseif ($Clean) {
        Clean-Environment
    }
    elseif ($Logs) {
        Show-Logs
    }
    else {
        Start-Services -rebuild $Build
    }
}
catch {
    Write-Error "执行失败: $($_.Exception.Message)"
    exit 1
}
