using System;
using System.Collections.Generic;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// NodeExecutionContext对象池接口
    /// </summary>
    public interface INodeExecutionContextPool : IDisposable
    {
        /// <summary>
        /// 获取NodeExecutionContext实例
        /// </summary>
        /// <returns>NodeExecutionContext实例</returns>
        NodeExecutionContext Get();

        /// <summary>
        /// 返回NodeExecutionContext实例到池中
        /// </summary>
        /// <param name="context">要返回的实例</param>
        void Return(NodeExecutionContext context);

        /// <summary>
        /// 获取池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        NodeExecutionContextPoolStatistics GetStatistics();
    }

    /// <summary>
    /// NodeExecutionContext对象池统计信息
    /// </summary>
    public class NodeExecutionContextPoolStatistics
    {
        /// <summary>
        /// 池中可用对象数量
        /// </summary>
        public int AvailableCount { get; set; }

        /// <summary>
        /// 总创建对象数量
        /// </summary>
        public int TotalCreated { get; set; }

        /// <summary>
        /// 当前使用中的对象数量
        /// </summary>
        public int InUseCount { get; set; }

        /// <summary>
        /// 池的最大容量
        /// </summary>
        public int MaxSize { get; set; }

        /// <summary>
        /// 池命中率（从池中获取的比例）
        /// </summary>
        public double HitRate { get; set; }

        /// <summary>
        /// 重置成功率
        /// </summary>
        public double ResetSuccessRate { get; set; }
    }

    /// <summary>
    /// NodeExecutionContext专用对象池实现
    /// </summary>
    public class NodeExecutionContextPool : INodeExecutionContextPool
    {
        private readonly IObjectPool<NodeExecutionContext> _pool;
        private readonly ILogger<NodeExecutionContextPool>? _logger;
        private volatile int _totalGets;
        private volatile int _poolHits;
        private volatile int _resetAttempts;
        private volatile int _resetSuccesses;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="maxSize">池的最大容量</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="loggerFactory">日志工厂</param>
        public NodeExecutionContextPool(int maxSize = 50, ILogger<NodeExecutionContextPool>? logger = null, ILoggerFactory? loggerFactory = null)
        {
            _logger = logger;
            _pool = new ObjectPool<NodeExecutionContext>(
                objectFactory: CreateNodeExecutionContext,
                resetAction: ResetNodeExecutionContext,
                destroyAction: DestroyNodeExecutionContext,
                maxSize: maxSize,
                logger: loggerFactory?.CreateLogger<ObjectPool<NodeExecutionContext>>());
        }

        /// <summary>
        /// 获取NodeExecutionContext实例
        /// </summary>
        public NodeExecutionContext Get()
        {
            System.Threading.Interlocked.Increment(ref _totalGets);
            
            var context = _pool.Get();
            
            // 如果是从池中获取的（不是新创建的），增加命中计数
            if (_pool.Count < _pool.MaxSize)
            {
                System.Threading.Interlocked.Increment(ref _poolHits);
            }

            _logger?.LogDebug("获取NodeExecutionContext，ID: {NodeExecutionId}", context.NodeExecutionId);
            return context;
        }

        /// <summary>
        /// 返回NodeExecutionContext实例到池中
        /// </summary>
        public void Return(NodeExecutionContext context)
        {
            if (context == null)
            {
                _logger?.LogWarning("尝试返回null的NodeExecutionContext到池中");
                return;
            }

            _logger?.LogDebug("返回NodeExecutionContext到池中，ID: {NodeExecutionId}", context.NodeExecutionId);
            _pool.Return(context);
        }

        /// <summary>
        /// 获取池统计信息
        /// </summary>
        public NodeExecutionContextPoolStatistics GetStatistics()
        {
            var totalGets = _totalGets;
            var poolHits = _poolHits;
            var resetAttempts = _resetAttempts;
            var resetSuccesses = _resetSuccesses;

            return new NodeExecutionContextPoolStatistics
            {
                AvailableCount = _pool.Count,
                TotalCreated = _pool.TotalCreated,
                InUseCount = _pool.TotalCreated - _pool.Count,
                MaxSize = _pool.MaxSize,
                HitRate = totalGets > 0 ? (double)poolHits / totalGets : 0,
                ResetSuccessRate = resetAttempts > 0 ? (double)resetSuccesses / resetAttempts : 0
            };
        }

        /// <summary>
        /// 创建新的NodeExecutionContext实例
        /// </summary>
        private NodeExecutionContext CreateNodeExecutionContext()
        {
            var context = new NodeExecutionContext
            {
                NodeExecutionId = Guid.NewGuid(),
                WorkflowExecutionId = Guid.Empty,
                Node = null!,
                InputData = new Dictionary<string, object>(),
                Variables = new Dictionary<string, object>(),
                Context = new Dictionary<string, object>(),
                Logger = null!,
                ServiceProvider = null!,
                CancellationToken = default
            };

            _logger?.LogDebug("创建新的NodeExecutionContext，ID: {NodeExecutionId}", context.NodeExecutionId);
            return context;
        }

        /// <summary>
        /// 重置NodeExecutionContext实例
        /// </summary>
        private void ResetNodeExecutionContext(NodeExecutionContext context)
        {
            System.Threading.Interlocked.Increment(ref _resetAttempts);

            try
            {
                // 重置所有属性到初始状态
                context.NodeExecutionId = Guid.NewGuid();
                context.WorkflowExecutionId = Guid.Empty;
                context.Node = null!;
                
                // 清空字典但保留实例以减少GC压力
                context.InputData.Clear();
                context.Variables.Clear();
                context.Context.Clear();
                
                // 重置其他属性
                context.Logger = null!;
                context.ServiceProvider = null!;
                context.CancellationToken = default;

                System.Threading.Interlocked.Increment(ref _resetSuccesses);
                _logger?.LogDebug("成功重置NodeExecutionContext，新ID: {NodeExecutionId}", context.NodeExecutionId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "重置NodeExecutionContext时发生异常");
                throw; // 重新抛出异常，让对象池处理
            }
        }

        /// <summary>
        /// 销毁NodeExecutionContext实例
        /// </summary>
        private void DestroyNodeExecutionContext(NodeExecutionContext context)
        {
            try
            {
                // 清理资源
                context.InputData?.Clear();
                context.Variables?.Clear();
                context.Context?.Clear();

                _logger?.LogDebug("销毁NodeExecutionContext，ID: {NodeExecutionId}", context.NodeExecutionId);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "销毁NodeExecutionContext时发生异常");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _pool?.Dispose();
            
            var statistics = GetStatistics();
            _logger?.LogInformation(
                "NodeExecutionContext对象池已释放 - 总创建: {TotalCreated}, 命中率: {HitRate:P2}, 重置成功率: {ResetSuccessRate:P2}",
                statistics.TotalCreated, statistics.HitRate, statistics.ResetSuccessRate);
        }
    }
}
