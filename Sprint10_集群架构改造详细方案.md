# Sprint 10: FlowCustomV1 集群部署架构改造详细方案

## 📊 架构分析与设计决策

### n8n Queue Mode 核心特征分析
- **Main实例**: 处理UI、API、触发器，生成执行任务但不执行
- **Worker实例**: 专门从Redis队列获取任务并执行工作流
- **Webhook处理器**: 可选的独立HTTP请求处理层
- **Redis消息代理**: 任务队列和状态同步的核心
- **多Main支持**: 企业版高可用架构

### FlowCustomV1 现有架构优势
- ✅ 完整的NATS消息系统 (比Redis更强大)
- ✅ 模块化的WorkflowEngine设计
- ✅ 成熟的插件系统和节点执行器
- ✅ 对象池和资源管理系统
- ✅ 自适应并发控制和背压机制
- ✅ 完整的K8s部署配置基础

### 架构改造策略
**保留优势，借鉴n8n模式，实现三层分离：**
1. **Master层**: API + UI + 调度管理
2. **Webhook层**: 专门的HTTP请求处理
3. **Worker层**: 专门的工作流执行

## 🏗️ 详细架构设计

### 1. Master节点架构 (基于n8n Main模式)

```csharp
// 文件位置: src/FlowCustomV1.Cluster/Master/
public interface IClusterMasterManager
{
    // 集群生命周期管理
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    
    // 工作流调度分发 (不执行)
    Task<string> ScheduleWorkflowAsync(WorkflowExecutionRequest request);
    Task<bool> CancelWorkflowAsync(string executionId);
    
    // 集群状态管理
    Task<ClusterHealthStatus> GetClusterHealthAsync();
    Task<IEnumerable<WorkerNodeInfo>> GetWorkerNodesAsync();
    Task<IEnumerable<WebhookNodeInfo>> GetWebhookNodesAsync();
    
    // 触发器管理 (定时器、调度器)
    Task RegisterTriggerAsync(TriggerDefinition trigger);
    Task UnregisterTriggerAsync(string triggerId);
}

// 分布式工作流调度器
public interface IDistributedWorkflowScheduler
{
    Task<string> ScheduleAsync(WorkflowExecutionRequest request);
    Task<WorkflowExecutionStatus> GetStatusAsync(string executionId);
    Task<bool> CancelAsync(string executionId);
    Task RebalanceWorkloadsAsync();
}
```

**Master节点核心功能：**
- 🎯 **API服务**: 提供完整的RESTful API
- 🖥️ **UI托管**: 托管React前端应用
- ⏰ **触发器管理**: 管理定时器、调度器等触发器
- 📋 **任务调度**: 生成工作流执行任务并分发到队列
- 🔍 **集群监控**: 监控Worker和Webhook节点状态
- 🎛️ **配置管理**: 管理集群配置和节点注册

### 2. Worker节点架构 (专门执行)

```csharp
// 文件位置: src/FlowCustomV1.Cluster/Worker/
public interface IClusterWorkerManager
{
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    
    // 任务处理
    Task<bool> CanAcceptWorkload(WorkflowExecutionRequest request);
    Task ProcessTaskAsync(WorkflowExecutionTask task);
    
    // 节点管理
    Task RegisterWithMasterAsync(MasterNodeInfo masterInfo);
    Task<NodeMetrics> GetNodeMetricsAsync();
    Task ReportHealthAsync();
}

// 分布式工作流执行器
public interface IDistributedWorkflowExecutor
{
    Task<WorkflowExecutionResult> ExecuteAsync(
        DistributedWorkflowExecutionContext context,
        CancellationToken cancellationToken = default);
        
    Task<NodeExecutionResult> ExecuteNodeAsync(
        DistributedNodeExecutionContext context,
        CancellationToken cancellationToken = default);
}
```

**Worker节点核心功能：**
- 📥 **任务接收**: 从NATS队列接收工作流执行任务
- ⚙️ **工作流执行**: 复用现有WorkflowEngine执行逻辑
- 📊 **状态报告**: 实时报告执行状态和进度
- 🔧 **资源监控**: 监控CPU、内存等资源使用情况
- 🔄 **自动注册**: 自动向Master节点注册和心跳
- 📈 **动态扩缩容**: 支持K8s HPA自动扩缩容

### 3. Webhook节点架构 (专门处理HTTP)

```csharp
// 文件位置: src/FlowCustomV1.Cluster/Webhook/
public interface IWebhookProcessorManager
{
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    
    // HTTP请求处理
    Task<WebhookResponse> ProcessWebhookAsync(WebhookRequest request);
    Task<FormResponse> ProcessFormAsync(FormRequest request);
    
    // 负载管理
    Task<WebhookNodeMetrics> GetMetricsAsync();
    Task<bool> CanAcceptRequest();
}
```

**Webhook节点核心功能：**
- 🌐 **HTTP处理**: 专门处理Webhook、表单等HTTP请求
- 🔄 **请求转换**: 将HTTP请求转换为工作流执行任务
- ⚡ **高并发**: 支持大量并发HTTP请求处理
- 🎯 **负载分发**: 智能分发请求到不同Worker节点
- 📊 **性能监控**: 监控请求处理性能和吞吐量

### 4. NATS消息架构扩展

**基于现有NATS系统，扩展为完整的任务队列：**

```csharp
// NATS主题设计
public static class NatsTopics
{
    // 任务队列
    public const string WorkflowTasks = "workflow.tasks";
    public const string NodeTasks = "node.tasks";
    
    // 状态同步
    public const string ExecutionStatus = "execution.status";
    public const string NodeStatus = "node.status";
    
    // 集群管理
    public const string NodeRegistration = "cluster.node.register";
    public const string NodeHeartbeat = "cluster.node.heartbeat";
    public const string ClusterEvents = "cluster.events";
    
    // 监控指标
    public const string Metrics = "monitoring.metrics";
    public const string Alerts = "monitoring.alerts";
}

// 任务消息结构
public class WorkflowExecutionTask
{
    public string TaskId { get; set; }
    public string ExecutionId { get; set; }
    public WorkflowDefinition Workflow { get; set; }
    public Dictionary<string, object> InputData { get; set; }
    public string TriggeredBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public int Priority { get; set; }
    public string TargetWorkerType { get; set; } // 可选的Worker类型指定
}
```

### 5. Redis分布式状态管理

```csharp
// 文件位置: src/FlowCustomV1.Cluster/State/
public interface IDistributedStateManager
{
    // 状态管理
    Task<T?> GetStateAsync<T>(string key) where T : class;
    Task SetStateAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    Task RemoveStateAsync(string key);
    
    // 分布式锁
    Task<bool> TryLockAsync(string lockKey, TimeSpan expiry);
    Task ReleaseLockAsync(string lockKey);
    
    // 集群配置
    Task<ClusterConfiguration> GetClusterConfigAsync();
    Task UpdateClusterConfigAsync(ClusterConfiguration config);
}

// 工作流状态同步
public interface IWorkflowStateSynchronizer
{
    Task SynchronizeExecutionStateAsync(string executionId, WorkflowExecutionState state);
    Task<WorkflowExecutionState?> GetExecutionStateAsync(string executionId);
    Task NotifyStateChangeAsync(string executionId, WorkflowStateChangeEvent changeEvent);
}
```

## 🔄 消息流设计

### 工作流执行流程
```
1. 用户请求 → Master节点API
2. Master节点 → 生成WorkflowExecutionTask
3. Master节点 → 发布任务到NATS队列
4. Worker节点 → 从队列接收任务
5. Worker节点 → 执行工作流 (复用现有WorkflowEngine)
6. Worker节点 → 发布状态更新到NATS
7. Master节点 → 接收状态更新
8. Master节点 → 更新Redis状态
9. Master节点 → 返回结果给用户
```

### Webhook处理流程
```
1. 外部HTTP请求 → Webhook节点
2. Webhook节点 → 验证和预处理
3. Webhook节点 → 生成WorkflowExecutionTask
4. Webhook节点 → 发布任务到NATS队列
5. Worker节点 → 接收并执行任务
6. Worker节点 → 发布执行结果
7. Webhook节点 → 接收结果并返回HTTP响应
```

## 📦 Kubernetes部署架构

### Master节点部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flowcustom-master
spec:
  replicas: 3  # 高可用Master集群
  selector:
    matchLabels:
      app: flowcustom-master
  template:
    spec:
      containers:
      - name: master
        image: flowcustom/master:latest
        env:
        - name: NODE_ROLE
          value: "master"
        - name: CLUSTER_NAME
          value: "flowcustom-production"
        - name: NATS_URL
          value: "nats://nats-cluster:4222"
        - name: REDIS_URL
          value: "redis://redis-cluster:6379"
        ports:
        - containerPort: 5279  # API端口
        - containerPort: 5173  # UI端口
```

### Worker节点部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flowcustom-worker
spec:
  replicas: 10  # 可扩展的Worker节点
  selector:
    matchLabels:
      app: flowcustom-worker
  template:
    spec:
      containers:
      - name: worker
        image: flowcustom/worker:latest
        env:
        - name: NODE_ROLE
          value: "worker"
        - name: MASTER_ENDPOINTS
          value: "flowcustom-master-service:5279"
        - name: NATS_URL
          value: "nats://nats-cluster:4222"
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 2Gi
```

### 自动扩缩容配置
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: flowcustom-worker-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: flowcustom-worker
  minReplicas: 5
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 🎯 实施路线图

### Phase 1: 基础架构拆分 (1周)
- [ ] 创建Master、Worker、Webhook三个独立项目
- [ ] 重构现有WorkflowEngine为可复用组件
- [ ] 扩展NATS消息系统支持任务队列

### Phase 2: 核心功能实现 (2周)
- [ ] 实现Master节点的调度分发逻辑
- [ ] 实现Worker节点的任务接收和执行
- [ ] 实现Webhook节点的HTTP处理

### Phase 3: 状态管理和服务发现 (1周)
- [ ] 实现Redis分布式状态管理
- [ ] 实现基于K8s的服务发现
- [ ] 实现节点注册和健康检查

### Phase 4: 部署和测试 (1周)
- [ ] 完善K8s部署配置
- [ ] 集成测试和性能验证
- [ ] 监控和告警系统集成

## 📊 预期收益

### 性能提升
- **吞吐量**: 从当前100 req/s提升到1000+ req/s
- **响应时间**: API响应时间保持<100ms
- **并发能力**: 支持10K+ 并发工作流执行

### 可扩展性
- **水平扩展**: Worker节点支持动态扩缩容
- **高可用**: Master节点集群化部署
- **故障恢复**: 节点故障自动恢复<30秒

### 运维友好
- **监控完善**: 集群级别的监控和告警
- **部署简化**: 一键K8s部署
- **配置灵活**: 支持多环境配置管理

## 🔧 技术实现细节

### 现有组件复用策略

**保留并增强的组件：**
- ✅ `WorkflowEngine.cs` → 在Worker节点中复用
- ✅ `NatsService.cs` → 扩展为分布式任务队列
- ✅ `AdaptiveSemaphore` → Worker节点并发控制
- ✅ `BackpressureController` → Worker节点背压控制
- ✅ `ObjectPool系统` → Worker节点资源管理
- ✅ `插件系统` → 所有节点类型共享

**新增的核心组件：**
```csharp
// 集群协调器
public class ClusterCoordinator
{
    private readonly IDistributedStateManager _stateManager;
    private readonly INatsService _natsService;
    private readonly IServiceDiscovery _serviceDiscovery;

    public async Task<WorkerNodeInfo> SelectOptimalWorkerAsync(WorkflowExecutionRequest request)
    {
        // 基于负载、资源使用率、节点健康状态选择最优Worker
        var workers = await _serviceDiscovery.DiscoverServicesAsync("worker");
        var metrics = await GetWorkerMetricsAsync(workers);
        return SelectByLoadBalancingAlgorithm(metrics, request);
    }
}

// 任务分发器
public class TaskDistributor
{
    public async Task DistributeTaskAsync(WorkflowExecutionTask task)
    {
        // 智能任务分发：考虑Worker专长、负载、地理位置等
        var targetWorker = await _coordinator.SelectOptimalWorkerAsync(task.Request);
        var taskMessage = new NatsMessage<WorkflowExecutionTask>
        {
            Subject = $"workflow.tasks.{targetWorker.NodeId}",
            Data = task
        };
        await _natsService.PublishAsync(taskMessage);
    }
}
```

### 负载均衡算法设计

**多层负载均衡策略：**
1. **L4负载均衡**: K8s Service + Ingress
2. **L7应用负载均衡**: Master节点间的智能路由
3. **任务级负载均衡**: Worker节点选择算法

```csharp
public class WorkerLoadBalancer
{
    public WorkerNodeInfo SelectWorker(IEnumerable<WorkerNodeInfo> workers, WorkflowExecutionRequest request)
    {
        // 1. 过滤健康节点
        var healthyWorkers = workers.Where(w => w.HealthStatus == NodeHealthStatus.Healthy);

        // 2. 计算负载权重
        var weightedWorkers = healthyWorkers.Select(w => new
        {
            Worker = w,
            Weight = CalculateWeight(w, request)
        });

        // 3. 加权轮询选择
        return WeightedRoundRobinSelect(weightedWorkers);
    }

    private double CalculateWeight(WorkerNodeInfo worker, WorkflowExecutionRequest request)
    {
        var cpuWeight = (1.0 - worker.CpuUsage) * 0.4;
        var memoryWeight = (1.0 - worker.MemoryUsage) * 0.3;
        var queueWeight = (1.0 - worker.QueueLength / 100.0) * 0.2;
        var affinityWeight = CalculateAffinity(worker, request) * 0.1;

        return cpuWeight + memoryWeight + queueWeight + affinityWeight;
    }
}
```

### 故障恢复和高可用设计

**Master节点高可用：**
```csharp
public class MasterElection
{
    private readonly IDistributedStateManager _stateManager;

    public async Task<bool> TryBecomeLeaderAsync()
    {
        var lockKey = "cluster.master.leader";
        var lockExpiry = TimeSpan.FromMinutes(1);

        if (await _stateManager.TryLockAsync(lockKey, lockExpiry))
        {
            // 成为Leader，开始处理触发器和调度任务
            await StartLeaderTasks();
            return true;
        }
        return false;
    }
}
```

**Worker节点故障恢复：**
```csharp
public class WorkerFailureDetector
{
    public async Task MonitorWorkerHealthAsync()
    {
        var workers = await _serviceDiscovery.DiscoverServicesAsync("worker");

        foreach (var worker in workers)
        {
            if (!await IsWorkerHealthyAsync(worker))
            {
                await HandleWorkerFailureAsync(worker);
            }
        }
    }

    private async Task HandleWorkerFailureAsync(WorkerNodeInfo worker)
    {
        // 1. 标记节点为不健康
        await _stateManager.SetStateAsync($"worker.{worker.NodeId}.health", "unhealthy");

        // 2. 重新分发该节点的任务
        var pendingTasks = await GetPendingTasksAsync(worker.NodeId);
        foreach (var task in pendingTasks)
        {
            await _taskDistributor.RedistributeTaskAsync(task);
        }

        // 3. 通知集群管理器
        await _natsService.PublishAsync(new WorkerFailureEvent
        {
            WorkerId = worker.NodeId,
            FailureTime = DateTime.UtcNow,
            PendingTaskCount = pendingTasks.Count()
        });
    }
}
```

## 🚀 部署和运维

### Docker镜像构建策略

**多阶段构建优化：**
```dockerfile
# Master节点镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS master-runtime
WORKDIR /app
COPY --from=build /app/master ./
COPY --from=build /app/frontend ./wwwroot
ENTRYPOINT ["dotnet", "FlowCustomV1.Master.dll"]

# Worker节点镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS worker-runtime
WORKDIR /app
COPY --from=build /app/worker ./
ENTRYPOINT ["dotnet", "FlowCustomV1.Worker.dll"]

# Webhook节点镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS webhook-runtime
WORKDIR /app
COPY --from=build /app/webhook ./
ENTRYPOINT ["dotnet", "FlowCustomV1.Webhook.dll"]
```

### 监控和可观测性

**Prometheus指标设计：**
```csharp
public class ClusterMetrics
{
    private static readonly Counter TasksProcessed = Metrics
        .CreateCounter("flowcustom_tasks_processed_total", "Total processed tasks", new[] { "node_type", "status" });

    private static readonly Histogram TaskDuration = Metrics
        .CreateHistogram("flowcustom_task_duration_seconds", "Task processing duration", new[] { "node_type" });

    private static readonly Gauge ActiveWorkers = Metrics
        .CreateGauge("flowcustom_active_workers", "Number of active worker nodes");

    private static readonly Gauge QueueLength = Metrics
        .CreateGauge("flowcustom_queue_length", "Current queue length", new[] { "queue_type" });
}
```

**日志聚合策略：**
```csharp
public class DistributedLogging
{
    public async Task LogWorkflowExecutionAsync(WorkflowExecutionContext context, LogLevel level, string message)
    {
        var logEntry = new DistributedLogEntry
        {
            Timestamp = DateTime.UtcNow,
            Level = level,
            Message = message,
            ExecutionId = context.Execution.Id,
            NodeId = Environment.MachineName,
            NodeType = GetNodeType(),
            TraceId = Activity.Current?.TraceId.ToString()
        };

        // 发送到集中式日志系统
        await _natsService.PublishAsync($"logs.{level.ToString().ToLower()}", logEntry);
    }
}
```

## 📈 性能优化策略

### 缓存策略
```csharp
public class DistributedCacheManager
{
    // 工作流定义缓存
    public async Task<WorkflowDefinition?> GetWorkflowAsync(Guid workflowId)
    {
        var cacheKey = $"workflow.{workflowId}";
        var cached = await _redis.GetAsync<WorkflowDefinition>(cacheKey);

        if (cached == null)
        {
            cached = await _repository.GetByIdAsync(workflowId);
            if (cached != null)
            {
                await _redis.SetAsync(cacheKey, cached, TimeSpan.FromMinutes(10));
            }
        }

        return cached;
    }
}
```

### 连接池优化
```csharp
public class ConnectionPoolManager
{
    private readonly ObjectPool<NatsConnection> _natsPool;
    private readonly ObjectPool<IDbConnection> _dbPool;

    public ConnectionPoolManager()
    {
        _natsPool = new DefaultObjectPool<NatsConnection>(
            new NatsConnectionPooledObjectPolicy(),
            maximumRetained: 100);

        _dbPool = new DefaultObjectPool<IDbConnection>(
            new DbConnectionPooledObjectPolicy(),
            maximumRetained: 50);
    }
}
```

这个方案充分借鉴了n8n的成熟架构模式，同时保留了FlowCustomV1的技术优势，实现了真正的分布式集群架构。通过三层节点分离、NATS任务队列、Redis状态管理和K8s自动化部署，系统将具备企业级的可扩展性、高可用性和运维友好性。
