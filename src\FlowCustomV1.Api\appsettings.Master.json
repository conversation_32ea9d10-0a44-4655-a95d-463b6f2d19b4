{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "FlowCustomV1.Api": "Debug",
      "FlowCustomV1.Engine": "Debug",
      "FlowCustomV1.Core": "Information"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    }
  },
  "AllowedHosts": "*",
  
  // 集群配置
  "Cluster": {
    "EnableClusterMode": true,
    "NodeMode": "Master",
    "NodeId": "master-001",
    "NodeDisplayName": "FlowCustomV1 Master节点",
    "ClusterName": "flowcustom-cluster",
    
    "Master": {
      "ApiPort": 5279,
      "EnableSwagger": true,
      "EnableCors": true,
      "CorsOrigins": ["http://localhost:5173", "http://localhost:3000"],
      "TaskScheduling": {
        "LoadBalancingStrategy": "weighted_round_robin",
        "TaskTimeout": "00:30:00",
        "MaxRetryAttempts": 3,
        "RetryDelay": "00:00:05"
      }
    },
    
    "Communication": {
      "ConnectionString": "nats://localhost:4222",
      "ConnectionName": "FlowCustomV1-Master",
      "HeartbeatInterval": "00:00:30",
      "ConnectTimeout": "00:00:10",
      "MaxReconnectAttempts": -1,
      "ReconnectWait": "00:00:02"
    }
  },

  // 数据库配置
  "DatabaseProvider": "SQLite",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=flowcustom.db",
    "MySQL": "Server=localhost;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;",
    "PostgreSQL": "Host=localhost;Database=flowcustom_debug;Username=flowcustom;Password=flowcustom123;",
    "NATS": "nats://localhost:4222",
    "Redis": "localhost:6379"
  },

  // 缓存配置
  "Cache": {
    "Provider": "Redis",
    "DefaultExpiration": "01:00:00"
  },

  // NATS配置
  "NATS": {
    "Servers": ["nats://localhost:4222"],
    "ConnectionName": "FlowCustomV1-Master",
    "MaxReconnectAttempts": -1,
    "ReconnectWait": "00:00:02",
    "ConnectTimeout": "00:00:10",
    "EnableJetStream": true,
    "JetStream": {
      "Domain": "flowcustom",
      "Streams": [
        {
          "Name": "WORKFLOW_TASKS",
          "Subjects": ["workflow.tasks"],
          "Storage": "File",
          "Retention": "WorkQueue",
          "MaxAge": "24h",
          "MaxBytes": **********,
          "MaxMsgs": 1000000
        },
        {
          "Name": "WORKFLOW_STATUS",
          "Subjects": ["workflow.status.*"],
          "Storage": "File",
          "Retention": "Limits",
          "MaxAge": "7d",
          "MaxBytes": 536870912,
          "MaxMsgs": 500000
        },
        {
          "Name": "CLUSTER_EVENTS",
          "Subjects": ["cluster.*"],
          "Storage": "Memory",
          "Retention": "Limits",
          "MaxAge": "1h",
          "MaxBytes": 104857600,
          "MaxMsgs": 100000
        }
      ]
    }
  },

  // 性能监控配置
  "PerformanceMonitoring": {
    "EnableMetrics": true,
    "MetricsInterval": "00:00:30",
    "EnableTracing": true,
    "SamplingRate": 0.1
  },

  // 插件配置
  "PluginHost": {
    "EnableOptimization": true,
    "CacheCompiledPlugins": true,
    "MaxConcurrentCompilations": 4
  },

  // 工作流引擎配置
  "WorkflowEngine": {
    "MaxConcurrentExecutions": 10,
    "DefaultTimeout": "00:05:00",
    "EnableDetailedLogging": true,
    "EnablePerformanceTracking": true
  }
}
