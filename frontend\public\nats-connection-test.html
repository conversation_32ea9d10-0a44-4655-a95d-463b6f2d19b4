<!DOCTYPE html>
<html>
<head>
    <title>NATS Connection Test</title>
    <script type="module">
        // 使用与前端相同的nats.ws版本进行测试
        import { connect, StringCodec } from 'https://cdn.skypack.dev/nats.ws@1.28.0';
        
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        async function testNatsConnection() {
            try {
                addLog('🔌 开始连接NATS WebSocket服务器: ws://localhost:8081');
                status.textContent = '正在连接...';
                status.style.color = 'orange';
                
                // 使用与前端相同的连接配置
                const nc = await connect({
                    servers: ['ws://localhost:8081'],
                    name: 'NATS-Connection-Test',
                    // 基础连接选项
                    reconnect: true,
                    maxReconnectAttempts: 5,
                    reconnectTimeWait: 2000,
                    // WebSocket特定选项
                    timeout: 10000, // 10秒连接超时
                    pingInterval: 30000, // 30秒心跳间隔
                    maxPingOut: 3, // 最大未响应ping数
                    // 调试选项
                    verbose: true,
                    debug: true,
                });
                
                addLog('✅ NATS WebSocket连接成功！');
                status.textContent = '连接成功';
                status.style.color = 'green';
                
                // 测试发布/订阅
                const sc = StringCodec();
                
                // 订阅测试主题
                const sub = nc.subscribe('test.frontend');
                (async () => {
                    for await (const m of sub) {
                        addLog('📨 收到消息: ' + sc.decode(m.data));
                    }
                })();
                
                // 发布测试消息
                setTimeout(() => {
                    addLog('📤 发送测试消息...');
                    nc.publish('test.frontend', sc.encode('Hello from Frontend Test!'));
                }, 1000);
                
                // 监听连接关闭
                nc.closed().then(() => {
                    addLog('🔌 NATS连接已关闭');
                    status.textContent = '连接关闭';
                    status.style.color = 'red';
                });
                
            } catch (error) {
                addLog('❌ NATS连接失败: ' + error.message);
                addLog('错误详情: ' + JSON.stringify(error, null, 2));
                status.textContent = '连接失败';
                status.style.color = 'red';
                console.error('NATS连接错误:', error);
            }
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testNatsConnection);
    </script>
</head>
<body>
    <h1>NATS WebSocket Connection Test</h1>
    <div>状态: <span id="status">准备中...</span></div>
    <div id="log" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;"></div>
</body>
</html>
