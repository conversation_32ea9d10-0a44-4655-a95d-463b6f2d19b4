using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 对象池扩展方法
    /// </summary>
    public static class ObjectPoolExtensions
    {
        /// <summary>
        /// 添加对象池服务到依赖注入容器
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddObjectPooling(this IServiceCollection services)
        {
            // 注册对象池管理器为单例
            services.AddSingleton<IObjectPoolManager>(provider =>
            {
                var logger = provider.GetService<ILogger<ObjectPoolManager>>();
                var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
                return new ObjectPoolManager(logger, loggerFactory);
            });

            return services;
        }

        /// <summary>
        /// 添加特定类型的对象池
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="services">服务集合</param>
        /// <param name="factory">对象创建工厂</param>
        /// <param name="resetAction">对象重置操作</param>
        /// <param name="destroyAction">对象销毁操作</param>
        /// <param name="maxSize">池的最大容量</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddObjectPool<T>(
            this IServiceCollection services,
            Func<IServiceProvider, T> factory,
            Action<T>? resetAction = null,
            Action<T>? destroyAction = null,
            int maxSize = 100) where T : class
        {
            services.AddSingleton<IObjectPool<T>>(provider =>
            {
                var logger = provider.GetService<ILogger<ObjectPool<T>>>();
                return new ObjectPool<T>(
                    () => factory(provider),
                    resetAction,
                    destroyAction,
                    maxSize,
                    logger);
            });

            return services;
        }

        /// <summary>
        /// 使用对象池执行操作
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="pool">对象池</param>
        /// <param name="action">要执行的操作</param>
        /// <returns>操作结果</returns>
        public static TResult Use<T, TResult>(this IObjectPool<T> pool, Func<T, TResult> action) where T : class
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var item = pool.Get();
            try
            {
                return action(item);
            }
            finally
            {
                pool.Return(item);
            }
        }

        /// <summary>
        /// 使用对象池执行异步操作
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="pool">对象池</param>
        /// <param name="action">要执行的异步操作</param>
        /// <returns>操作结果</returns>
        public static async System.Threading.Tasks.Task<TResult> UseAsync<T, TResult>(
            this IObjectPool<T> pool, 
            Func<T, System.Threading.Tasks.Task<TResult>> action) where T : class
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var item = pool.Get();
            try
            {
                return await action(item);
            }
            finally
            {
                pool.Return(item);
            }
        }

        /// <summary>
        /// 使用对象池执行操作（无返回值）
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="pool">对象池</param>
        /// <param name="action">要执行的操作</param>
        public static void Use<T>(this IObjectPool<T> pool, Action<T> action) where T : class
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var item = pool.Get();
            try
            {
                action(item);
            }
            finally
            {
                pool.Return(item);
            }
        }

        /// <summary>
        /// 使用对象池执行异步操作（无返回值）
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="pool">对象池</param>
        /// <param name="action">要执行的异步操作</param>
        public static async System.Threading.Tasks.Task UseAsync<T>(
            this IObjectPool<T> pool, 
            Func<T, System.Threading.Tasks.Task> action) where T : class
        {
            if (pool == null) throw new ArgumentNullException(nameof(pool));
            if (action == null) throw new ArgumentNullException(nameof(action));

            var item = pool.Get();
            try
            {
                await action(item);
            }
            finally
            {
                pool.Return(item);
            }
        }
    }
}
