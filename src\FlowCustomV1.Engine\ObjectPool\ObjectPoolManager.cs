using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 对象池管理器接口
    /// </summary>
    public interface IObjectPoolManager : IDisposable
    {
        /// <summary>
        /// 获取或创建指定类型的对象池
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="factory">对象创建工厂</param>
        /// <param name="resetAction">对象重置操作</param>
        /// <param name="destroyAction">对象销毁操作</param>
        /// <param name="maxSize">池的最大容量</param>
        /// <returns>对象池实例</returns>
        IObjectPool<T> GetOrCreatePool<T>(
            Func<T> factory,
            Action<T>? resetAction = null,
            Action<T>? destroyAction = null,
            int maxSize = 100) where T : class;

        /// <summary>
        /// 获取对象池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        ObjectPoolStatistics GetStatistics();

        /// <summary>
        /// 清空所有对象池
        /// </summary>
        void ClearAllPools();
    }

    /// <summary>
    /// 对象池统计信息
    /// </summary>
    public class ObjectPoolStatistics
    {
        /// <summary>
        /// 对象池数量
        /// </summary>
        public int PoolCount { get; set; }

        /// <summary>
        /// 总对象数量（池中的）
        /// </summary>
        public int TotalPooledObjects { get; set; }

        /// <summary>
        /// 总创建对象数量
        /// </summary>
        public int TotalCreatedObjects { get; set; }

        /// <summary>
        /// 各类型对象池详情
        /// </summary>
        public Dictionary<string, PoolTypeStatistics> PoolDetails { get; set; } = new();
    }

    /// <summary>
    /// 单个类型对象池统计信息
    /// </summary>
    public class PoolTypeStatistics
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 池中对象数量
        /// </summary>
        public int PooledCount { get; set; }

        /// <summary>
        /// 最大容量
        /// </summary>
        public int MaxSize { get; set; }

        /// <summary>
        /// 总创建数量
        /// </summary>
        public int TotalCreated { get; set; }

        /// <summary>
        /// 池利用率
        /// </summary>
        public double UtilizationRate => MaxSize > 0 ? (double)PooledCount / MaxSize : 0;
    }

    /// <summary>
    /// 对象池管理器实现
    /// </summary>
    public class ObjectPoolManager : IObjectPoolManager
    {
        private readonly ConcurrentDictionary<Type, object> _pools = new();
        private readonly ILogger<ObjectPoolManager>? _logger;
        private readonly ILoggerFactory? _loggerFactory;
        private volatile bool _disposed;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="loggerFactory">日志工厂</param>
        public ObjectPoolManager(ILogger<ObjectPoolManager>? logger = null, ILoggerFactory? loggerFactory = null)
        {
            _logger = logger;
            _loggerFactory = loggerFactory;
        }

        /// <summary>
        /// 获取或创建指定类型的对象池
        /// </summary>
        public IObjectPool<T> GetOrCreatePool<T>(
            Func<T> factory,
            Action<T>? resetAction = null,
            Action<T>? destroyAction = null,
            int maxSize = 100) where T : class
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ObjectPoolManager));

            var type = typeof(T);
            var pool = _pools.GetOrAdd(type, _ =>
            {
                var newPool = new ObjectPool<T>(factory, resetAction, destroyAction, maxSize,
                    _loggerFactory?.CreateLogger<ObjectPool<T>>());
                _logger?.LogInformation("为类型 {TypeName} 创建了新的对象池，最大容量: {MaxSize}", type.Name, maxSize);
                return newPool;
            });

            return (IObjectPool<T>)pool;
        }

        /// <summary>
        /// 获取对象池统计信息
        /// </summary>
        public ObjectPoolStatistics GetStatistics()
        {
            if (_disposed)
                return new ObjectPoolStatistics();

            var statistics = new ObjectPoolStatistics
            {
                PoolCount = _pools.Count
            };

            foreach (var kvp in _pools)
            {
                var type = kvp.Key;
                var pool = kvp.Value;

                // 使用反射获取池的统计信息
                var countProperty = pool.GetType().GetProperty("Count");
                var maxSizeProperty = pool.GetType().GetProperty("MaxSize");
                var totalCreatedProperty = pool.GetType().GetProperty("TotalCreated");

                if (countProperty != null && maxSizeProperty != null && totalCreatedProperty != null)
                {
                    var count = (int)(countProperty.GetValue(pool) ?? 0);
                    var maxSize = (int)(maxSizeProperty.GetValue(pool) ?? 0);
                    var totalCreated = (int)(totalCreatedProperty.GetValue(pool) ?? 0);

                    statistics.TotalPooledObjects += count;
                    statistics.TotalCreatedObjects += totalCreated;

                    statistics.PoolDetails[type.Name] = new PoolTypeStatistics
                    {
                        TypeName = type.Name,
                        PooledCount = count,
                        MaxSize = maxSize,
                        TotalCreated = totalCreated
                    };
                }
            }

            return statistics;
        }

        /// <summary>
        /// 清空所有对象池
        /// </summary>
        public void ClearAllPools()
        {
            if (_disposed)
                return;

            foreach (var pool in _pools.Values)
            {
                if (pool is IDisposable disposable)
                {
                    try
                    {
                        // 调用Clear方法
                        var clearMethod = pool.GetType().GetMethod("Clear");
                        clearMethod?.Invoke(pool, null);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "清空对象池时发生异常");
                    }
                }
            }

            _logger?.LogInformation("已清空所有对象池");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            foreach (var pool in _pools.Values)
            {
                if (pool is IDisposable disposable)
                {
                    try
                    {
                        disposable.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "释放对象池时发生异常");
                    }
                }
            }

            _pools.Clear();
            _logger?.LogInformation("对象池管理器已释放");
        }
    }
}
