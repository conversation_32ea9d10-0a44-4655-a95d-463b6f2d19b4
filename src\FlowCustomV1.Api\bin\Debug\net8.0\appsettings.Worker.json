{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "FlowCustomV1.Api": "Debug",
      "FlowCustomV1.Engine": "Debug",
      "FlowCustomV1.Core": "Information"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    }
  },
  "AllowedHosts": "*",
  
  // 集群配置
  "Cluster": {
    "EnableClusterMode": true,
    "NodeMode": "Worker",
    "NodeId": "worker-001",
    "NodeDisplayName": "FlowCustomV1 Worker节点",
    "ClusterName": "flowcustom-cluster",
    
    "Worker": {
      "MaxConcurrentExecutions": 4,
      "WorkerType": "general",
      "SupportedNodeTypes": ["script", "http", "timer", "webhook", "condition", "loop"],
      "HealthCheckInterval": "00:00:30",
      "TaskTimeout": "00:05:00"
    },
    
    "Communication": {
      "ConnectionString": "nats://localhost:4222",
      "ConnectionName": "FlowCustomV1-Worker-001",
      "HeartbeatInterval": "00:00:30",
      "ConnectTimeout": "00:00:10",
      "MaxReconnectAttempts": -1,
      "ReconnectWait": "00:00:02"
    }
  },

  // 数据库配置
  "DatabaseProvider": "SQLite",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=flowcustom.db",
    "MySQL": "Server=localhost;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;",
    "PostgreSQL": "Host=localhost;Database=flowcustom_debug;Username=flowcustom;Password=flowcustom123;",
    "NATS": "nats://localhost:4222",
    "Redis": "localhost:6379"
  },

  // 缓存配置
  "Cache": {
    "Provider": "Redis",
    "DefaultExpiration": "01:00:00"
  },

  // NATS配置
  "NATS": {
    "Servers": ["nats://localhost:4222"],
    "ConnectionName": "FlowCustomV1-Worker-001",
    "MaxReconnectAttempts": -1,
    "ReconnectWait": "00:00:02",
    "ConnectTimeout": "00:00:10",
    "EnableJetStream": true,
    "JetStream": {
      "Domain": "flowcustom"
    }
  },

  // 性能监控配置
  "PerformanceMonitoring": {
    "EnableMetrics": true,
    "MetricsInterval": "00:00:30",
    "EnableTracing": true,
    "SamplingRate": 0.1
  },

  // 插件配置
  "PluginHost": {
    "EnableOptimization": true,
    "CacheCompiledPlugins": true,
    "MaxConcurrentCompilations": 2
  },

  // 工作流引擎配置
  "WorkflowEngine": {
    "MaxConcurrentExecutions": 4,
    "DefaultTimeout": "00:05:00",
    "EnableDetailedLogging": true,
    "EnablePerformanceTracking": true
  }
}
