<!DOCTYPE html>
<html>
<head>
    <title>NATS.ws Connection Test</title>
    <script type="module">
        // 使用ES模块导入nats.ws
        import { connect, StringCodec } from 'https://cdn.skypack.dev/nats.ws@1.30.3';
        
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        async function testNatsConnection() {
            try {
                addLog('🔌 开始连接NATS WebSocket服务器: ws://localhost:8081');
                status.textContent = '正在连接...';
                status.style.color = 'orange';
                
                const nc = await connect({
                    servers: ['ws://localhost:8081'],
                    name: 'NATS-Test-Client',
                    verbose: true,
                    reconnect: true,
                    maxReconnectAttempts: 3,
                    reconnectTimeWait: 1000,
                });
                
                addLog('✅ NATS WebSocket连接成功！');
                status.textContent = '连接成功';
                status.style.color = 'green';
                
                // 测试发布/订阅
                const sc = StringCodec();
                
                // 订阅测试主题
                const sub = nc.subscribe('test.subject');
                (async () => {
                    for await (const m of sub) {
                        addLog('📨 收到消息: ' + sc.decode(m.data));
                    }
                })();
                
                // 发布测试消息
                setTimeout(() => {
                    addLog('📤 发送测试消息...');
                    nc.publish('test.subject', sc.encode('Hello from NATS WebSocket!'));
                }, 1000);
                
                // 监听连接关闭
                nc.closed().then(() => {
                    addLog('🔌 NATS连接已关闭');
                    status.textContent = '连接关闭';
                    status.style.color = 'red';
                });
                
            } catch (error) {
                addLog('❌ NATS连接失败: ' + error.message);
                status.textContent = '连接失败';
                status.style.color = 'red';
                console.error('NATS连接错误:', error);
            }
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testNatsConnection);
    </script>
</head>
<body>
    <h1>NATS.ws WebSocket Connection Test</h1>
    <div>状态: <span id="status">准备中...</span></div>
    <div id="log" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap;"></div>
</body>
</html>
