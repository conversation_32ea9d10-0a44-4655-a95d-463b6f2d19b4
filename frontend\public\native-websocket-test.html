<!DOCTYPE html>
<html>
<head>
    <title>Native WebSocket NATS Test</title>
</head>
<body>
    <h1>Native WebSocket NATS Connection Test</h1>
    <div>状态: <span id="status">准备中...</span></div>
    <div id="log" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;"></div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        // 测试原生WebSocket连接到NATS
        function testNativeWebSocketConnection() {
            addLog('🔌 开始测试原生WebSocket连接到 ws://localhost:8081');
            status.textContent = '正在连接...';
            status.style.color = 'orange';
            
            // 创建WebSocket连接，指定NATS WebSocket子协议
            const ws = new WebSocket('ws://localhost:8081', ['nats']);
            
            ws.onopen = function(event) {
                addLog('✅ WebSocket连接成功！');
                addLog('协议: ' + ws.protocol);
                addLog('就绪状态: ' + ws.readyState);
                status.textContent = '连接成功';
                status.style.color = 'green';
                
                // 发送NATS CONNECT消息
                const connectMsg = JSON.stringify({
                    verbose: false,
                    pedantic: false,
                    tls_required: false,
                    name: "native-websocket-test",
                    lang: "javascript",
                    version: "1.0.0"
                });
                
                const natsConnect = `CONNECT ${connectMsg}\r\n`;
                addLog('📤 发送CONNECT消息: ' + natsConnect.trim());
                ws.send(natsConnect);
                
                // 发送PING
                setTimeout(() => {
                    addLog('📤 发送PING');
                    ws.send('PING\r\n');
                }, 1000);
            };
            
            ws.onerror = function(error) {
                addLog('❌ WebSocket连接错误: ' + error);
                addLog('错误类型: ' + error.type);
                status.textContent = '连接失败';
                status.style.color = 'red';
            };
            
            ws.onclose = function(event) {
                addLog(`🔌 WebSocket连接关闭: 代码=${event.code}, 原因="${event.reason}", 干净关闭=${event.wasClean}`);
                status.textContent = '连接关闭';
                status.style.color = 'orange';
            };
            
            ws.onmessage = function(event) {
                addLog('📨 收到NATS消息: ' + event.data);
                
                // 如果收到PONG，发送订阅消息
                if (event.data.trim() === 'PONG') {
                    addLog('📤 发送订阅消息');
                    ws.send('SUB test.native 1\r\n');
                    
                    // 发送发布消息
                    setTimeout(() => {
                        const message = 'Hello from native WebSocket!';
                        addLog('📤 发送发布消息');
                        ws.send(`PUB test.native ${message.length}\r\n${message}\r\n`);
                    }, 500);
                }
            };
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testNativeWebSocketConnection);
    </script>
</body>
</html>
