# FlowCustomV1 集群架构集成指南

## 概述

FlowCustomV1 v0.9.8 引入了集群架构，将原有的单体应用拆分为Master节点和Worker节点，提供更好的可扩展性和性能。

## 架构对比

### 原有架构 (v0.9.7及之前)
```
前端 (React 18) ←→ 后台API (ASP.NET Core) ←→ SQLite数据库
```

### 新集群架构 (v0.9.8+)
```
前端 (React 18) ←→ Master节点 (API Gateway) ←→ Worker节点集群
                        ↓                        ↑
                   NATS消息队列 ←→ MySQL/Redis ←→ 工作流引擎
```

## 集成方案

### 1. 前端集成 (无需修改)

前端应用继续使用相同的API端点，Master节点提供完全兼容的REST API：

```javascript
// 原有的API调用方式保持不变
const response = await fetch('http://localhost:5279/api/workflows');
const workflows = await response.json();

// 工作流执行API
const executeResponse = await fetch('http://localhost:5279/api/workflows/execute', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ workflowId: 'workflow-001', inputData: {} })
});
```

### 2. API端点映射

| 功能 | 原有端点 | 集群端点 | 处理节点 |
|------|----------|----------|----------|
| 工作流管理 | `/api/workflows` | `/api/workflows` | Master |
| 工作流执行 | `/api/workflows/execute` | `/api/workflows/execute` | Master → Worker |
| 节点管理 | `/api/nodes` | `/api/nodes` | Master |
| 集群状态 | - | `/api/cluster/status` | Master |
| 健康检查 | `/health` | `/health` | Master/Worker |

### 3. 实时通信集成

#### 方案A: Server-Sent Events (推荐)
```javascript
// 前端订阅工作流状态更新
const eventSource = new EventSource('http://localhost:5279/api/workflows/status-stream');
eventSource.onmessage = function(event) {
  const statusUpdate = JSON.parse(event.data);
  updateWorkflowStatus(statusUpdate);
};
```

#### 方案B: WebSocket代理
```javascript
// Master节点提供WebSocket代理到NATS
const ws = new WebSocket('ws://localhost:5279/ws/workflow-status');
ws.onmessage = function(event) {
  const statusUpdate = JSON.parse(event.data);
  updateWorkflowStatus(statusUpdate);
};
```

### 4. 配置集成

#### 前端环境配置
```javascript
// vite.config.js 或 .env
export default {
  define: {
    __API_BASE_URL__: 'http://localhost:5279',
    __WS_URL__: 'ws://localhost:5279/ws',
    __CLUSTER_MODE__: true
  }
}
```

#### Master节点配置
```json
{
  "NodeConfiguration": {
    "EnableMasterRole": true,
    "EnableWorkerRole": false,
    "Master": {
      "ApiPort": 5279,
      "EnableSwagger": true,
      "EnableCors": true,
      "CorsOrigins": ["http://localhost:5173"]
    }
  }
}
```

## 部署配置

### 开发环境
```bash
# 1. 启动基础设施
docker-compose -f docker-compose.debug.yml up -d mysql redis nats

# 2. 启动Worker容器
docker-compose -f docker-compose.debug.yml up -d flowcustom-worker1

# 3. 启动Master节点 (本地)
.\start-master.cmd

# 4. 启动前端 (本地)
cd frontend && npm run dev
```

### 生产环境
```bash
# 使用Docker Compose启动完整集群
docker-compose -f docker-compose.yml up -d
```

## 迁移步骤

### 1. 数据库迁移
```sql
-- 从SQLite迁移到MySQL
-- 使用现有的迁移脚本或数据导出工具
```

### 2. 配置迁移
```bash
# 将原有的appsettings.json拆分为Master和Worker配置
cp appsettings.json config/cluster/master.json
cp appsettings.json config/cluster/worker.json
```

### 3. 代码适配 (如有自定义扩展)
```csharp
// 原有的服务注册
services.AddScoped<IWorkflowService, WorkflowService>();

// 集群模式下的服务注册
services.AddFlowCustomClusterNodeWithRoles(configuration);
```

## 监控和调试

### 集群状态监控
- Master API: http://localhost:5279/api/cluster/status
- NATS监控: http://localhost:8222
- Swagger UI: http://localhost:5279/swagger

### 日志查看
```bash
# Master节点日志 (控制台输出)
# Worker容器日志
docker logs flowcustom-worker1-debug -f

# NATS日志
docker logs flowcustom-nats-debug -f
```

## 性能优化

### 1. 负载均衡
- 多个Worker节点自动负载均衡
- 支持按节点类型和资源使用率分发任务

### 2. 缓存策略
- Redis缓存工作流定义和执行状态
- 减少数据库查询压力

### 3. 消息队列优化
- NATS JetStream提供持久化和重试机制
- 支持消息去重和顺序保证

## 故障排除

### 常见问题
1. **Worker节点无法连接NATS**
   - 检查网络配置和防火墙设置
   - 确认NATS服务正常运行

2. **前端API调用失败**
   - 检查Master节点是否启动
   - 确认CORS配置正确

3. **工作流执行超时**
   - 检查Worker节点资源使用情况
   - 调整任务超时配置

### 调试命令
```bash
# 检查集群状态
curl http://localhost:5279/api/cluster/status

# 检查NATS连接
curl http://localhost:8222/connz

# 检查容器健康状态
docker ps --format "table {{.Names}}\t{{.Status}}"
```
