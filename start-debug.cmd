@echo off
echo 🚀 启动FlowCustomV1集群调试环境
echo ================================

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker
    pause
    exit /b 1
)

echo ℹ️  启动基础设施服务...
docker-compose -f docker-compose.debug.yml up -d mysql redis nats

echo ℹ️  等待基础设施服务就绪...
timeout /t 30 /nobreak >nul

echo ℹ️  启动集群节点...
docker-compose -f docker-compose.debug.yml up -d flowcustom-master flowcustom-worker1 flowcustom-worker2

echo ℹ️  等待集群节点就绪...
timeout /t 20 /nobreak >nul

echo ℹ️  启动前端服务...
docker-compose -f docker-compose.debug.yml up -d flowcustom-frontend

echo ✅ 所有服务启动完成！
echo.
echo 📋 访问地址:
echo • Master API: http://localhost:5279
echo • Swagger UI: http://localhost:5279/swagger
echo • 前端界面: http://localhost:5173
echo • NATS监控: http://localhost:8222
echo.
echo 📊 查看状态: docker-compose -f docker-compose.debug.yml ps
echo 📋 查看日志: docker-compose -f docker-compose.debug.yml logs -f
echo 🛑 停止服务: docker-compose -f docker-compose.debug.yml down
echo.
pause
