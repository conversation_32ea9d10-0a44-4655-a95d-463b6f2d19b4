{"version": 3, "file": "IStatefulReconnectOptions.js", "sourceRoot": "", "sources": ["../../src/IStatefulReconnectOptions.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n/** Options provided to the 'withStatefulReconnect' method on {@link @microsoft/signalr.HubConnectionBuilder} to configure options for Stateful Reconnect. */\r\nexport interface IStatefulReconnectOptions {\r\n    /** Amount of bytes we'll buffer when using Stateful Reconnect until applying backpressure to sends from the client. */\r\n    bufferSize: number;\r\n}"]}