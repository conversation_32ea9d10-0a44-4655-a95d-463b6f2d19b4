using FlowCustomV1.Api.Configuration;
using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// 集群服务接口
/// </summary>
public interface IClusterService
{
    /// <summary>
    /// 当前节点模式
    /// </summary>
    NodeMode NodeMode { get; }

    /// <summary>
    /// 是否启用集群模式
    /// </summary>
    bool IsClusterEnabled { get; }

    /// <summary>
    /// 启动集群服务
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止集群服务
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取集群状态
    /// </summary>
    Task<ClusterStatus> GetClusterStatusAsync();

    /// <summary>
    /// 分发工作流任务到Worker节点（Master模式）
    /// </summary>
    Task<string> DispatchWorkflowTaskAsync(WorkflowTaskRequest request);

    /// <summary>
    /// 处理工作流任务（Worker模式）
    /// </summary>
    Task ProcessWorkflowTaskAsync(WorkflowTaskRequest request);

    /// <summary>
    /// 发布工作流状态更新
    /// </summary>
    Task PublishWorkflowStatusAsync(WorkflowStatusUpdate statusUpdate);

    /// <summary>
    /// 发布节点状态更新
    /// </summary>
    Task PublishNodeStatusAsync(NodeStatusUpdate statusUpdate);
}

/// <summary>
/// 集群状态
/// </summary>
public class ClusterStatus
{
    /// <summary>
    /// 集群名称
    /// </summary>
    public string ClusterName { get; set; } = string.Empty;

    /// <summary>
    /// 当前节点信息
    /// </summary>
    public NodeInfo CurrentNode { get; set; } = new();

    /// <summary>
    /// 集群节点列表
    /// </summary>
    public List<NodeInfo> Nodes { get; set; } = new();

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 集群健康状态
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 节点信息
/// </summary>
public class NodeInfo
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 节点模式
    /// </summary>
    public NodeMode Mode { get; set; }

    /// <summary>
    /// 节点状态
    /// </summary>
    public NodeHealthStatus Status { get; set; }

    /// <summary>
    /// 负载信息
    /// </summary>
    public NodeLoadInfo Load { get; set; } = new();

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeat { get; set; }

    /// <summary>
    /// 节点启动时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 支持的节点类型（Worker节点）
    /// </summary>
    public string[] SupportedNodeTypes { get; set; } = Array.Empty<string>();
}

/// <summary>
/// 节点健康状态
/// </summary>
public enum NodeHealthStatus
{
    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy,

    /// <summary>
    /// 离线
    /// </summary>
    Offline
}

/// <summary>
/// 节点负载信息
/// </summary>
public class NodeLoadInfo
{
    /// <summary>
    /// CPU使用率 (0-100)
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率 (0-100)
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 最大并发数
    /// </summary>
    public int MaxConcurrency { get; set; }

    /// <summary>
    /// 负载评分 (0-1, 越低表示负载越轻)
    /// </summary>
    public double LoadScore => MaxConcurrency > 0 ? (double)ActiveTasks / MaxConcurrency : 0;
}

/// <summary>
/// 工作流任务请求
/// </summary>
public class WorkflowTaskRequest
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义
    /// </summary>
    public string WorkflowDefinition { get; set; } = string.Empty;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 任务优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 触发者
    /// </summary>
    public string TriggeredBy { get; set; } = "system";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan? Timeout { get; set; }

    /// <summary>
    /// 目标Worker节点ID（可选）
    /// </summary>
    public string? TargetWorkerId { get; set; }
}

/// <summary>
/// 工作流状态更新
/// </summary>
public class WorkflowStatusUpdate
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 进度 (0-100)
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object>? OutputData { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理节点ID
    /// </summary>
    public string? ProcessedBy { get; set; }
}

/// <summary>
/// 节点状态更新
/// </summary>
public class NodeStatusUpdate
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object>? OutputData { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理节点ID
    /// </summary>
    public string? ProcessedBy { get; set; }
}
