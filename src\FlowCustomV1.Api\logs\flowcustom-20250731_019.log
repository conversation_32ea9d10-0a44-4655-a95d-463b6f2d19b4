[2025-07-31 19:18:05.972 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\debug
[2025-07-31 19:18:05.981 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\info
[2025-07-31 19:18:05.984 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\warn
[2025-07-31 19:18:05.988 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\error
[2025-07-31 19:18:05.991 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\trace
[2025-07-31 19:18:05.994 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 19:20:48.110 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 19:20:48.150 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 19:20:48.153 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 19:20:48.158 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 19:20:48.161 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 19:20:48.163 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 19:20:48.165 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 19:20:48.167 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 19:20:48.168 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 19:20:48.170 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 19:20:48.172 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 19:20:48.175 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 19:20:48.177 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 19:20:48.178 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 19:20:48.180 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 19:20:48.193 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 19:20:48.201 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 19:20:48.201 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 19:20:48.204 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 19:20:48.210 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 19:20:48.212 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 19:20:48.213 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 19:20:48.219 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 19:20:48.222 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 19:20:48.222 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,344,520 字节
[2025-07-31 19:20:48.224 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 19:20:48.226 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 19:20:48.228 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 19:20:48.231 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Master", NodeId: master-001
[2025-07-31 19:20:48.234 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Master", 节点ID: master-001
[2025-07-31 19:20:48.238 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 1/10)
[2025-07-31 19:20:48.857 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:20:48.860 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:20:48.863 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:20:48.866 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:20:48.868 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:20:49.071 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 19:20:49.074 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 19:20:49.154 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 19:20:49.157 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 19:20:49.159 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 19:20:49.251 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 2/10)
[2025-07-31 19:20:49.333 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:20:49.349 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:20:49.381 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 19:20:49.384 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 19:20:49.633 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 19:20:49.643 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 19:20:49.649 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 19:20:49.652 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 19:20:49.656 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 19:20:49.660 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 19:20:49.829 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 19:20:49.831 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 19:20:49.838 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 19:20:49.841 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 19:20:49.844 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 19:20:49.848 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 19:20:49.853 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 19:20:49.856 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 19:20:49.864 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 19:20:49.871 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 19:20:49.875 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 19:20:49.880 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 19:20:49.886 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 19:20:49.892 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 19:20:49.898 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 19:20:49.903 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 19:20:49.907 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 19:20:49.914 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 19:20:49.934 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 19:20:49.940 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 19:20:49.945 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 19:20:49.950 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 19:20:49.954 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 19:20:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 19:20:49.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 19:20:49.965 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 19:20:49.968 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 19:20:49.971 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 19:20:49.974 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 19:20:49.977 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 19:20:49.981 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 19:20:49.983 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 19:20:49.993 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 19:20:49.999 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 19:20:50.003 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 19:20:50.007 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 19:20:50.009 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:20:50.011 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:20:50.013 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 19:20:50.015 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 19:20:50.017 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 19:20:50.025 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 19:20:50.027 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 19:20:50.030 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 19:20:50.036 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:20:50.164 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:20:50.169 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:20:50.172 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:20:50.174 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:20:50.176 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 19:20:50.178 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 19:20:50.186 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 19:20:50.188 +08:00 INF] Program: 后台初始化完成
[2025-07-31 19:20:50.261 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.heartbeat
[2025-07-31 19:20:50.316 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.events
[2025-07-31 19:20:50.345 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ NATS订阅初始化成功
[2025-07-31 19:20:50.423 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: master-001
[2025-07-31 19:20:50.426 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:20:50.430 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 19:20:50.438 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 19:20:50.442 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 19:20:50.498 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 19:20:50.502 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 19:20:53.212 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 19:20:58.214 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 19:21:18.161 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 19:21:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:21:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:21:37.661 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:21:37.670 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:21:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:21:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:22:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:22:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:22:19.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:22:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:22:31.863 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔍 集群状态查询 - 节点字典大小: 1, 节点列表: [worker-001]
[2025-07-31 19:22:31.883 +08:00 INF] Program: 请求完成: GET /api/cluster/status - 200 - 30ms
[2025-07-31 19:22:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:22:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:22:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:22:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:23:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:23:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:23:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:23:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:23:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:23:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:23:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:23:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:24:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:24:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:24:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:24:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:24:37.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:24:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:24:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:24:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:25:07.640 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:25:07.643 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:25:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:25:20.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:25:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:25:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:25:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:25:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:26:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:26:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:26:19.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:26:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:26:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:26:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:26:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:26:50.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:27:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:27:07.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:27:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:27:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:27:37.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:27:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:27:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:27:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:28:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:28:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:28:19.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:28:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:28:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:28:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:28:49.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:28:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:29:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:29:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:29:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:29:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:29:37.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:29:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:29:49.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:29:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:30:07.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:30:07.640 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:30:18.107 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 19:30:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:30:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:30:37.641 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:30:37.646 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:30:49.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:30:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:31:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:31:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:31:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:31:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:31:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:31:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:31:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:31:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:32:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:32:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:32:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:32:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:32:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:32:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:32:49.973 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:32:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:33:07.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:33:07.640 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker", 字典大小: 1
[2025-07-31 19:33:19.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:33:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:33:48.113 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 19:33:50.012 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:33:50.402 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:33:50.493 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 19:33:51.163 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:33:51.139 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:33:55.266 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:33:55.277 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 19:34:00.299 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 19:34:04.369 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:34:04.375 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 19:34:14.383 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 19:34:14.393 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:34:14.395 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:34:14.397 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:34:14.399 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:34:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:34:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:34:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:34:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:35:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:35:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:35:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:35:50.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:36:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:36:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:36:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:36:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:37:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:37:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:37:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:37:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:38:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:38:20.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:38:48.113 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 19:38:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:38:50.379 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 19:38:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:38:55.348 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:38:55.349 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:38:59.406 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:38:59.410 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 19:39:04.418 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 19:39:04.427 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:39:04.428 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:39:04.430 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:39:04.431 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:39:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:39:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:39:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:39:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:40:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:40:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:40:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:40:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:41:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:41:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:41:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:41:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:42:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:42:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:42:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:42:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:42:59.460 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:42:59.465 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:42:59.473 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not start to connect nats server: nats://localhost:4222
 ---> NATS.Client.Core.Internal.SocketClosedException: Socket has been closed.
   at NATS.Client.Core.Internal.SocketReader.ReadAtLeastAsync(Int32 minimumSize)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at NATS.Client.Core.Internal.NatsReadProtocolProcessor.ReadLoopAsync()
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   --- End of inner exception stack trace ---
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:42:59.479 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 19:43:04.485 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 19:43:04.501 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:43:04.504 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:43:04.506 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:43:04.508 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:43:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:43:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:43:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:43:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:44:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:44:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:44:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:44:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:45:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:45:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:45:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:45:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:46:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:46:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:46:49.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:46:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:47:19.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:47:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:47:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:47:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:48:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:48:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:48:49.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:48:50.384 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:49:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:49:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:49:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:49:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:50:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:50:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:50:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:50:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:51:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:51:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:51:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:51:50.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:52:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:52:20.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:52:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:52:50.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:53:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:53:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:53:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:53:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:54:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:54:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:54:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:54:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:55:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:55:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:55:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:55:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:56:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:56:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:56:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:56:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:57:19.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:57:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:57:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:57:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:58:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:58:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:58:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:58:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:59:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:59:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:59:49.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:59:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:00:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:00:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:00:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:00:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:01:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:01:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:01:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:01:50.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:02:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:02:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:02:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:02:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:03:19.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:03:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:03:49.980 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:03:50.390 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:04:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:04:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:04:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:04:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:05:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:05:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:05:49.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:05:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:06:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:06:20.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:06:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:06:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:07:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:07:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:07:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:07:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:08:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:08:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:08:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:08:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:09:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:09:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:09:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:09:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:10:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:10:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:10:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:10:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:11:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:11:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:11:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:11:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:12:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:12:20.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:12:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:12:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:13:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:13:20.382 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:13:49.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:13:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:14:19.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:14:20.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:14:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:14:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:15:19.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:15:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:15:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:15:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:16:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:16:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:16:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:16:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:17:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:17:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:17:49.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:17:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:18:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:18:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:18:49.974 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:18:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:19:19.956 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:19:20.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:19:49.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:19:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:20:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:20:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:20:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:20:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:21:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:21:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:21:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:21:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:22:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:22:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:22:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:22:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:23:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:23:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:23:49.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:23:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:24:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:24:20.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:24:49.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:24:50.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:25:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:25:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:25:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:25:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:26:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:26:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:26:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:26:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:27:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:27:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:27:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:27:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:28:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:28:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:28:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:28:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:29:19.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:29:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:29:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:29:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:30:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:30:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:30:49.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:30:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:31:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:31:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:31:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:31:50.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:32:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:32:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:32:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:32:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:33:19.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:33:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:33:49.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:33:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:34:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:34:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:34:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:34:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:35:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:35:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:35:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:35:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:36:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:36:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:36:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:36:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:37:19.968 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:37:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:37:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:37:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:38:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:38:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:38:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:38:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:39:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:39:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:39:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:39:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:40:19.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:40:20.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:40:49.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:40:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:41:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:41:20.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:41:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:41:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:42:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:42:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:42:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:42:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:43:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:43:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:43:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:43:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:44:19.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:44:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:44:49.966 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:44:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:45:19.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:45:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:45:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:45:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:46:19.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:46:20.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:46:49.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:46:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:47:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:47:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:47:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:47:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:48:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:48:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:48:50.038 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:48:50.408 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:49:19.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:49:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:49:49.974 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:49:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:50:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:50:20.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:50:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:50:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:51:18.105 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 20:51:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:51:20.378 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 20:51:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:51:30.548 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 20:51:30.550 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 20:51:31.074 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 20:51:31.076 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 20:51:31.078 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 20:51:31.080 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 20:51:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:51:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:52:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:52:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:52:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:52:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:53:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:53:20.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:53:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:53:50.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:54:19.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:54:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:54:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:54:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:55:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:55:20.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:55:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:55:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:56:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:56:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:56:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:56:50.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:57:19.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:57:20.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:57:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:57:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:58:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:58:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:58:49.972 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:58:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:59:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:59:20.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 20:59:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:59:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:00:19.962 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:00:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:00:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:00:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:01:14.291 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 520ms
[2025-07-31 21:01:14.449 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 153ms
[2025-07-31 21:01:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:01:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:01:43.703 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 BuiltinPluginManager 获取到 16 个节点类型定义
[2025-07-31 21:01:43.710 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 JsonPluginManager 获取到 0 个节点类型定义
[2025-07-31 21:01:43.715 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 DllPluginManager 获取到 0 个节点类型定义
[2025-07-31 21:01:43.717 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: GetAllNodeTypeDefinitionsAsync(category=null): 总计返回 16 个节点类型定义
[2025-07-31 21:01:43.761 +08:00 INF] Program: 请求完成: GET /api/plugin/node-types - 200 - 72ms
[2025-07-31 21:01:43.894 +08:00 INF] Program: 请求完成: GET /api/workflows/50711aaa-8281-4b60-ba1e-15c6f15eac48 - 200 - 103ms
[2025-07-31 21:01:43.974 +08:00 INF] Program: 请求完成: GET /api/plugin/category-metadata - 200 - 8ms
[2025-07-31 21:01:43.975 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 BuiltinPluginManager 获取到 16 个节点类型
[2025-07-31 21:01:43.980 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 JsonPluginManager 获取到 0 个节点类型
[2025-07-31 21:01:43.983 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 DllPluginManager 获取到 0 个节点类型
[2025-07-31 21:01:43.985 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: GetAllNodeTypeDefinitionsAsync: 总计返回 16 个节点类型
[2025-07-31 21:01:43.998 +08:00 INF] Program: 请求完成: GET /api/plugin/node-types/by-category - 200 - 32ms
[2025-07-31 21:01:47.386 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 11ms
[2025-07-31 21:01:47.400 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 12ms
[2025-07-31 21:01:49.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:01:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:01:59.026 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 577ms
[2025-07-31 21:01:59.031 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 3ms
[2025-07-31 21:01:59.447 +08:00 INF] Program: 请求完成: DELETE /api/workflows/50711aaa-8281-4b60-ba1e-15c6f15eac48 - 204 - 9567ms
[2025-07-31 21:01:59.450 +08:00 WRN] Program: 慢请求检测: DELETE /api/workflows/50711aaa-8281-4b60-ba1e-15c6f15eac48 - 9567ms
[2025-07-31 21:02:19.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:02:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:02:49.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:02:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:03:01.812 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 6ms
[2025-07-31 21:03:01.824 +08:00 INF] Program: 请求完成: GET /api/workflows - 200 - 9ms
[2025-07-31 21:03:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:03:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:03:50.008 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:03:50.388 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:04:19.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:04:20.366 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:04:49.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:04:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:05:19.964 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:05:20.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:05:49.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:05:50.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:06:19.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:06:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:06:49.969 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:06:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:07:19.971 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:07:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:07:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:07:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:08:19.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:08:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:08:49.970 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:08:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:09:19.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:09:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:09:49.963 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:09:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:10:19.960 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:10:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:10:49.958 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:10:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:11:18.102 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:11:19.959 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:11:20.376 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:11:20.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:11:31.355 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:11:31.357 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 21:11:35.411 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:35.416 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 21:11:40.421 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 21:11:44.456 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:44.466 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 21:11:48.109 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:11:49.961 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:11:50.372 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:11:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:11:54.478 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 21:11:58.535 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 3/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:58.539 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 15000ms 后尝试重连
[2025-07-31 21:12:05.432 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:12:05.434 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 4/10)
[2025-07-31 21:12:09.473 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 4/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:09.477 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 20000ms 后尝试重连
[2025-07-31 21:12:18.103 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:12:19.967 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:12:20.368 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:12:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 21:12:29.484 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 5/10)
[2025-07-31 21:12:33.559 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 5/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:33.566 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 25000ms 后尝试重连
[2025-07-31 21:12:39.494 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:12:39.498 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 6/10)
[2025-07-31 21:12:43.533 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 6/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:43.545 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 30000ms 后尝试重连
[2025-07-31 21:12:48.099 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:12:49.965 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:12:50.376 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:12:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
