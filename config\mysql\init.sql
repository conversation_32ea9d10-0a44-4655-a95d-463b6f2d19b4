-- FlowCustomV1 调试环境数据库初始化脚本
-- 创建时间: 2025-07-31

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建工作流定义表
CREATE TABLE IF NOT EXISTS `workflows` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `definition` longtext NOT NULL,
  `version` int NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(255) DEFAULT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_workflows_name` (`name`),
  KEY `idx_workflows_active` (`is_active`),
  KEY `idx_workflows_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建工作流执行记录表
CREATE TABLE IF NOT EXISTS `workflow_executions` (
  `id` varchar(36) NOT NULL,
  `workflow_id` varchar(36) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `triggered_by` varchar(255) DEFAULT NULL,
  `trigger_type` varchar(50) DEFAULT NULL,
  `input_data` longtext,
  `output_data` longtext,
  `error_message` text,
  `started_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `processed_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_executions_workflow_id` (`workflow_id`),
  KEY `idx_executions_status` (`status`),
  KEY `idx_executions_created_at` (`created_at`),
  KEY `idx_executions_triggered_by` (`triggered_by`),
  CONSTRAINT `fk_executions_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `workflows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建节点执行记录表
CREATE TABLE IF NOT EXISTS `node_executions` (
  `id` varchar(36) NOT NULL,
  `execution_id` varchar(36) NOT NULL,
  `node_id` varchar(255) NOT NULL,
  `node_type` varchar(100) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `input_data` longtext,
  `output_data` longtext,
  `error_message` text,
  `started_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `processed_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_node_executions_execution_id` (`execution_id`),
  KEY `idx_node_executions_node_id` (`node_id`),
  KEY `idx_node_executions_status` (`status`),
  KEY `idx_node_executions_created_at` (`created_at`),
  CONSTRAINT `fk_node_executions_execution` FOREIGN KEY (`execution_id`) REFERENCES `workflow_executions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建集群节点表
CREATE TABLE IF NOT EXISTS `cluster_nodes` (
  `id` varchar(36) NOT NULL,
  `node_id` varchar(255) NOT NULL UNIQUE,
  `display_name` varchar(255) NOT NULL,
  `cluster_name` varchar(255) NOT NULL,
  `roles` varchar(255) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'offline',
  `last_heartbeat` datetime DEFAULT NULL,
  `metadata` longtext,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cluster_nodes_node_id` (`node_id`),
  KEY `idx_cluster_nodes_cluster_name` (`cluster_name`),
  KEY `idx_cluster_nodes_status` (`status`),
  KEY `idx_cluster_nodes_last_heartbeat` (`last_heartbeat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建参数模板表
CREATE TABLE IF NOT EXISTS `parameter_templates` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `category` varchar(100) DEFAULT NULL,
  `template_data` longtext NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_parameter_templates_name` (`name`),
  KEY `idx_parameter_templates_category` (`category`),
  KEY `idx_parameter_templates_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些测试数据
INSERT INTO `workflows` (`id`, `name`, `description`, `definition`, `version`, `is_active`, `created_by`) VALUES
('test-workflow-001', '测试工作流1', '用于调试的简单测试工作流', '{"nodes":[],"connections":[]}', 1, 1, 'system'),
('test-workflow-002', '测试工作流2', '用于调试的HTTP请求工作流', '{"nodes":[],"connections":[]}', 1, 1, 'system');

INSERT INTO `parameter_templates` (`id`, `name`, `description`, `category`, `template_data`, `created_by`) VALUES
('http-get-template', 'HTTP GET请求模板', '标准的HTTP GET请求参数模板', 'http', '{"method":"GET","headers":{},"timeout":30}', 'system'),
('http-post-template', 'HTTP POST请求模板', '标准的HTTP POST请求参数模板', 'http', '{"method":"POST","headers":{"Content-Type":"application/json"},"timeout":30}', 'system');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
