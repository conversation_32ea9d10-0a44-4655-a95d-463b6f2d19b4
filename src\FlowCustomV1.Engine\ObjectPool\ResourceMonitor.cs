using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 资源监控器接口
    /// </summary>
    public interface IResourceMonitor : IDisposable
    {
        /// <summary>
        /// 开始监控
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// 获取资源使用统计
        /// </summary>
        /// <returns>资源使用统计</returns>
        ResourceUsageStatistics GetResourceUsage();

        /// <summary>
        /// 检测内存泄漏
        /// </summary>
        /// <returns>内存泄漏检测结果</returns>
        MemoryLeakDetectionResult DetectMemoryLeaks();

        /// <summary>
        /// 注册对象池进行监控
        /// </summary>
        /// <param name="poolName">对象池名称</param>
        /// <param name="getStatistics">获取统计信息的委托</param>
        void RegisterPool(string poolName, Func<object> getStatistics);

        /// <summary>
        /// 取消注册对象池
        /// </summary>
        /// <param name="poolName">对象池名称</param>
        void UnregisterPool(string poolName);
    }

    /// <summary>
    /// 资源使用统计
    /// </summary>
    public class ResourceUsageStatistics
    {
        /// <summary>
        /// 当前内存使用量（字节）
        /// </summary>
        public long CurrentMemoryUsage { get; set; }

        /// <summary>
        /// 峰值内存使用量（字节）
        /// </summary>
        public long PeakMemoryUsage { get; set; }

        /// <summary>
        /// GC回收次数
        /// </summary>
        public GCCollectionCounts GCCounts { get; set; } = new();

        /// <summary>
        /// 对象池统计信息
        /// </summary>
        public Dictionary<string, object> PoolStatistics { get; set; } = new();

        /// <summary>
        /// 监控开始时间
        /// </summary>
        public DateTime MonitoringStartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }
    }

    /// <summary>
    /// GC回收次数统计
    /// </summary>
    public class GCCollectionCounts
    {
        /// <summary>
        /// 第0代GC回收次数
        /// </summary>
        public int Generation0 { get; set; }

        /// <summary>
        /// 第1代GC回收次数
        /// </summary>
        public int Generation1 { get; set; }

        /// <summary>
        /// 第2代GC回收次数
        /// </summary>
        public int Generation2 { get; set; }
    }

    /// <summary>
    /// 内存泄漏检测结果
    /// </summary>
    public class MemoryLeakDetectionResult
    {
        /// <summary>
        /// 是否检测到潜在内存泄漏
        /// </summary>
        public bool HasPotentialLeak { get; set; }

        /// <summary>
        /// 内存增长率（每分钟字节数）
        /// </summary>
        public double MemoryGrowthRate { get; set; }

        /// <summary>
        /// 检测详情
        /// </summary>
        public List<string> Details { get; set; } = new();

        /// <summary>
        /// 建议操作
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 资源监控器实现
    /// </summary>
    public class ResourceMonitor : IResourceMonitor
    {
        private readonly ILogger<ResourceMonitor>? _logger;
        private readonly ConcurrentDictionary<string, Func<object>> _registeredPools = new();
        private readonly Timer? _monitoringTimer;
        private readonly object _lockObject = new();
        
        private volatile bool _isMonitoring;
        private volatile bool _disposed;
        private DateTime _monitoringStartTime;
        private long _initialMemoryUsage;
        private long _peakMemoryUsage;
        private readonly List<MemorySnapshot> _memorySnapshots = new();
        private GCCollectionCounts _initialGCCounts = new();

        /// <summary>
        /// 内存快照
        /// </summary>
        private class MemorySnapshot
        {
            public DateTime Timestamp { get; set; }
            public long MemoryUsage { get; set; }
            public GCCollectionCounts GCCounts { get; set; } = new();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="monitoringIntervalSeconds">监控间隔（秒）</param>
        public ResourceMonitor(ILogger<ResourceMonitor>? logger = null, int monitoringIntervalSeconds = 30)
        {
            _logger = logger;
            _monitoringTimer = new Timer(MonitoringCallback, null, Timeout.Infinite, Timeout.Infinite);
            
            // 设置监控间隔
            var interval = TimeSpan.FromSeconds(Math.Max(1, monitoringIntervalSeconds));
            _monitoringTimer.Change(interval, interval);
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            if (_disposed || _isMonitoring)
                return;

            lock (_lockObject)
            {
                if (_isMonitoring)
                    return;

                _monitoringStartTime = DateTime.UtcNow;
                _initialMemoryUsage = GC.GetTotalMemory(false);
                _peakMemoryUsage = _initialMemoryUsage;
                _initialGCCounts = GetCurrentGCCounts();
                _memorySnapshots.Clear();

                _isMonitoring = true;
                _logger?.LogInformation("资源监控已启动，初始内存使用: {InitialMemory:N0} 字节", _initialMemoryUsage);
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            if (_disposed || !_isMonitoring)
                return;

            lock (_lockObject)
            {
                if (!_isMonitoring)
                    return;

                _isMonitoring = false;
                var finalMemoryUsage = GC.GetTotalMemory(false);
                var monitoringDuration = DateTime.UtcNow - _monitoringStartTime;

                _logger?.LogInformation(
                    "资源监控已停止，监控时长: {Duration}, 最终内存使用: {FinalMemory:N0} 字节, 峰值: {PeakMemory:N0} 字节",
                    monitoringDuration, finalMemoryUsage, _peakMemoryUsage);
            }
        }

        /// <summary>
        /// 获取资源使用统计
        /// </summary>
        public ResourceUsageStatistics GetResourceUsage()
        {
            var currentMemory = GC.GetTotalMemory(false);
            var currentGCCounts = GetCurrentGCCounts();

            // 更新峰值内存使用量
            if (currentMemory > _peakMemoryUsage)
            {
                _peakMemoryUsage = currentMemory;
            }

            var statistics = new ResourceUsageStatistics
            {
                CurrentMemoryUsage = currentMemory,
                PeakMemoryUsage = _peakMemoryUsage,
                GCCounts = currentGCCounts,
                MonitoringStartTime = _monitoringStartTime,
                LastUpdateTime = DateTime.UtcNow
            };

            // 收集对象池统计信息
            foreach (var kvp in _registeredPools)
            {
                try
                {
                    var poolStats = kvp.Value();
                    statistics.PoolStatistics[kvp.Key] = poolStats;
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "获取对象池 {PoolName} 统计信息时发生异常", kvp.Key);
                }
            }

            return statistics;
        }

        /// <summary>
        /// 检测内存泄漏
        /// </summary>
        public MemoryLeakDetectionResult DetectMemoryLeaks()
        {
            var result = new MemoryLeakDetectionResult();

            lock (_lockObject)
            {
                if (_memorySnapshots.Count < 2)
                {
                    result.Details.Add("内存快照数量不足，无法进行泄漏检测");
                    return result;
                }

                // 计算内存增长率
                var firstSnapshot = _memorySnapshots.First();
                var lastSnapshot = _memorySnapshots.Last();
                var timeDiff = (lastSnapshot.Timestamp - firstSnapshot.Timestamp).TotalMinutes;
                var memoryDiff = lastSnapshot.MemoryUsage - firstSnapshot.MemoryUsage;

                if (timeDiff > 0)
                {
                    result.MemoryGrowthRate = memoryDiff / timeDiff;
                }

                // 检测潜在泄漏
                const double leakThreshold = 1024 * 1024; // 1MB per minute
                if (result.MemoryGrowthRate > leakThreshold)
                {
                    result.HasPotentialLeak = true;
                    result.Details.Add($"检测到高内存增长率: {result.MemoryGrowthRate:N0} 字节/分钟");
                    result.Recommendations.Add("建议检查对象池配置和对象生命周期管理");
                    result.Recommendations.Add("考虑手动触发GC回收: GC.Collect()");
                }

                // 检查GC回收效率
                var gcDiff0 = lastSnapshot.GCCounts.Generation0 - firstSnapshot.GCCounts.Generation0;
                var gcDiff1 = lastSnapshot.GCCounts.Generation1 - firstSnapshot.GCCounts.Generation1;
                var gcDiff2 = lastSnapshot.GCCounts.Generation2 - firstSnapshot.GCCounts.Generation2;

                if (gcDiff2 > 10 && result.MemoryGrowthRate > 0)
                {
                    result.Details.Add($"频繁的第2代GC回收 ({gcDiff2} 次) 但内存仍在增长");
                    result.Recommendations.Add("检查是否存在强引用阻止对象回收");
                }

                result.Details.Add($"监控时长: {timeDiff:F1} 分钟");
                result.Details.Add($"内存变化: {memoryDiff:N0} 字节");
                result.Details.Add($"GC回收次数 - Gen0: {gcDiff0}, Gen1: {gcDiff1}, Gen2: {gcDiff2}");
            }

            return result;
        }

        /// <summary>
        /// 注册对象池进行监控
        /// </summary>
        public void RegisterPool(string poolName, Func<object> getStatistics)
        {
            if (string.IsNullOrEmpty(poolName) || getStatistics == null)
                return;

            _registeredPools.AddOrUpdate(poolName, getStatistics, (key, oldValue) => getStatistics);
            _logger?.LogDebug("已注册对象池进行监控: {PoolName}", poolName);
        }

        /// <summary>
        /// 取消注册对象池
        /// </summary>
        public void UnregisterPool(string poolName)
        {
            if (string.IsNullOrEmpty(poolName))
                return;

            _registeredPools.TryRemove(poolName, out _);
            _logger?.LogDebug("已取消注册对象池: {PoolName}", poolName);
        }

        /// <summary>
        /// 监控回调
        /// </summary>
        private void MonitoringCallback(object? state)
        {
            if (!_isMonitoring || _disposed)
                return;

            try
            {
                var currentMemory = GC.GetTotalMemory(false);
                var currentGCCounts = GetCurrentGCCounts();

                lock (_lockObject)
                {
                    // 添加内存快照
                    _memorySnapshots.Add(new MemorySnapshot
                    {
                        Timestamp = DateTime.UtcNow,
                        MemoryUsage = currentMemory,
                        GCCounts = currentGCCounts
                    });

                    // 保持最近100个快照
                    if (_memorySnapshots.Count > 100)
                    {
                        _memorySnapshots.RemoveAt(0);
                    }

                    // 更新峰值内存
                    if (currentMemory > _peakMemoryUsage)
                    {
                        _peakMemoryUsage = currentMemory;
                    }
                }

                _logger?.LogDebug("资源监控快照 - 内存: {Memory:N0} 字节, GC: Gen0={Gen0}, Gen1={Gen1}, Gen2={Gen2}",
                    currentMemory, currentGCCounts.Generation0, currentGCCounts.Generation1, currentGCCounts.Generation2);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "资源监控回调执行时发生异常");
            }
        }

        /// <summary>
        /// 获取当前GC回收次数
        /// </summary>
        private static GCCollectionCounts GetCurrentGCCounts()
        {
            return new GCCollectionCounts
            {
                Generation0 = GC.CollectionCount(0),
                Generation1 = GC.CollectionCount(1),
                Generation2 = GC.CollectionCount(2)
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            StopMonitoring();
            _monitoringTimer?.Dispose();
            _registeredPools.Clear();

            _logger?.LogInformation("资源监控器已释放");
        }
    }
}
