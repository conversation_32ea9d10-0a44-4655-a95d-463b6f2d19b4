{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5279", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7101;http://localhost:5279", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Master": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5279", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Master"}}, "Worker": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:8080", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Worker"}}}}