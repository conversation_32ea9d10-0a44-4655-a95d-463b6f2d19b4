version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: flowcustom-mysql-debug
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: flowcustom123
      MYSQL_DATABASE: flowcustom_debug
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: flowcustom123
    ports:
      - "3306:3306"
    volumes:
      - mysql_debug_data:/var/lib/mysql
      - ./config/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - flowcustom_debug
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pflowcustom123"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: flowcustom-redis-debug
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_debug_data:/data
    networks:
      - flowcustom_debug
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # NATS JetStream服务器 (支持WebSocket)
  nats:
    image: nats:2.10-alpine
    container_name: flowcustom-nats-debug
    restart: unless-stopped
    command: [
      "--config", "/etc/nats/nats-websocket.conf"
    ]
    ports:
      - "4222:4222"  # NATS客户端端口
      - "8222:8222"  # HTTP监控端口
      - "8081:8081"  # WebSocket端口
    volumes:
      - nats_debug_data:/data
      - ./config/nats/nats-websocket.conf:/etc/nats/nats-websocket.conf:ro
    networks:
      - flowcustom_debug
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      timeout: 3s
      retries: 5

  # Master节点在本地运行，这里注释掉
  # flowcustom-master:
  #   build:
  #     context: .
  #     dockerfile: src/FlowCustomV1.Cluster/Dockerfile.debug
  #     target: master
  #   container_name: flowcustom-master-debug
  #   restart: unless-stopped
  #   environment:
  #     - ASPNETCORE_ENVIRONMENT=Development
  #     - ASPNETCORE_URLS=http://+:5279
  #     - NodeConfiguration__NodeId=master-001
  #     - NodeConfiguration__DisplayName=调试Master节点
  #     - NodeConfiguration__ClusterName=flowcustom-debug-cluster
  #     - NodeConfiguration__EnableMasterRole=true
  #     - NodeConfiguration__EnableWorkerRole=false
  #     - NodeConfiguration__Master__ApiPort=5279
  #     - NodeConfiguration__Master__EnableSwagger=true
  #     - NodeConfiguration__Master__EnableCors=true
  #     - NodeConfiguration__Communication__NatsConnectionString=nats://nats:4222
  #     - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;
  #     - ConnectionStrings__NATS=nats://nats:4222
  #     - ConnectionStrings__Redis=redis:6379
  #   ports:
  #     - "5279:5279"
  #   volumes:
  #     - ./logs/master:/app/logs
  #   networks:
  #     - flowcustom_debug
  #   depends_on:
  #     mysql:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #     nats:
  #       condition: service_healthy
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:5279/health"]
  #     timeout: 10s
  #     retries: 5
  #     start_period: 30s

  # Worker节点1 - 连接到本地基础设施
  flowcustom-worker1:
    build:
      context: .
      dockerfile: src/FlowCustomV1.Cluster/Dockerfile.debug
      target: worker
    container_name: flowcustom-worker1-debug
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - NodeConfiguration__NodeId=docker-worker-001
      - NodeConfiguration__DisplayName=Docker调试Worker节点1
      - NodeConfiguration__ClusterName=flowcustom-local-debug-cluster
      - NodeConfiguration__EnableMasterRole=false
      - NodeConfiguration__EnableWorkerRole=true
      - NodeConfiguration__Worker__MaxConcurrentExecutions=4
      - NodeConfiguration__Worker__WorkerType=general
      - NodeConfiguration__Worker__SupportedNodeTypes__0=script
      - NodeConfiguration__Worker__SupportedNodeTypes__1=http
      - NodeConfiguration__Worker__SupportedNodeTypes__2=timer
      - NodeConfiguration__Worker__SupportedNodeTypes__3=webhook
      - NodeConfiguration__Communication__NatsConnectionString=nats://host.docker.internal:4222
      - NodeConfiguration__Communication__NatsOptions__Name=FlowCustomV1-DockerWorker1-Debug
      - ConnectionStrings__DefaultConnection=Server=host.docker.internal;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;
      - ConnectionStrings__NATS=nats://host.docker.internal:4222
      - ConnectionStrings__Redis=host.docker.internal:6379
    ports:
      - "8080:8080"
    volumes:
      - ./logs/worker1:/app/logs
    networks:
      - flowcustom_debug
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端开发服务器 (暂时注释掉，稍后配置)
  # flowcustom-frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile.debug
  #   container_name: flowcustom-frontend-debug
  #   restart: unless-stopped
  #   environment:
  #     - VITE_API_BASE_URL=http://localhost:5279
  #     - VITE_NATS_WS_URL=ws://localhost:8080
  #     - NODE_ENV=development
  #   ports:
  #     - "5173:5173"
  #   volumes:
  #     - ./frontend:/app
  #     - /app/node_modules
  #   networks:
  #     - flowcustom_debug

volumes:
  mysql_debug_data:
  redis_debug_data:
  nats_debug_data:
  nats_debug_logs:

networks:
  flowcustom_debug:
    driver: bridge
