# FlowCustomV1 系统分析图表

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| 文档名称 | FlowCustomV1 系统分析图表 |
| 版本 | v1.0 |
| 创建日期 | 2025-01-27 |
| 作者 | 系统分析师 |

## 🎯 1. 功能用例图

### 1.1 系统整体用例图
```mermaid
graph TB
    subgraph "FlowCustomV1 工作流系统"
        subgraph "工作流管理"
            UC1[创建工作流]
            UC2[编辑工作流]
            UC3[删除工作流]
            UC4[复制工作流]
            UC5[导入工作流]
            UC6[导出工作流]
        end
        
        subgraph "节点管理"
            UC7[添加节点]
            UC8[配置节点]
            UC9[删除节点]
            UC10[连接节点]
            UC11[断开连接]
        end
        
        subgraph "执行管理"
            UC12[执行工作流]
            UC13[暂停执行]
            UC14[停止执行]
            UC15[重新执行]
            UC16[测试节点]
        end
        
        subgraph "监控管理"
            UC17[查看执行状态]
            UC18[查看执行日志]
            UC19[查看性能指标]
            UC20[设置告警]
        end
        
        subgraph "插件管理"
            UC21[安装插件]
            UC22[卸载插件]
            UC23[更新插件]
            UC24[配置插件]
        end
        
        subgraph "用户管理"
            UC25[用户登录]
            UC26[权限管理]
            UC27[角色分配]
        end
    end
    
    %% 参与者
    Designer[工作流设计师]
    Operator[运维人员]
    Admin[系统管理员]
    Developer[插件开发者]
    
    %% 关联关系
    Designer --> UC1
    Designer --> UC2
    Designer --> UC3
    Designer --> UC4
    Designer --> UC5
    Designer --> UC6
    Designer --> UC7
    Designer --> UC8
    Designer --> UC9
    Designer --> UC10
    Designer --> UC11
    Designer --> UC12
    Designer --> UC16
    
    Operator --> UC12
    Operator --> UC13
    Operator --> UC14
    Operator --> UC15
    Operator --> UC17
    Operator --> UC18
    Operator --> UC19
    
    Admin --> UC20
    Admin --> UC21
    Admin --> UC22
    Admin --> UC23
    Admin --> UC24
    Admin --> UC25
    Admin --> UC26
    Admin --> UC27
    
    Developer --> UC21
    Developer --> UC24
```

### 1.2 核心用例详细描述

#### UC1: 创建工作流
```mermaid
graph LR
    A[开始] --> B[输入工作流基本信息]
    B --> C[选择工作流模板]
    C --> D[配置工作流参数]
    D --> E[验证工作流配置]
    E --> F{验证通过?}
    F -->|是| G[保存工作流]
    F -->|否| H[显示错误信息]
    H --> D
    G --> I[返回工作流ID]
    I --> J[结束]
```

#### UC12: 执行工作流
```mermaid
graph LR
    A[开始] --> B[选择工作流]
    B --> C[设置执行参数]
    C --> D[验证执行权限]
    D --> E{权限验证通过?}
    E -->|否| F[显示权限错误]
    E -->|是| G[提交执行请求]
    G --> H[创建执行实例]
    H --> I[开始执行]
    I --> J[监控执行状态]
    J --> K{执行完成?}
    K -->|否| J
    K -->|是| L[显示执行结果]
    L --> M[结束]
    F --> M
```

## 🏗️ 2. 系统架构类图

### 2.1 核心领域模型类图
```mermaid
classDiagram
    class WorkflowDefinition {
        +Guid Id
        +string Name
        +string Description
        +string Version
        +WorkflowStatus Status
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +string CreatedBy
        +List~WorkflowNode~ Nodes
        +List~WorkflowConnection~ Connections
        +Dictionary~string,object~ Variables
        +WorkflowMetadata Metadata
        +Create(name, description) WorkflowDefinition
        +AddNode(nodeDefinition) void
        +RemoveNode(nodeId) void
        +AddConnection(connection) void
        +RemoveConnection(connectionId) void
        +Validate() ValidationResult
        +Execute(parameters) Task~ExecutionResult~
    }
    
    class WorkflowNode {
        +string Id
        +string Name
        +string Type
        +string PluginName
        +NodePosition Position
        +Dictionary~string,object~ Configuration
        +List~NodeEndpoint~ InputEndpoints
        +List~NodeEndpoint~ OutputEndpoints
        +bool IsEnabled
        +NodeStatus Status
        +Create(definition) WorkflowNode
        +UpdateConfiguration(config) void
        +Validate() ValidationResult
        +Execute(context) Task~NodeExecutionResult~
    }
    
    class WorkflowConnection {
        +string Id
        +string SourceNodeId
        +string SourceEndpointId
        +string TargetNodeId
        +string TargetEndpointId
        +ConnectionType Type
        +Dictionary~string,object~ Properties
        +Create(source, target) WorkflowConnection
        +Validate() ValidationResult
    }
    
    class WorkflowExecution {
        +Guid Id
        +Guid WorkflowId
        +ExecutionStatus Status
        +DateTime StartedAt
        +DateTime? CompletedAt
        +string StartedBy
        +Dictionary~string,object~ InputParameters
        +Dictionary~string,object~ OutputResults
        +List~NodeExecution~ NodeExecutions
        +List~ExecutionLog~ Logs
        +Create(workflowId, parameters) WorkflowExecution
        +Start() void
        +Pause() void
        +Resume() void
        +Stop() void
        +AddLog(message, level) void
    }
    
    class NodeExecution {
        +Guid Id
        +Guid ExecutionId
        +string NodeId
        +NodeExecutionStatus Status
        +DateTime StartedAt
        +DateTime? CompletedAt
        +Dictionary~string,object~ InputData
        +Dictionary~string,object~ OutputData
        +string ErrorMessage
        +int RetryCount
        +Create(executionId, nodeId) NodeExecution
        +Start(inputData) void
        +Complete(outputData) void
        +Fail(error) void
    }
    
    class PluginDefinition {
        +Guid Id
        +string Name
        +string Version
        +string Description
        +string Author
        +PluginType Type
        +string AssemblyPath
        +Dictionary~string,object~ Configuration
        +List~NodeTypeDefinition~ NodeTypes
        +bool IsEnabled
        +DateTime InstalledAt
        +Load() IPlugin
        +Unload() void
        +Validate() ValidationResult
    }
    
    class NodeTypeDefinition {
        +string Id
        +string Name
        +string Category
        +string Description
        +string Icon
        +List~EndpointDefinition~ InputEndpoints
        +List~EndpointDefinition~ OutputEndpoints
        +Dictionary~string,object~ DefaultConfiguration
        +ConfigurationSchema ConfigSchema
        +Create(id, name) NodeTypeDefinition
        +CreateNode() WorkflowNode
    }
    
    %% 关联关系
    WorkflowDefinition ||--o{ WorkflowNode : contains
    WorkflowDefinition ||--o{ WorkflowConnection : contains
    WorkflowDefinition ||--o{ WorkflowExecution : executes
    WorkflowExecution ||--o{ NodeExecution : contains
    WorkflowNode }o--|| NodeTypeDefinition : instanceOf
    PluginDefinition ||--o{ NodeTypeDefinition : provides
    WorkflowConnection }o--|| WorkflowNode : connects
```

### 2.2 服务层类图
```mermaid
classDiagram
    class IWorkflowEngine {
        <<interface>>
        +CreateWorkflowAsync(definition) Task~WorkflowDefinition~
        +UpdateWorkflowAsync(id, definition) Task~WorkflowDefinition~
        +DeleteWorkflowAsync(id) Task
        +GetWorkflowAsync(id) Task~WorkflowDefinition~
        +ExecuteWorkflowAsync(id, parameters) Task~WorkflowExecution~
        +ValidateWorkflowAsync(definition) Task~ValidationResult~
    }
    
    class WorkflowEngine {
        -IWorkflowRepository _workflowRepository
        -IExecutionEngine _executionEngine
        -IValidationService _validationService
        -IEventBus _eventBus
        +CreateWorkflowAsync(definition) Task~WorkflowDefinition~
        +UpdateWorkflowAsync(id, definition) Task~WorkflowDefinition~
        +DeleteWorkflowAsync(id) Task
        +GetWorkflowAsync(id) Task~WorkflowDefinition~
        +ExecuteWorkflowAsync(id, parameters) Task~WorkflowExecution~
        +ValidateWorkflowAsync(definition) Task~ValidationResult~
    }
    
    class IExecutionEngine {
        <<interface>>
        +ExecuteAsync(workflow, parameters) Task~WorkflowExecution~
        +PauseExecutionAsync(executionId) Task
        +ResumeExecutionAsync(executionId) Task
        +StopExecutionAsync(executionId) Task
        +GetExecutionStatusAsync(executionId) Task~ExecutionStatus~
    }
    
    class ExecutionEngine {
        -INodeExecutionScheduler _scheduler
        -IPluginManager _pluginManager
        -IExecutionRepository _executionRepository
        -IEventBus _eventBus
        +ExecuteAsync(workflow, parameters) Task~WorkflowExecution~
        +PauseExecutionAsync(executionId) Task
        +ResumeExecutionAsync(executionId) Task
        +StopExecutionAsync(executionId) Task
        +GetExecutionStatusAsync(executionId) Task~ExecutionStatus~
    }
    
    class IPluginManager {
        <<interface>>
        +LoadPluginAsync(pluginPath) Task~PluginDefinition~
        +UnloadPluginAsync(pluginId) Task
        +GetPluginAsync(pluginId) Task~PluginDefinition~
        +GetNodeTypesAsync() Task~List~NodeTypeDefinition~~
        +CreateNodeExecutorAsync(nodeType) Task~INodeExecutor~
    }
    
    class PluginManager {
        -Dictionary~string,IPlugin~ _loadedPlugins
        -IPluginRepository _pluginRepository
        -ILogger _logger
        +LoadPluginAsync(pluginPath) Task~PluginDefinition~
        +UnloadPluginAsync(pluginId) Task
        +GetPluginAsync(pluginId) Task~PluginDefinition~
        +GetNodeTypesAsync() Task~List~NodeTypeDefinition~~
        +CreateNodeExecutorAsync(nodeType) Task~INodeExecutor~
    }
    
    class INodeExecutor {
        <<interface>>
        +ExecuteAsync(context) Task~NodeExecutionResult~
        +ValidateAsync(configuration) Task~ValidationResult~
        +GetConfigurationSchemaAsync() Task~ConfigurationSchema~
    }
    
    %% 实现关系
    WorkflowEngine ..|> IWorkflowEngine
    ExecutionEngine ..|> IExecutionEngine
    PluginManager ..|> IPluginManager
    
    %% 依赖关系
    WorkflowEngine --> IExecutionEngine
    WorkflowEngine --> IValidationService
    ExecutionEngine --> IPluginManager
    ExecutionEngine --> INodeExecutionScheduler
    PluginManager --> INodeExecutor
```

### 2.3 数据访问层类图
```mermaid
classDiagram
    class IRepository~T~ {
        <<interface>>
        +GetByIdAsync(id) Task~T~
        +GetAllAsync() Task~List~T~~
        +AddAsync(entity) Task~T~
        +UpdateAsync(entity) Task~T~
        +DeleteAsync(id) Task
        +ExistsAsync(id) Task~bool~
    }
    
    class IWorkflowRepository {
        <<interface>>
        +GetByNameAsync(name) Task~WorkflowDefinition~
        +GetByStatusAsync(status) Task~List~WorkflowDefinition~~
        +SearchAsync(criteria) Task~List~WorkflowDefinition~~
    }
    
    class WorkflowRepository {
        -FlowCustomDbContext _context
        -IMapper _mapper
        -ILogger _logger
        +GetByIdAsync(id) Task~WorkflowDefinition~
        +GetAllAsync() Task~List~WorkflowDefinition~~
        +AddAsync(workflow) Task~WorkflowDefinition~
        +UpdateAsync(workflow) Task~WorkflowDefinition~
        +DeleteAsync(id) Task
        +GetByNameAsync(name) Task~WorkflowDefinition~
        +GetByStatusAsync(status) Task~List~WorkflowDefinition~~
        +SearchAsync(criteria) Task~List~WorkflowDefinition~~
    }
    
    class IExecutionRepository {
        <<interface>>
        +GetByWorkflowIdAsync(workflowId) Task~List~WorkflowExecution~~
        +GetRunningExecutionsAsync() Task~List~WorkflowExecution~~
        +GetExecutionLogsAsync(executionId) Task~List~ExecutionLog~~
    }
    
    class ExecutionRepository {
        -FlowCustomDbContext _context
        -IMapper _mapper
        -ILogger _logger
        +GetByIdAsync(id) Task~WorkflowExecution~
        +GetAllAsync() Task~List~WorkflowExecution~~
        +AddAsync(execution) Task~WorkflowExecution~
        +UpdateAsync(execution) Task~WorkflowExecution~
        +GetByWorkflowIdAsync(workflowId) Task~List~WorkflowExecution~~
        +GetRunningExecutionsAsync() Task~List~WorkflowExecution~~
        +GetExecutionLogsAsync(executionId) Task~List~ExecutionLog~~
    }
    
    class FlowCustomDbContext {
        +DbSet~WorkflowDefinitionEntity~ Workflows
        +DbSet~WorkflowExecutionEntity~ Executions
        +DbSet~PluginDefinitionEntity~ Plugins
        +DbSet~ExecutionLogEntity~ ExecutionLogs
        +OnModelCreating(builder) void
        +SaveChangesAsync() Task~int~
    }
    
    %% 继承关系
    IWorkflowRepository --|> IRepository
    IExecutionRepository --|> IRepository
    WorkflowRepository ..|> IWorkflowRepository
    ExecutionRepository ..|> IExecutionRepository
    
    %% 依赖关系
    WorkflowRepository --> FlowCustomDbContext
    ExecutionRepository --> FlowCustomDbContext
```

## 🔄 3. 时序图

### 3.1 工作流创建时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 前端界面
    participant API as API控制器
    participant WE as 工作流引擎
    participant VS as 验证服务
    participant WR as 工作流仓储
    participant DB as 数据库
    participant EB as 事件总线
    
    U->>UI: 创建新工作流
    UI->>UI: 打开工作流设计器
    U->>UI: 拖拽节点到画布
    UI->>UI: 更新本地状态
    U->>UI: 配置节点属性
    UI->>UI: 验证节点配置
    U->>UI: 连接节点
    UI->>UI: 创建连接关系
    U->>UI: 保存工作流
    
    UI->>API: POST /api/workflows
    Note over UI,API: 发送工作流定义JSON
    
    API->>API: 验证请求参数
    API->>WE: CreateWorkflowAsync(definition)
    
    WE->>VS: ValidateWorkflowAsync(definition)
    VS->>VS: 验证节点配置
    VS->>VS: 验证连接关系
    VS->>VS: 验证循环依赖
    VS-->>WE: ValidationResult
    
    alt 验证失败
        WE-->>API: 返回验证错误
        API-->>UI: 400 Bad Request
        UI->>U: 显示错误信息
    else 验证成功
        WE->>WR: AddAsync(workflow)
        WR->>DB: INSERT INTO workflows
        DB-->>WR: 返回插入结果
        WR-->>WE: WorkflowDefinition
        
        WE->>EB: 发布WorkflowCreated事件
        EB->>EB: 异步处理事件
        
        WE-->>API: WorkflowDefinition
        API-->>UI: 201 Created
        UI->>U: 显示创建成功
    end
```

### 3.2 工作流执行时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 执行监控器
    participant API as API控制器
    participant WE as 工作流引擎
    participant EE as 执行引擎
    participant NES as 节点执行调度器
    participant PM as 插件管理器
    participant NE as 节点执行器
    participant ER as 执行仓储
    participant DB as 数据库
    participant NATS as NATS消息
    
    U->>UI: 点击执行按钮
    UI->>API: POST /api/workflows/{id}/execute
    Note over UI,API: 发送执行参数
    
    API->>WE: ExecuteWorkflowAsync(id, parameters)
    WE->>EE: ExecuteAsync(workflow, parameters)
    
    EE->>ER: CreateExecutionAsync(workflowId, parameters)
    ER->>DB: INSERT INTO executions
    DB-->>ER: 返回执行记录
    ER-->>EE: WorkflowExecution
    
    EE->>NATS: 发布ExecutionStarted事件
    NATS->>UI: 推送执行开始状态
    UI->>U: 显示执行开始
    
    EE->>NES: ScheduleNodesAsync(execution)
    
    loop 节点执行循环
        NES->>NES: 找到可执行节点
        NES->>PM: CreateNodeExecutorAsync(nodeType)
        PM-->>NES: INodeExecutor
        
        NES->>NE: ExecuteAsync(context)
        Note over NE: 执行节点逻辑
        NE-->>NES: NodeExecutionResult
        
        NES->>ER: UpdateNodeExecutionAsync(result)
        ER->>DB: UPDATE node_executions
        
        NES->>NATS: 发布NodeCompleted事件
        NATS->>UI: 推送节点状态更新
        UI->>U: 更新节点状态显示
        
        alt 节点执行失败
            NES->>NATS: 发布NodeFailed事件
            NATS->>UI: 推送失败状态
            UI->>U: 显示错误信息
        end
    end
    
    NES-->>EE: 所有节点执行完成
    EE->>ER: CompleteExecutionAsync(executionId)
    ER->>DB: UPDATE executions SET status='Completed'
    
    EE->>NATS: 发布ExecutionCompleted事件
    NATS->>UI: 推送执行完成状态
    UI->>U: 显示执行完成
    
    EE-->>WE: WorkflowExecution
    WE-->>API: WorkflowExecution
    API-->>UI: 200 OK
```

### 3.3 插件加载时序图
```mermaid
sequenceDiagram
    participant A as 管理员
    participant UI as 管理界面
    participant API as API控制器
    participant PM as 插件管理器
    participant PR as 插件仓储
    participant FS as 文件系统
    participant AL as 程序集加载器
    participant DB as 数据库
    
    A->>UI: 上传插件文件
    UI->>API: POST /api/plugins/upload
    Note over UI,API: 上传插件ZIP文件
    
    API->>FS: 保存插件文件
    FS-->>API: 文件路径
    
    API->>PM: LoadPluginAsync(pluginPath)
    
    PM->>FS: 解压插件文件
    FS-->>PM: 插件目录
    
    PM->>FS: 读取插件清单文件
    FS-->>PM: 插件元数据
    
    PM->>PM: 验证插件签名
    PM->>PM: 检查依赖关系
    
    alt 验证失败
        PM-->>API: 返回验证错误
        API-->>UI: 400 Bad Request
        UI->>A: 显示错误信息
    else 验证成功
        PM->>AL: 加载插件程序集
        AL-->>PM: 插件实例
        
        PM->>PM: 注册节点类型
        PM->>PR: SavePluginAsync(pluginDefinition)
        PR->>DB: INSERT INTO plugins
        DB-->>PR: 插入结果
        
        PM-->>API: PluginDefinition
        API-->>UI: 201 Created
        UI->>A: 显示加载成功
    end
```

## 📊 4. 业务流程图

### 4.1 工作流设计流程
```mermaid
flowchart TD
    A[开始设计工作流] --> B[创建新工作流]
    B --> C[设置工作流基本信息]
    C --> D[从节点面板拖拽节点]
    D --> E[配置节点属性]
    E --> F[连接节点]
    F --> G{是否需要添加更多节点?}
    G -->|是| D
    G -->|否| H[验证工作流完整性]
    H --> I{验证通过?}
    I -->|否| J[修复验证错误]
    J --> H
    I -->|是| K[保存工作流]
    K --> L[测试工作流]
    L --> M{测试通过?}
    M -->|否| N[调试和修复]
    N --> L
    M -->|是| O[发布工作流]
    O --> P[结束]
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style I fill:#fff3e0
    style M fill:#fff3e0
```

### 4.2 工作流执行流程
```mermaid
flowchart TD
    A[接收执行请求] --> B[验证执行权限]
    B --> C{权限验证通过?}
    C -->|否| D[返回权限错误]
    C -->|是| E[创建执行实例]
    E --> F[初始化执行上下文]
    F --> G[找到起始节点]
    G --> H[将起始节点加入执行队列]
    
    H --> I[从队列取出节点]
    I --> J[检查节点前置条件]
    J --> K{前置条件满足?}
    K -->|否| L[等待前置节点完成]
    L --> I
    K -->|是| M[执行节点]
    M --> N{执行成功?}
    N -->|否| O[记录错误日志]
    O --> P{是否需要重试?}
    P -->|是| Q[延迟后重新执行]
    Q --> M
    P -->|否| R[标记执行失败]
    
    N -->|是| S[记录执行结果]
    S --> T[找到后续节点]
    T --> U{有后续节点?}
    U -->|是| V[将后续节点加入队列]
    V --> W{队列是否为空?}
    W -->|否| I
    U -->|否| W
    W -->|是| X[标记执行完成]
    
    R --> Y[结束执行]
    X --> Y
    D --> Y
    
    style A fill:#e1f5fe
    style Y fill:#c8e6c9
    style R fill:#ffcdd2
    style X fill:#c8e6c9
```

### 4.3 插件管理流程
```mermaid
flowchart TD
    A[插件管理请求] --> B{操作类型}
    
    B -->|安装| C[上传插件文件]
    C --> D[验证插件格式]
    D --> E{格式正确?}
    E -->|否| F[返回格式错误]
    E -->|是| G[检查插件依赖]
    G --> H{依赖满足?}
    H -->|否| I[返回依赖错误]
    H -->|是| J[加载插件程序集]
    J --> K[注册节点类型]
    K --> L[保存插件信息]
    L --> M[插件安装成功]
    
    B -->|卸载| N[检查插件使用情况]
    N --> O{插件正在使用?}
    O -->|是| P[返回使用中错误]
    O -->|否| Q[卸载插件程序集]
    Q --> R[删除插件文件]
    R --> S[删除插件信息]
    S --> T[插件卸载成功]
    
    B -->|更新| U[检查版本兼容性]
    U --> V{版本兼容?}
    V -->|否| W[返回兼容性错误]
    V -->|是| X[备份当前版本]
    X --> Y[安装新版本]
    Y --> Z{安装成功?}
    Z -->|否| AA[恢复备份版本]
    AA --> BB[返回更新失败]
    Z -->|是| CC[删除备份]
    CC --> DD[插件更新成功]
    
    F --> EE[结束]
    I --> EE
    M --> EE
    P --> EE
    T --> EE
    W --> EE
    BB --> EE
    DD --> EE
    
    style A fill:#e1f5fe
    style EE fill:#c8e6c9
    style F fill:#ffcdd2
    style I fill:#ffcdd2
    style P fill:#ffcdd2
    style W fill:#ffcdd2
    style BB fill:#ffcdd2
```

## 🔄 5. 状态图

### 5.1 工作流状态图
```mermaid
stateDiagram-v2
    [*] --> Draft : 创建工作流
    
    Draft --> Draft : 编辑工作流
    Draft --> Published : 发布工作流
    Draft --> Deleted : 删除工作流
    
    Published --> Published : 查看工作流
    Published --> Archived : 归档工作流
    Published --> Draft : 创建新版本
    Published --> Executing : 执行工作流
    
    Executing --> Completed : 执行完成
    Executing --> Failed : 执行失败
    Executing --> Paused : 暂停执行
    Executing --> Stopped : 停止执行
    
    Paused --> Executing : 恢复执行
    Paused --> Stopped : 停止执行
    
    Completed --> Published : 返回已发布状态
    Failed --> Published : 返回已发布状态
    Stopped --> Published : 返回已发布状态
    
    Archived --> Published : 恢复工作流
    Archived --> Deleted : 删除工作流
    
    Deleted --> [*]
    
    note right of Draft
        草稿状态：
        - 可以编辑
        - 可以删除
        - 可以发布
    end note
    
    note right of Published
        已发布状态：
        - 只读
        - 可以执行
        - 可以归档
    end note
    
    note right of Executing
        执行中状态：
        - 不可编辑
        - 可以监控
        - 可以控制
    end note
```

### 5.2 节点执行状态图
```mermaid
stateDiagram-v2
    [*] --> Pending : 节点创建
    
    Pending --> Running : 开始执行
    Pending --> Skipped : 跳过执行
    
    Running --> Completed : 执行成功
    Running --> Failed : 执行失败
    Running --> Paused : 暂停执行
    
    Failed --> Running : 重试执行
    Failed --> Skipped : 跳过节点
    
    Paused --> Running : 恢复执行
    Paused --> Skipped : 跳过节点
    
    Completed --> [*]
    Skipped --> [*]
    
    note right of Pending
        等待状态：
        - 等待前置条件
        - 等待资源分配
    end note
    
    note right of Running
        运行状态：
        - 正在执行逻辑
        - 可以暂停
        - 可能失败
    end note
    
    note right of Failed
        失败状态：
        - 记录错误信息
        - 可以重试
        - 可以跳过
    end note
```

### 5.3 插件状态图
```mermaid
stateDiagram-v2
    [*] --> Installing : 开始安装
    
    Installing --> Installed : 安装成功
    Installing --> InstallFailed : 安装失败
    
    Installed --> Loading : 开始加载
    Installed --> Uninstalling : 开始卸载
    Installed --> Updating : 开始更新
    
    Loading --> Active : 加载成功
    Loading --> LoadFailed : 加载失败
    
    Active --> Unloading : 开始卸载
    Active --> Updating : 开始更新
    Active --> Disabled : 禁用插件
    
    Disabled --> Active : 启用插件
    Disabled --> Uninstalling : 开始卸载
    
    Unloading --> Uninstalling : 继续卸载
    Unloading --> LoadFailed : 卸载失败
    
    Uninstalling --> [*] : 卸载完成
    
    Updating --> Active : 更新成功
    Updating --> UpdateFailed : 更新失败
    
    InstallFailed --> [*]
    LoadFailed --> Installing : 重新安装
    UpdateFailed --> Active : 回滚版本
    
    note right of Active
        活跃状态：
        - 可以创建节点
        - 可以执行逻辑
        - 可以更新
    end note
    
    note right of Disabled
        禁用状态：
        - 不能创建节点
        - 不能执行
        - 可以启用
    end note
```

## 📋 6. 活动图

### 6.1 工作流执行活动图
```mermaid
flowchart TD
    A[开始执行] --> B[验证工作流]
    B --> C{工作流有效?}
    C -->|否| D[返回验证错误]
    C -->|是| E[创建执行上下文]
    
    E --> F[初始化变量]
    F --> G[找到起始节点]
    G --> H[创建节点执行任务]
    
    H --> I[并行执行节点]
    I --> J[等待节点完成]
    J --> K{所有节点完成?}
    K -->|否| L[检查可执行节点]
    L --> M{有可执行节点?}
    M -->|是| N[创建新执行任务]
    N --> I
    M -->|否| O[等待其他节点]
    O --> J
    
    K -->|是| P[收集执行结果]
    P --> Q{执行成功?}
    Q -->|是| R[标记执行成功]
    Q -->|否| S[标记执行失败]
    
    R --> T[清理资源]
    S --> T
    T --> U[发送完成通知]
    U --> V[结束执行]
    
    D --> V
    
    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style D fill:#ffcdd2
    style S fill:#ffcdd2
    style R fill:#c8e6c9
```

### 6.2 节点配置活动图
```mermaid
flowchart TD
    A[选择节点类型] --> B[加载节点模板]
    B --> C[显示配置表单]
    C --> D[用户输入配置]
    D --> E[实时验证配置]
    E --> F{配置有效?}
    F -->|否| G[显示错误提示]
    G --> D
    F -->|是| H[预览配置效果]
    H --> I[用户确认配置]
    I --> J{用户确认?}
    J -->|否| D
    J -->|是| K[保存节点配置]
    K --> L[更新工作流定义]
    L --> M[验证工作流完整性]
    M --> N{工作流有效?}
    N -->|否| O[显示工作流错误]
    O --> P[回滚配置更改]
    P --> D
    N -->|是| Q[配置保存成功]
    Q --> R[结束配置]
    
    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style G fill:#fff3e0
    style O fill:#ffcdd2
```

## 🎯 7. 总结

本系统分析图表文档提供了FlowCustomV1工作流系统的完整视图：

### 7.1 功能覆盖
- **用例图**: 展示了系统的所有功能用例和参与者关系
- **类图**: 详细描述了系统的静态结构和类之间的关系
- **时序图**: 展示了关键业务流程的交互序列
- **流程图**: 描述了核心业务流程的执行逻辑
- **状态图**: 展示了关键实体的状态转换
- **活动图**: 描述了复杂业务活动的执行流程

### 7.2 设计特点
1. **清晰的分层架构**: 从用户界面到数据访问的完整分层
2. **松耦合设计**: 通过接口和事件实现模块间的松耦合
3. **可扩展性**: 插件化架构支持功能扩展
4. **高并发支持**: 异步执行和并行处理设计
5. **完整的状态管理**: 详细的状态转换和生命周期管理

### 7.3 实施指导
这些图表为系统开发提供了详细的设计指导，开发团队可以基于这些图表：
- 理解系统整体架构
- 实现各个模块的详细设计
- 进行代码开发和测试
- 进行系统集成和部署

---

*本文档遵循UML 2.5标准和软件工程最佳实践编写。*