[2025-07-31 19:11:36.855 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\debug
[2025-07-31 19:11:36.864 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\info
[2025-07-31 19:11:36.867 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\warn
[2025-07-31 19:11:36.870 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\error
[2025-07-31 19:11:36.873 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\trace
[2025-07-31 19:11:36.876 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 19:14:02.241 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 19:14:02.288 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 19:14:02.293 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 19:14:02.298 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 19:14:02.300 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 19:14:02.303 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 19:14:02.305 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 19:14:02.306 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 19:14:02.309 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 19:14:02.311 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 19:14:02.312 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 19:14:02.316 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 19:14:02.317 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 19:14:02.319 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 19:14:02.320 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 19:14:02.336 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 19:14:02.344 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 19:14:02.346 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 19:14:02.347 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 19:14:02.354 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 19:14:02.356 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 19:14:02.356 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 19:14:02.362 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 19:14:02.365 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 19:14:02.366 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,295,608 字节
[2025-07-31 19:14:02.367 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 19:14:02.369 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 19:14:02.371 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 19:14:02.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Master", NodeId: master-001
[2025-07-31 19:14:02.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Master", 节点ID: master-001
[2025-07-31 19:14:02.382 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 1/10)
[2025-07-31 19:14:03.134 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:14:03.138 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:14:03.141 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:14:03.144 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:14:03.147 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:14:03.384 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 2/10)
[2025-07-31 19:14:03.445 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 19:14:03.448 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 19:14:03.521 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 19:14:03.525 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 19:14:03.527 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 19:14:03.666 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:14:03.681 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:14:03.717 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 19:14:03.721 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 19:14:03.978 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 19:14:03.988 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 19:14:03.994 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 19:14:03.997 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 19:14:04.000 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 19:14:04.005 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 19:14:04.179 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 19:14:04.181 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 19:14:04.187 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 19:14:04.189 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 19:14:04.192 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 19:14:04.196 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 19:14:04.199 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 19:14:04.202 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 19:14:04.211 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 19:14:04.215 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 19:14:04.220 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 19:14:04.224 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 19:14:04.229 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 19:14:04.232 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 19:14:04.237 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 19:14:04.242 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 19:14:04.246 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 19:14:04.252 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 19:14:04.273 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 19:14:04.278 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 19:14:04.282 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 19:14:04.289 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 19:14:04.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 19:14:04.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 19:14:04.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 19:14:04.307 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 19:14:04.311 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 19:14:04.314 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 19:14:04.317 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 19:14:04.320 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 19:14:04.323 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 19:14:04.325 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 19:14:04.337 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 19:14:04.344 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 19:14:04.347 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 19:14:04.350 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 19:14:04.352 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:14:04.355 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:14:04.357 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 19:14:04.359 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 19:14:04.361 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 19:14:04.369 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 19:14:04.372 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 19:14:04.374 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 19:14:04.380 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:14:04.392 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 3/10)
[2025-07-31 19:14:04.520 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:14:04.526 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:14:04.528 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:14:04.530 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:14:04.533 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 19:14:04.535 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 19:14:04.543 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 19:14:04.546 +08:00 INF] Program: 后台初始化完成
[2025-07-31 19:14:05.397 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.heartbeat
[2025-07-31 19:14:05.400 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.events
[2025-07-31 19:14:05.402 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ NATS订阅初始化成功
[2025-07-31 19:14:05.428 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:14:05.428 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: master-001
[2025-07-31 19:14:05.432 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 19:14:05.436 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 19:14:05.438 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 19:14:05.476 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 19:14:05.478 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 19:14:07.361 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 19:14:12.348 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 19:14:32.292 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 19:14:34.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:14:35.413 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:14:53.743 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:14:53.750 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker"
[2025-07-31 19:15:04.306 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:15:05.413 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:15:23.743 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:15:23.747 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker"
[2025-07-31 19:15:34.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:15:35.407 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:15:45.653 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔍 集群状态查询 - 节点字典大小: 0, 节点列表: []
[2025-07-31 19:15:45.672 +08:00 INF] Program: 请求完成: GET /api/cluster/status - 200 - 28ms
[2025-07-31 19:15:53.739 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:15:53.741 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker"
[2025-07-31 19:16:04.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:16:05.405 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
[2025-07-31 19:16:23.733 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: worker-001 <- cluster.heartbeat
[2025-07-31 19:16:23.735 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: worker-001, 模式: "Worker"
[2025-07-31 19:16:34.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:16:35.415 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: master-001 -> cluster.heartbeat
