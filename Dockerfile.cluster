# FlowCustomV1 集群节点 Dockerfile
# 支持Master和Worker角色的统一镜像

# 构建阶段
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.Cluster/FlowCustomV1.Cluster.csproj", "src/FlowCustomV1.Cluster/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制所有源代码
COPY . .

# 构建前端
WORKDIR /src/frontend
RUN npm ci --only=production
RUN npm run build

# 构建后端
WORKDIR /src
RUN dotnet build "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj" -c Release -o /app/build

# 发布阶段
FROM build AS publish
RUN dotnet publish "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 运行时阶段
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/*

# 复制发布的应用
COPY --from=publish /app/publish .

# 复制前端构建结果
COPY --from=build /src/frontend/dist ./wwwroot

# 创建数据目录
RUN mkdir -p /app/data /app/logs

# 设置环境变量
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5279
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5279/health || exit 1

# 暴露端口
EXPOSE 5279

# 设置用户权限
RUN groupadd -r flowcustom && useradd -r -g flowcustom flowcustom
RUN chown -R flowcustom:flowcustom /app
USER flowcustom

# 启动应用
ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll"]
