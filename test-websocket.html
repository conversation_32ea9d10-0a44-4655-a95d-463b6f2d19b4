<!DOCTYPE html>
<html>
<head>
    <title>NATS WebSocket Test</title>
</head>
<body>
    <h1>NATS WebSocket Connection Test</h1>
    <div id="status">正在测试...</div>
    <div id="log"></div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        // 测试WebSocket连接
        addLog('开始测试WebSocket连接到 ws://localhost:8081');
        
        const ws = new WebSocket('ws://localhost:8081');
        
        ws.onopen = function(event) {
            addLog('✅ WebSocket连接成功！');
            status.textContent = '连接成功';
            status.style.color = 'green';
        };
        
        ws.onerror = function(error) {
            addLog('❌ WebSocket连接错误: ' + error);
            status.textContent = '连接失败';
            status.style.color = 'red';
        };
        
        ws.onclose = function(event) {
            addLog(`🔌 WebSocket连接关闭: 代码=${event.code}, 原因="${event.reason}"`);
            status.textContent = '连接关闭';
            status.style.color = 'orange';
        };
        
        ws.onmessage = function(event) {
            addLog('📨 收到消息: ' + event.data);
        };
    </script>
</body>
</html>
