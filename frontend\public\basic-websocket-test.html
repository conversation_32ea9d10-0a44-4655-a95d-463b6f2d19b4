<!DOCTYPE html>
<html>
<head>
    <title>Basic WebSocket Test</title>
</head>
<body>
    <h1>Basic WebSocket Connection Test</h1>
    <div>状态: <span id="status">准备中...</span></div>
    <div id="log" style="margin-top: 20px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;"></div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            log.appendChild(div);
            console.log(message);
        }

        // 测试基础WebSocket连接
        function testBasicWebSocket() {
            addLog('🔌 开始测试基础WebSocket连接到 ws://localhost:8081');
            status.textContent = '正在连接...';
            status.style.color = 'orange';
            
            try {
                // 创建WebSocket连接
                const ws = new WebSocket('ws://localhost:8081');
                
                ws.onopen = function(event) {
                    addLog('✅ WebSocket连接成功！');
                    addLog('协议: ' + ws.protocol);
                    addLog('就绪状态: ' + ws.readyState);
                    addLog('URL: ' + ws.url);
                    status.textContent = '连接成功';
                    status.style.color = 'green';
                    
                    // 尝试发送一个简单的消息
                    setTimeout(() => {
                        addLog('📤 发送测试消息...');
                        ws.send('Hello WebSocket!');
                    }, 1000);
                };
                
                ws.onerror = function(error) {
                    addLog('❌ WebSocket连接错误: ' + error);
                    addLog('错误类型: ' + error.type);
                    addLog('错误对象: ' + JSON.stringify(error));
                    status.textContent = '连接失败';
                    status.style.color = 'red';
                };
                
                ws.onclose = function(event) {
                    addLog(`🔌 WebSocket连接关闭:`);
                    addLog(`  代码: ${event.code}`);
                    addLog(`  原因: "${event.reason}"`);
                    addLog(`  干净关闭: ${event.wasClean}`);
                    status.textContent = '连接关闭';
                    status.style.color = 'orange';
                };
                
                ws.onmessage = function(event) {
                    addLog('📨 收到WebSocket消息: ' + event.data);
                };
                
            } catch (error) {
                addLog('❌ 创建WebSocket时发生错误: ' + error.message);
                addLog('错误堆栈: ' + error.stack);
                status.textContent = '创建失败';
                status.style.color = 'red';
            }
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testBasicWebSocket);
    </script>
</body>
</html>
