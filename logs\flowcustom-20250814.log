[2025-08-14 10:55:52.790 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-08-14 10:55:52.899 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-08-14 10:55:52.905 +00:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-08-14 10:55:52.915 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-08-14 10:55:52.919 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-08-14 10:55:52.921 +00:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-08-14 10:55:52.923 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-08-14 10:55:52.924 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-08-14 10:55:52.924 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-08-14 10:55:52.926 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-08-14 10:55:52.927 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-08-14 10:55:52.933 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-08-14 10:55:52.934 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-08-14 10:55:52.936 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-08-14 10:55:52.937 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-08-14 10:55:52.964 +00:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-08-14 10:55:52.978 +00:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00 %-50.00 %-80.00 %], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-08-14 10:55:52.982 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-08-14 10:55:52.987 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-08-14 10:55:53.002 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,063,424 字节
[2025-08-14 10:55:53.006 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-08-14 10:55:53.012 +00:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: false, NodeMode: "Standalone", NodeId: 06777577e9fc
[2025-08-14 10:55:53.015 +00:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群模式未启用，运行在独立模式
[2025-08-14 10:55:53.017 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-08-14 10:55:53.020 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-08-14 10:55:53.029 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/debug
[2025-08-14 10:55:53.035 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/info
[2025-08-14 10:55:53.041 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/warn
[2025-08-14 10:55:53.046 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/error
[2025-08-14 10:55:53.052 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/trace
[2025-08-14 10:55:53.054 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-08-14 10:55:53.055 +00:00 ERR] Program: 后台初始化失败
MySqlConnector.MySqlException (0x80004005): Unable to connect to any of the specified MySQL hosts.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1063
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at FlowCustomV1.Data.Providers.MySqlProvider.ConfigureDbContext(DbContextOptionsBuilder builder, String connectionString) in /src/src/FlowCustomV1.Data/Providers/MySqlProvider.cs:line 17
   at Program.<>c__DisplayClass0_0.<<Main>$>b__23(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in /src/src/FlowCustomV1.Api/Program.cs:line 120
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__12>d.MoveNext() in /src/src/FlowCustomV1.Api/Program.cs:line 355
[2025-08-14 10:55:53.264 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-08-14 10:55:53.267 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-08-14 10:55:53.269 +00:00 WRN] Microsoft.AspNetCore.Hosting.Diagnostics: Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:80'.
[2025-08-14 10:55:57.930 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已停止
[2025-08-14 10:55:57.932 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 正在停止日志清理服务...
[2025-08-14 10:55:57.933 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-08-14 10:55:57.935 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-08-14 10:55:57.938 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已停止，监控时长: "00:00:04.9356914", 最终内存使用: 10,345,224 字节, 峰值: 8,063,424 字节
[2025-08-14 10:55:57.939 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已停止
[2025-08-14 10:55:57.946 +00:00 INF] FlowCustomV1.Engine.ObjectPool.NodeExecutionContextFactory: NodeExecutionContextFactory已释放 - 总创建: 0, 总返回: 0, 活跃实例: 0
[2025-08-14 10:55:57.947 +00:00 INF] FlowCustomV1.Engine.ObjectPool.NodeExecutionContextPool: NodeExecutionContext对象池已释放 - 总创建: 0, 命中率: 0.00 %, 重置成功率: 0.00 %
[2025-08-14 10:55:57.948 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控器已释放
[2025-08-14 10:55:57.949 +00:00 INF] FlowCustomV1.Engine.ObjectPool.NodeExecutionContextFactory: NodeExecutionContextFactory已释放 - 总创建: 0, 总返回: 0, 活跃实例: 0
[2025-08-14 10:55:57.949 +00:00 INF] FlowCustomV1.Engine.ObjectPool.NodeExecutionContextPool: NodeExecutionContext对象池已释放 - 总创建: 0, 命中率: 0.00 %, 重置成功率: 0.00 %
[2025-08-14 10:55:57.953 +00:00 INF] FlowCustomV1.Api.Services.NatsService: 正在停止NATS服务...
[2025-08-14 10:55:57.954 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS服务已停止
[2025-08-14 10:55:57.954 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已释放
