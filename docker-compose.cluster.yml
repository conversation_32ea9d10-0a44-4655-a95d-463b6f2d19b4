version: '3.8'

services:
  # NATS JetStream 集群
  nats-1:
    image: nats:2.10-alpine
    container_name: flowcustom-nats-1
    command: ["nats-server", "--config", "/etc/nats/nats.conf"]
    volumes:
      - ./config/nats/nats-1.conf:/etc/nats/nats.conf
      - nats1_data:/data/jetstream
    ports:
      - "4222:4222"
      - "8222:8222"
      - "6222:6222"
    networks:
      - flowcustom-cluster
    healthcheck:
      test: ["CMD", "nats", "server", "check", "--server", "nats://localhost:4222"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  nats-2:
    image: nats:2.10-alpine
    container_name: flowcustom-nats-2
    command: ["nats-server", "--config", "/etc/nats/nats.conf"]
    volumes:
      - ./config/nats/nats-2.conf:/etc/nats/nats.conf
      - nats2_data:/data/jetstream
    ports:
      - "4223:4222"
      - "8223:8222"
      - "6223:6222"
    networks:
      - flowcustom-cluster
    depends_on:
      - nats-1
    healthcheck:
      test: ["CMD", "nats", "server", "check", "--server", "nats://localhost:4222"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 15s

  nats-3:
    image: nats:2.10-alpine
    container_name: flowcustom-nats-3
    command: ["nats-server", "--config", "/etc/nats/nats.conf"]
    volumes:
      - ./config/nats/nats-3.conf:/etc/nats/nats.conf
      - nats3_data:/data/jetstream
    ports:
      - "4224:4222"
      - "8224:8222"
      - "6224:6222"
    networks:
      - flowcustom-cluster
    depends_on:
      - nats-1
      - nats-2
    healthcheck:
      test: ["CMD", "nats", "server", "check", "--server", "nats://localhost:4222"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: flowcustom-mysql
    environment:
      MYSQL_ROOT_PASSWORD: flowcustom123
      MYSQL_DATABASE: flowcustom
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: flowcustom123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - flowcustom-cluster
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pflowcustom123"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: flowcustom-redis
    command: redis-server --appendonly yes --requirepass flowcustom123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - flowcustom-cluster
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # FlowCustom Master+Worker 节点 1
  flowcustom-master-1:
    build:
      context: .
      dockerfile: Dockerfile.cluster
    container_name: flowcustom-master-1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5279
      - NodeConfiguration__NodeId=master-1
      - NodeConfiguration__EnableMasterRole=true
      - NodeConfiguration__EnableWorkerRole=true
      - NodeConfiguration__ClusterName=flowcustom-production
      - ConnectionStrings__NATS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom;Uid=flowcustom;Pwd=flowcustom123;
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5279:5279"
    networks:
      - flowcustom-cluster
    depends_on:
      nats-1:
        condition: service_healthy
      nats-2:
        condition: service_healthy
      nats-3:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5279/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # FlowCustom Master+Worker 节点 2
  flowcustom-master-2:
    build:
      context: .
      dockerfile: Dockerfile.cluster
    container_name: flowcustom-master-2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5279
      - NodeConfiguration__NodeId=master-2
      - NodeConfiguration__EnableMasterRole=true
      - NodeConfiguration__EnableWorkerRole=true
      - NodeConfiguration__ClusterName=flowcustom-production
      - ConnectionStrings__NATS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom;Uid=flowcustom;Pwd=flowcustom123;
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5280:5279"
    networks:
      - flowcustom-cluster
    depends_on:
      nats-1:
        condition: service_healthy
      nats-2:
        condition: service_healthy
      nats-3:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5279/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # FlowCustom 纯Worker节点 1
  flowcustom-worker-1:
    build:
      context: .
      dockerfile: Dockerfile.cluster
    container_name: flowcustom-worker-1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - NodeConfiguration__NodeId=worker-1
      - NodeConfiguration__EnableMasterRole=false
      - NodeConfiguration__EnableWorkerRole=true
      - NodeConfiguration__ClusterName=flowcustom-production
      - NodeConfiguration__Worker__WorkerType=general
      - NodeConfiguration__Worker__MaxConcurrentExecutions=10
      - ConnectionStrings__NATS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom;Uid=flowcustom;Pwd=flowcustom123;
      - ConnectionStrings__Redis=redis:6379
    networks:
      - flowcustom-cluster
    depends_on:
      nats-1:
        condition: service_healthy
      nats-2:
        condition: service_healthy
      nats-3:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 3

  # FlowCustom 纯Worker节点 2 (高性能)
  flowcustom-worker-2:
    build:
      context: .
      dockerfile: Dockerfile.cluster
    container_name: flowcustom-worker-2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - NodeConfiguration__NodeId=worker-2
      - NodeConfiguration__EnableMasterRole=false
      - NodeConfiguration__EnableWorkerRole=true
      - NodeConfiguration__ClusterName=flowcustom-production
      - NodeConfiguration__Worker__WorkerType=high-performance
      - NodeConfiguration__Worker__MaxConcurrentExecutions=20
      - ConnectionStrings__NATS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom;Uid=flowcustom;Pwd=flowcustom123;
      - ConnectionStrings__Redis=redis:6379
    networks:
      - flowcustom-cluster
    depends_on:
      nats-1:
        condition: service_healthy
      nats-2:
        condition: service_healthy
      nats-3:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Nginx 负载均衡器
  nginx:
    image: nginx:alpine
    container_name: flowcustom-nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
    ports:
      - "80:80"
      - "443:443"
    networks:
      - flowcustom-cluster
    depends_on:
      - flowcustom-master-1
      - flowcustom-master-2
    restart: unless-stopped

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: flowcustom-prometheus
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - flowcustom-cluster
    restart: unless-stopped

  # Grafana 仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: flowcustom-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    networks:
      - flowcustom-cluster
    depends_on:
      - prometheus
    restart: unless-stopped

networks:
  flowcustom-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  nats1_data:
    driver: local
  nats2_data:
    driver: local
  nats3_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
