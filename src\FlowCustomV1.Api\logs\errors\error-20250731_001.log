[2025-07-31 16:04:15.345 +08:00 ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5000: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.DefaultAddressStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-31 16:06:23.676 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:06:23.696 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 16:09:36.361 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:09:36.396 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
