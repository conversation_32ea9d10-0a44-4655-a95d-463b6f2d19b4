# FlowCustomV1 V2.0 详细设计规格说明书

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| 文档名称 | FlowCustomV1 V2.0 详细设计规格说明书 |
| 文档版本 | v2.0.0 |
| CMMI等级 | Level 5 (优化级) |
| 创建日期 | 2025-01-27 |
| 最后更新 | 2025-01-27 |
| 作者 | FlowCustomV1 架构团队 |
| 审核者 | 技术委员会 |
| 批准者 | 项目总监 |
| 状态 | 待审核 |

## 🎯 1. 引言

### 1.1 文档目的
本文档按照CMMI Level 5标准，详细描述FlowCustomV1 V2.0工作流管理系统的架构设计、模块设计、接口设计、数据设计和部署设计，为系统开发、测试、部署和维护提供完整的技术规范。

### 1.2 文档范围
- 系统整体架构设计
- 各子系统详细设计
- 接口规范定义
- 数据模型设计
- 安全设计规范
- 性能设计要求
- 部署架构设计
- 质量保证措施

### 1.3 预期读者
- 系统架构师
- 开发工程师
- 测试工程师
- 运维工程师
- 项目管理人员

### 1.4 参考文档
- 《FlowCustomV1 需求规格说明书》
- 《FlowCustomV1 系统架构设计文档》
- 《CMMI Level 5 开发标准》
- 《企业级软件架构最佳实践》

## 🏗️ 2. 系统总体设计

### 2.1 设计原则

#### 2.1.1 SOLID原则
- **单一职责原则(SRP)**: 每个类只有一个引起变化的原因
- **开闭原则(OCP)**: 对扩展开放，对修改关闭
- **里氏替换原则(LSP)**: 子类必须能够替换其基类
- **接口隔离原则(ISP)**: 不应强迫客户依赖不需要的接口
- **依赖倒置原则(DIP)**: 高层模块不应依赖低层模块

#### 2.1.2 架构原则
- **分层架构**: 清晰的层次划分，降低耦合
- **微服务就绪**: 支持未来微服务拆分
- **事件驱动**: 基于事件的异步通信
- **领域驱动**: 以业务领域为核心的设计
- **云原生**: 支持容器化和云部署

#### 2.1.3 质量属性
- **可用性**: 99.9%系统可用性
- **性能**: API响应时间P95 < 100ms
- **可扩展性**: 支持水平扩展
- **安全性**: 多层安全防护
- **可维护性**: 代码覆盖率 > 90%

### 2.2 技术架构

#### 2.2.1 技术栈选型

**前端技术栈**
```typescript
// 核心框架
React: "^18.2.0"
TypeScript: "^5.0.0"
Vite: "^5.0.0"

// 状态管理
Zustand: "^4.4.0"

// UI组件
ReactFlow: "^12.0.0"
Tailwind CSS: "^3.3.0"
Headless UI: "^1.7.0"

// 工具库
Axios: "^1.6.0"
React Query: "^4.0.0"
React Hook Form: "^7.45.0"
Zod: "^3.22.0"
```

**后端技术栈**
```csharp
// 核心框架
.NET: "8.0"
ASP.NET Core: "8.0"
Entity Framework Core: "8.0"

// 消息系统
NATS: "^2.10.0"

// 数据库
PostgreSQL: "^16.0"
Redis: "^7.2.0"

// 监控和日志
Serilog: "^3.1.0"
OpenTelemetry: "^1.6.0"
Prometheus: "^8.0.0"

// 测试框架
xUnit: "^2.4.0"
Moq: "^4.20.0"
Testcontainers: "^3.5.0"
```

#### 2.2.2 系统架构图

```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        WEB[Web浏览器]
        MOBILE[移动端应用]
        API_CLIENT[API客户端]
    end
    
    subgraph "网关层 (Gateway Layer)"
        API_GW[API网关<br/>Kong/Nginx]
        LB[负载均衡器<br/>HAProxy]
    end
    
    subgraph "应用层 (Application Layer)"
        WF_API[工作流API服务<br/>.NET 8]
        EXEC_API[执行API服务<br/>.NET 8]
        PLUGIN_API[插件API服务<br/>.NET 8]
        MONITOR_API[监控API服务<br/>.NET 8]
    end
    
    subgraph "业务层 (Business Layer)"
        WF_ENGINE[工作流引擎<br/>WorkflowEngine]
        EXEC_ENGINE[执行引擎<br/>ExecutionEngine]
        PLUGIN_MGR[插件管理器<br/>PluginManager]
        EVENT_BUS[事件总线<br/>EventBus]
    end
    
    subgraph "数据层 (Data Layer)"
        POSTGRES[(PostgreSQL<br/>主数据库)]
        REDIS[(Redis<br/>缓存)]
        NATS[NATS<br/>消息队列]
        STORAGE[对象存储<br/>MinIO/S3]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        MONITOR[监控系统<br/>Prometheus+Grafana]
        LOG[日志系统<br/>ELK Stack]
        CONFIG[配置中心<br/>Consul]
        REGISTRY[服务注册<br/>Consul]
    end
```

### 2.3 部署架构

#### 2.3.1 容器化架构
```yaml
# Docker Compose 架构
version: '3.8'
services:
  # 前端服务
  frontend:
    image: flowcustom/frontend:v2.0.0
    ports: ["80:80"]
    
  # API网关
  gateway:
    image: kong:3.4
    ports: ["8000:8000", "8001:8001"]
    
  # 后端服务
  workflow-api:
    image: flowcustom/workflow-api:v2.0.0
    replicas: 3
    
  execution-api:
    image: flowcustom/execution-api:v2.0.0
    replicas: 3
    
  # 数据服务
  postgres:
    image: postgres:16
    volumes: ["postgres_data:/var/lib/postgresql/data"]
    
  redis:
    image: redis:7.2-alpine
    
  nats:
    image: nats:2.10-alpine
```

## 🔧 3. 详细模块设计

### 3.1 工作流引擎模块

#### 3.1.1 模块概述
工作流引擎是系统的核心模块，负责工作流的定义、验证、执行和监控。

#### 3.1.2 类设计

**WorkflowEngine 类**
```csharp
/// <summary>
/// 工作流引擎核心类
/// 职责：工作流生命周期管理
/// </summary>
public sealed class WorkflowEngine : IWorkflowEngine, IDisposable
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IExecutionEngine _executionEngine;
    private readonly IEventBus _eventBus;
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly WorkflowEngineOptions _options;
    
    /// <summary>
    /// 创建工作流
    /// </summary>
    /// <param name="definition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的工作流</returns>
    public async Task<Result<Workflow>> CreateWorkflowAsync(
        WorkflowDefinition definition,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public async Task<ValidationResult> ValidateWorkflowAsync(
        Guid workflowId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="input">输入参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public async Task<Result<WorkflowExecution>> ExecuteWorkflowAsync(
        Guid workflowId,
        Dictionary<string, object> input,
        CancellationToken cancellationToken = default);
}
```

**WorkflowDefinition 值对象**
```csharp
/// <summary>
/// 工作流定义值对象
/// 不可变对象，包含工作流的完整定义
/// </summary>
public sealed record WorkflowDefinition
{
    public required string Name { get; init; }
    public required string Description { get; init; }
    public required string Version { get; init; }
    public required IReadOnlyList<NodeDefinition> Nodes { get; init; }
    public required IReadOnlyList<ConnectionDefinition> Connections { get; init; }
    public required WorkflowMetadata Metadata { get; init; }
    
    /// <summary>
    /// 验证工作流定义的完整性
    /// </summary>
    public ValidationResult Validate()
    {
        var errors = new List<string>();
        
        // 验证节点
        if (!Nodes.Any())
            errors.Add("工作流必须包含至少一个节点");
            
        // 验证连接
        foreach (var connection in Connections)
        {
            if (!Nodes.Any(n => n.Id == connection.SourceNodeId))
                errors.Add($"连接源节点不存在: {connection.SourceNodeId}");
                
            if (!Nodes.Any(n => n.Id == connection.TargetNodeId))
                errors.Add($"连接目标节点不存在: {connection.TargetNodeId}");
        }
        
        // 验证循环依赖
        var cycleDetector = new CycleDetector();
        if (cycleDetector.HasCycle(Nodes, Connections))
            errors.Add("工作流存在循环依赖");
            
        return new ValidationResult(errors);
    }
}
```

#### 3.1.3 接口设计

**IWorkflowEngine 接口**
```csharp
/// <summary>
/// 工作流引擎接口
/// 定义工作流引擎的核心操作
/// </summary>
public interface IWorkflowEngine
{
    /// <summary>
    /// 创建工作流
    /// </summary>
    Task<Result<Workflow>> CreateWorkflowAsync(
        WorkflowDefinition definition,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取工作流
    /// </summary>
    Task<Result<Workflow>> GetWorkflowAsync(
        Guid workflowId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新工作流
    /// </summary>
    Task<Result<Workflow>> UpdateWorkflowAsync(
        Guid workflowId,
        WorkflowDefinition definition,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除工作流
    /// </summary>
    Task<Result> DeleteWorkflowAsync(
        Guid workflowId,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 执行工作流
    /// </summary>
    Task<Result<WorkflowExecution>> ExecuteWorkflowAsync(
        Guid workflowId,
        Dictionary<string, object> input,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证工作流
    /// </summary>
    Task<ValidationResult> ValidateWorkflowAsync(
        Guid workflowId,
        CancellationToken cancellationToken = default);
}
```

### 3.2 执行引擎模块

#### 3.2.1 模块概述
执行引擎负责工作流的实际执行，包括节点调度、状态管理、错误处理和性能监控。

#### 3.2.2 类设计

**ExecutionEngine 类**
```csharp
/// <summary>
/// 执行引擎核心类
/// 职责：工作流执行调度和状态管理
/// </summary>
public sealed class ExecutionEngine : IExecutionEngine
{
    private readonly INodeExecutor _nodeExecutor;
    private readonly IExecutionRepository _executionRepository;
    private readonly IEventBus _eventBus;
    private readonly IConcurrencyController _concurrencyController;
    private readonly ILogger<ExecutionEngine> _logger;
    
    /// <summary>
    /// 执行工作流
    /// </summary>
    public async Task<Result<WorkflowExecution>> ExecuteAsync(
        WorkflowDefinition workflow,
        Dictionary<string, object> input,
        CancellationToken cancellationToken = default)
    {
        // 创建执行上下文
        var context = new ExecutionContext(workflow, input);
        
        // 创建执行记录
        var execution = await CreateExecutionRecordAsync(context);
        
        try
        {
            // 发布执行开始事件
            await _eventBus.PublishAsync(new WorkflowExecutionStartedEvent(execution.Id));
            
            // 执行工作流
            await ExecuteWorkflowInternalAsync(context, cancellationToken);
            
            // 更新执行状态
            execution.Complete();
            await _executionRepository.UpdateAsync(execution);
            
            // 发布执行完成事件
            await _eventBus.PublishAsync(new WorkflowExecutionCompletedEvent(execution.Id));
            
            return Result.Success(execution);
        }
        catch (Exception ex)
        {
            // 处理执行异常
            execution.Fail(ex.Message);
            await _executionRepository.UpdateAsync(execution);
            
            // 发布执行失败事件
            await _eventBus.PublishAsync(new WorkflowExecutionFailedEvent(execution.Id, ex));
            
            return Result.Failure<WorkflowExecution>(ex.Message);
        }
    }
    
    /// <summary>
    /// 内部执行逻辑
    /// </summary>
    private async Task ExecuteWorkflowInternalAsync(
        ExecutionContext context,
        CancellationToken cancellationToken)
    {
        // 构建执行图
        var executionGraph = new ExecutionGraph(context.Workflow);
        
        // 获取起始节点
        var startNodes = executionGraph.GetStartNodes();
        
        // 并发执行起始节点
        var tasks = startNodes.Select(node => 
            ExecuteNodeAsync(node, context, cancellationToken));
            
        await Task.WhenAll(tasks);
    }
    
    /// <summary>
    /// 执行单个节点
    /// </summary>
    private async Task ExecuteNodeAsync(
        NodeDefinition node,
        ExecutionContext context,
        CancellationToken cancellationToken)
    {
        // 并发控制
        await _concurrencyController.AcquireAsync(cancellationToken);
        
        try
        {
            // 执行节点
            var result = await _nodeExecutor.ExecuteAsync(node, context, cancellationToken);
            
            // 更新上下文
            context.UpdateNodeResult(node.Id, result);
            
            // 触发后续节点
            var nextNodes = context.ExecutionGraph.GetNextNodes(node.Id);
            var readyNodes = nextNodes.Where(n => context.IsNodeReady(n.Id));
            
            // 递归执行后续节点
            var nextTasks = readyNodes.Select(nextNode => 
                ExecuteNodeAsync(nextNode, context, cancellationToken));
                
            await Task.WhenAll(nextTasks);
        }
        finally
        {
            _concurrencyController.Release();
        }
    }
}
```

### 3.3 插件管理模块

#### 3.3.1 模块概述
插件管理模块提供统一的插件加载、管理和执行机制，支持多种插件类型。

#### 3.3.2 类设计

**PluginManager 类**
```csharp
/// <summary>
/// 插件管理器
/// 职责：插件生命周期管理
/// </summary>
public sealed class PluginManager : IPluginManager, IDisposable
{
    private readonly ConcurrentDictionary<string, IPlugin> _plugins = new();
    private readonly IPluginLoader _pluginLoader;
    private readonly ILogger<PluginManager> _logger;
    
    /// <summary>
    /// 加载插件
    /// </summary>
    public async Task<Result<IPlugin>> LoadPluginAsync(
        PluginDescriptor descriptor,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查插件是否已加载
            if (_plugins.TryGetValue(descriptor.Id, out var existingPlugin))
            {
                return Result.Success(existingPlugin);
            }
            
            // 加载插件
            var plugin = await _pluginLoader.LoadAsync(descriptor, cancellationToken);
            
            // 初始化插件
            await plugin.InitializeAsync(cancellationToken);
            
            // 注册插件
            _plugins.TryAdd(descriptor.Id, plugin);
            
            _logger.LogInformation("插件加载成功: {PluginId}", descriptor.Id);
            
            return Result.Success(plugin);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "插件加载失败: {PluginId}", descriptor.Id);
            return Result.Failure<IPlugin>(ex.Message);
        }
    }
    
    /// <summary>
    /// 卸载插件
    /// </summary>
    public async Task<Result> UnloadPluginAsync(
        string pluginId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (_plugins.TryRemove(pluginId, out var plugin))
            {
                await plugin.DisposeAsync();
                _logger.LogInformation("插件卸载成功: {PluginId}", pluginId);
            }
            
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "插件卸载失败: {PluginId}", pluginId);
            return Result.Failure(ex.Message);
        }
    }
}
```

## 📊 4. 数据设计

### 4.1 数据模型设计

#### 4.1.1 核心实体

**Workflow 实体**
```csharp
/// <summary>
/// 工作流实体
/// </summary>
public class Workflow : AggregateRoot<Guid>
{
    public string Name { get; private set; }
    public string Description { get; private set; }
    public string Version { get; private set; }
    public WorkflowStatus Status { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public string CreatedBy { get; private set; }
    public string UpdatedBy { get; private set; }
    
    // 导航属性
    public virtual ICollection<WorkflowNode> Nodes { get; private set; } = new List<WorkflowNode>();
    public virtual ICollection<WorkflowConnection> Connections { get; private set; } = new List<WorkflowConnection>();
    public virtual ICollection<WorkflowExecution> Executions { get; private set; } = new List<WorkflowExecution>();
    
    // 工厂方法
    public static Workflow Create(string name, string description, string createdBy)
    {
        var workflow = new Workflow
        {
            Id = Guid.NewGuid(),
            Name = name,
            Description = description,
            Version = "1.0.0",
            Status = WorkflowStatus.Draft,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = createdBy,
            UpdatedBy = createdBy
        };
        
        workflow.AddDomainEvent(new WorkflowCreatedEvent(workflow.Id, workflow.Name));
        
        return workflow;
    }
    
    // 业务方法
    public void UpdateDefinition(WorkflowDefinition definition, string updatedBy)
    {
        // 更新基本信息
        Name = definition.Name;
        Description = definition.Description;
        UpdatedAt = DateTime.UtcNow;
        UpdatedBy = updatedBy;
        
        // 更新节点
        Nodes.Clear();
        foreach (var nodeDefinition in definition.Nodes)
        {
            var node = WorkflowNode.Create(Id, nodeDefinition);
            Nodes.Add(node);
        }
        
        // 更新连接
        Connections.Clear();
        foreach (var connectionDefinition in definition.Connections)
        {
            var connection = WorkflowConnection.Create(Id, connectionDefinition);
            Connections.Add(connection);
        }
        
        AddDomainEvent(new WorkflowUpdatedEvent(Id, Name));
    }
    
    public void Publish()
    {
        if (Status != WorkflowStatus.Draft)
            throw new InvalidOperationException("只有草稿状态的工作流可以发布");
            
        Status = WorkflowStatus.Published;
        UpdatedAt = DateTime.UtcNow;
        
        AddDomainEvent(new WorkflowPublishedEvent(Id, Name));
    }
}
```

#### 4.1.2 数据库设计

**PostgreSQL 表结构**
```sql
-- 工作流表
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) NOT NULL DEFAULT '1.0.0',
    status INTEGER NOT NULL DEFAULT 0,
    definition JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255) NOT NULL,
    
    CONSTRAINT chk_workflow_name_length CHECK (LENGTH(name) >= 1),
    CONSTRAINT chk_workflow_version_format CHECK (version ~ '^\d+\.\d+\.\d+$')
);

-- 工作流节点表
CREATE TABLE workflow_nodes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    node_type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    position_x DECIMAL(10,2) NOT NULL,
    position_y DECIMAL(10,2) NOT NULL,
    configuration JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    CONSTRAINT chk_node_name_length CHECK (LENGTH(name) >= 1)
);

-- 工作流连接表
CREATE TABLE workflow_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    source_node_id UUID NOT NULL REFERENCES workflow_nodes(id) ON DELETE CASCADE,
    target_node_id UUID NOT NULL REFERENCES workflow_nodes(id) ON DELETE CASCADE,
    source_port VARCHAR(100),
    target_port VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    CONSTRAINT chk_no_self_connection CHECK (source_node_id != target_node_id),
    CONSTRAINT uk_workflow_connections UNIQUE (workflow_id, source_node_id, target_node_id, source_port, target_port)
);

-- 工作流执行表
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(id),
    status INTEGER NOT NULL DEFAULT 0,
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms BIGINT,
    
    CONSTRAINT chk_execution_duration CHECK (duration_ms >= 0)
);

-- 节点执行表
CREATE TABLE node_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_execution_id UUID NOT NULL REFERENCES workflow_executions(id) ON DELETE CASCADE,
    node_id UUID NOT NULL REFERENCES workflow_nodes(id),
    status INTEGER NOT NULL DEFAULT 0,
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms BIGINT,
    
    CONSTRAINT chk_node_execution_duration CHECK (duration_ms >= 0)
);

-- 索引
CREATE INDEX idx_workflows_status ON workflows(status);
CREATE INDEX idx_workflows_created_at ON workflows(created_at);
CREATE INDEX idx_workflow_nodes_workflow_id ON workflow_nodes(workflow_id);
CREATE INDEX idx_workflow_connections_workflow_id ON workflow_connections(workflow_id);
CREATE INDEX idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_executions_started_at ON workflow_executions(started_at);
CREATE INDEX idx_node_executions_workflow_execution_id ON node_executions(workflow_execution_id);
CREATE INDEX idx_node_executions_status ON node_executions(status);
```

## 🔌 5. 接口设计

### 5.1 RESTful API 设计

#### 5.1.1 API 设计原则
- 遵循 RESTful 设计规范
- 使用标准 HTTP 状态码
- 统一的错误响应格式
- 支持分页和过滤
- API 版本控制

#### 5.1.2 工作流管理 API

**创建工作流**
```http
POST /api/v2/workflows
Content-Type: application/json

{
  "name": "数据处理工作流",
  "description": "处理用户上传的数据文件",
  "definition": {
    "nodes": [...],
    "connections": [...]
  }
}

Response: 201 Created
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "数据处理工作流",
  "description": "处理用户上传的数据文件",
  "version": "1.0.0",
  "status": "draft",
  "createdAt": "2025-01-27T10:00:00Z",
  "updatedAt": "2025-01-27T10:00:00Z"
}
```

**获取工作流列表**
```http
GET /api/v2/workflows?page=1&size=20&status=published&search=数据处理

Response: 200 OK
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "数据处理工作流",
      "description": "处理用户上传的数据文件",
      "version": "1.0.0",
      "status": "published",
      "createdAt": "2025-01-27T10:00:00Z",
      "updatedAt": "2025-01-27T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "size": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

#### 5.1.3 工作流执行 API

**执行工作流**
```http
POST /api/v2/workflows/{workflowId}/executions
Content-Type: application/json

{
  "input": {
    "fileName": "data.csv",
    "fileSize": 1024000
  }
}

Response: 202 Accepted
{
  "executionId": "456e7890-e89b-12d3-a456-426614174001",
  "status": "running",
  "startedAt": "2025-01-27T10:30:00Z"
}
```

### 5.2 事件接口设计

#### 5.2.1 NATS 事件主题

**工作流事件**
```
workflow.created.{workflowId}
workflow.updated.{workflowId}
workflow.deleted.{workflowId}
workflow.published.{workflowId}
```

**执行事件**
```
execution.started.{executionId}
execution.progress.{executionId}
execution.completed.{executionId}
execution.failed.{executionId}
```

#### 5.2.2 事件消息格式

**工作流执行进度事件**
```json
{
  "eventType": "execution.progress",
  "eventId": "789e0123-e89b-12d3-a456-426614174002",
  "timestamp": "2025-01-27T10:35:00Z",
  "data": {
    "executionId": "456e7890-e89b-12d3-a456-426614174001",
    "workflowId": "123e4567-e89b-12d3-a456-426614174000",
    "progress": {
      "completedNodes": 3,
      "totalNodes": 10,
      "percentage": 30
    },
    "currentNode": {
      "id": "node-4",
      "name": "数据验证",
      "status": "running"
    }
  }
}
```

## 🛡️ 6. 安全设计

### 6.1 认证和授权

#### 6.1.1 JWT 认证
```csharp
/// <summary>
/// JWT 认证服务
/// </summary>
public class JwtAuthenticationService : IAuthenticationService
{
    private readonly JwtOptions _jwtOptions;
    private readonly IUserRepository _userRepository;
    
    public async Task<AuthenticationResult> AuthenticateAsync(
        string username, 
        string password)
    {
        // 验证用户凭据
        var user = await _userRepository.ValidateCredentialsAsync(username, password);
        if (user == null)
        {
            return AuthenticationResult.Failed("无效的用户名或密码");
        }
        
        // 生成 JWT Token
        var token = GenerateJwtToken(user);
        var refreshToken = GenerateRefreshToken();
        
        // 保存刷新令牌
        await _userRepository.SaveRefreshTokenAsync(user.Id, refreshToken);
        
        return AuthenticationResult.Success(token, refreshToken);
    }
    
    private string GenerateJwtToken(User user)
    {
        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim("role", user.Role)
        };
        
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        
        var token = new JwtSecurityToken(
            issuer: _jwtOptions.Issuer,
            audience: _jwtOptions.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_jwtOptions.ExpirationMinutes),
            signingCredentials: credentials
        );
        
        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}
```

#### 6.1.2 基于角色的访问控制 (RBAC)
```csharp
/// <summary>
/// 权限验证特性
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class RequirePermissionAttribute : Attribute, IAuthorizationFilter
{
    private readonly string _permission;
    
    public RequirePermissionAttribute(string permission)
    {
        _permission = permission;
    }
    
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        var user = context.HttpContext.User;
        if (!user.Identity.IsAuthenticated)
        {
            context.Result = new UnauthorizedResult();
            return;
        }
        
        var authorizationService = context.HttpContext.RequestServices
            .GetRequiredService<IAuthorizationService>();
            
        var hasPermission = authorizationService.HasPermission(user, _permission);
        if (!hasPermission)
        {
            context.Result = new ForbidResult();
        }
    }
}

// 使用示例
[RequirePermission("workflow.create")]
public async Task<IActionResult> CreateWorkflow([FromBody] CreateWorkflowRequest request)
{
    // 创建工作流逻辑
}
```

### 6.2 数据安全

#### 6.2.1 敏感数据加密
```csharp
/// <summary>
/// 数据加密服务
/// </summary>
public class DataEncryptionService : IDataEncryptionService
{
    private readonly byte[] _key;
    private readonly byte[] _iv;
    
    public string Encrypt(string plainText)
    {
        using var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = _iv;
        
        using var encryptor = aes.CreateEncryptor();
        using var msEncrypt = new MemoryStream();
        using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
        using var swEncrypt = new StreamWriter(csEncrypt);
        
        swEncrypt.Write(plainText);
        
        return Convert.ToBase64String(msEncrypt.ToArray());
    }
    
    public string Decrypt(string cipherText)
    {
        var buffer = Convert.FromBase64String(cipherText);
        
        using var aes = Aes.Create();
        aes.Key = _key;
        aes.IV = _iv;
        
        using var decryptor = aes.CreateDecryptor();
        using var msDecrypt = new MemoryStream(buffer);
        using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
        using var srDecrypt = new StreamReader(csDecrypt);
        
        return srDecrypt.ReadToEnd();
    }
}
```

## 📈 7. 性能设计

### 7.1 缓存策略

#### 7.1.1 多级缓存架构
```csharp
/// <summary>
/// 多级缓存服务
/// </summary>
public class MultiLevelCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<MultiLevelCacheService> _logger;
    
    public async Task<T> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        // L1: 内存缓存
        if (_memoryCache.TryGetValue(key, out T value))
        {
            return value;
        }
        
        // L2: 分布式缓存
        var distributedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
        if (distributedValue != null)
        {
            value = JsonSerializer.Deserialize<T>(distributedValue);
            
            // 回填内存缓存
            _memoryCache.Set(key, value, TimeSpan.FromMinutes(5));
            
            return value;
        }
        
        return default(T);
    }
    
    public async Task SetAsync<T>(
        string key, 
        T value, 
        TimeSpan expiration, 
        CancellationToken cancellationToken = default)
    {
        // 设置内存缓存
        _memoryCache.Set(key, value, expiration);
        
        // 设置分布式缓存
        var serializedValue = JsonSerializer.Serialize(value);
        await _distributedCache.SetStringAsync(
            key, 
            serializedValue, 
            new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            }, 
            cancellationToken);
    }
}
```

### 7.2 并发控制

#### 7.2.1 自适应并发控制
```csharp
/// <summary>
/// 自适应并发控制器
/// </summary>
public class AdaptiveConcurrencyController : IConcurrencyController
{
    private readonly SemaphoreSlim _semaphore;
    private readonly ISystemMetricsCollector _metricsCollector;
    private readonly Timer _adjustmentTimer;
    private volatile int _currentConcurrency;
    private volatile int _maxConcurrency;
    
    public AdaptiveConcurrencyController(
        ISystemMetricsCollector metricsCollector,
        int initialConcurrency = 10,
        int maxConcurrency = 100)
    {
        _metricsCollector = metricsCollector;
        _currentConcurrency = initialConcurrency;
        _maxConcurrency = maxConcurrency;
        _semaphore = new SemaphoreSlim(initialConcurrency, maxConcurrency);
        
        // 每30秒调整一次并发度
        _adjustmentTimer = new Timer(AdjustConcurrency, null, 
            TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }
    
    public async Task AcquireAsync(CancellationToken cancellationToken = default)
    {
        await _semaphore.WaitAsync(cancellationToken);
    }
    
    public void Release()
    {
        _semaphore.Release();
    }
    
    private void AdjustConcurrency(object state)
    {
        var metrics = _metricsCollector.GetCurrentMetrics();
        
        // 基于系统指标调整并发度
        var targetConcurrency = CalculateTargetConcurrency(metrics);
        
        if (targetConcurrency != _currentConcurrency)
        {
            AdjustSemaphore(targetConcurrency);
            _currentConcurrency = targetConcurrency;
        }
    }
    
    private int CalculateTargetConcurrency(SystemMetrics metrics)
    {
        // CPU使用率因子
        var cpuFactor = metrics.CpuUsage < 0.7 ? 1.2 : 
                       metrics.CpuUsage > 0.9 ? 0.8 : 1.0;
        
        // 内存使用率因子
        var memoryFactor = metrics.MemoryUsage < 0.8 ? 1.1 : 
                          metrics.MemoryUsage > 0.95 ? 0.7 : 1.0;
        
        // 队列长度因子
        var queueFactor = metrics.QueueLength < 10 ? 1.1 : 
                         metrics.QueueLength > 50 ? 0.9 : 1.0;
        
        var targetConcurrency = (int)(_currentConcurrency * cpuFactor * memoryFactor * queueFactor);
        
        return Math.Clamp(targetConcurrency, 1, _maxConcurrency);
    }
}
```

## 🔍 8. 监控和日志设计

### 8.1 监控指标

#### 8.1.1 业务指标
- 工作流执行成功率
- 平均执行时间
- 并发执行数量
- 节点执行分布

#### 8.1.2 技术指标
- API响应时间
- 数据库连接池使用率
- 内存使用情况
- CPU使用率

#### 8.1.3 Prometheus 指标定义
```csharp
/// <summary>
/// 监控指标收集器
/// </summary>
public class MetricsCollector
{
    private static readonly Counter WorkflowExecutionsTotal = Metrics
        .CreateCounter("workflow_executions_total", "工作流执行总数", "status", "workflow_type");
    
    private static readonly Histogram WorkflowExecutionDuration = Metrics
        .CreateHistogram("workflow_execution_duration_seconds", "工作流执行时长", "workflow_type");
    
    private static readonly Gauge ConcurrentExecutions = Metrics
        .CreateGauge("concurrent_executions", "并发执行数量");
    
    private static readonly Counter ApiRequestsTotal = Metrics
        .CreateCounter("api_requests_total", "API请求总数", "method", "endpoint", "status_code");
    
    public void RecordWorkflowExecution(string workflowType, string status, double duration)
    {
        WorkflowExecutionsTotal.WithLabels(status, workflowType).Inc();
        WorkflowExecutionDuration.WithLabels(workflowType).Observe(duration);
    }
    
    public void SetConcurrentExecutions(int count)
    {
        ConcurrentExecutions.Set(count);
    }
    
    public void RecordApiRequest(string method, string endpoint, int statusCode)
    {
        ApiRequestsTotal.WithLabels(method, endpoint, statusCode.ToString()).Inc();
    }
}
```

### 8.2 结构化日志

#### 8.2.1 日志配置
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Elasticsearch"],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/flowcustom-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "formatter": "Serilog.Formatting.Json.JsonFormatter"
        }
      },
      {
        "Name": "Elasticsearch",
        "Args": {
          "nodeUris": "http://elasticsearch:9200",
          "indexFormat": "flowcustom-logs-{0:yyyy.MM.dd}"
        }
      }
    ],
    "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
  }
}
```

#### 8.2.2 日志记录示例
```csharp
/// <summary>
/// 工作流执行日志记录
/// </summary>
public class WorkflowExecutionLogger
{
    private readonly ILogger<WorkflowExecutionLogger> _logger;
    
    public void LogExecutionStarted(Guid executionId, Guid workflowId, string workflowName)
    {
        _logger.LogInformation("工作流执行开始 {ExecutionId} {WorkflowId} {WorkflowName}", 
            executionId, workflowId, workflowName);
    }
    
    public void LogNodeExecutionCompleted(Guid executionId, string nodeId, string nodeName, TimeSpan duration)
    {
        _logger.LogInformation("节点执行完成 {ExecutionId} {NodeId} {NodeName} {Duration}ms", 
            executionId, nodeId, nodeName, duration.TotalMilliseconds);
    }
    
    public void LogExecutionFailed(Guid executionId, Exception exception)
    {
        _logger.LogError(exception, "工作流执行失败 {ExecutionId}", executionId);
    }
}
```

## 🧪 9. 测试设计

### 9.1 测试策略

#### 9.1.1 测试金字塔
```
    /\
   /  \     E2E Tests (10%)
  /____\    
 /      \   Integration Tests (20%)
/________\  Unit Tests (70%)
```

#### 9.1.2 单元测试示例
```csharp
/// <summary>
/// 工作流引擎单元测试
/// </summary>
public class WorkflowEngineTests
{
    private readonly Mock<IWorkflowRepository> _mockRepository;
    private readonly Mock<IExecutionEngine> _mockExecutionEngine;
    private readonly Mock<IEventBus> _mockEventBus;
    private readonly WorkflowEngine _workflowEngine;
    
    public WorkflowEngineTests()
    {
        _mockRepository = new Mock<IWorkflowRepository>();
        _mockExecutionEngine = new Mock<IExecutionEngine>();
        _mockEventBus = new Mock<IEventBus>();
        
        _workflowEngine = new WorkflowEngine(
            _mockRepository.Object,
            _mockExecutionEngine.Object,
            _mockEventBus.Object,
            Mock.Of<ILogger<WorkflowEngine>>());
    }
    
    [Fact]
    public async Task CreateWorkflowAsync_ValidDefinition_ReturnsSuccess()
    {
        // Arrange
        var definition = new WorkflowDefinition
        {
            Name = "测试工作流",
            Description = "测试描述",
            Version = "1.0.0",
            Nodes = new List<NodeDefinition>
            {
                new NodeDefinition { Id = "node1", Type = "start", Name = "开始" }
            },
            Connections = new List<ConnectionDefinition>(),
            Metadata = new WorkflowMetadata()
        };
        
        var expectedWorkflow = Workflow.Create(definition.Name, definition.Description, "test-user");
        _mockRepository.Setup(r => r.CreateAsync(It.IsAny<Workflow>(), It.IsAny<CancellationToken>()))
                      .ReturnsAsync(expectedWorkflow);
        
        // Act
        var result = await _workflowEngine.CreateWorkflowAsync(definition);
        
        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(definition.Name, result.Value.Name);
        _mockRepository.Verify(r => r.CreateAsync(It.IsAny<Workflow>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockEventBus.Verify(e => e.PublishAsync(It.IsAny<WorkflowCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }
    
    [Fact]
    public async Task CreateWorkflowAsync_InvalidDefinition_ReturnsFailure()
    {
        // Arrange
        var definition = new WorkflowDefinition
        {
            Name = "", // 无效名称
            Description = "测试描述",
            Version = "1.0.0",
            Nodes = new List<NodeDefinition>(),
            Connections = new List<ConnectionDefinition>(),
            Metadata = new WorkflowMetadata()
        };
        
        // Act
        var result = await _workflowEngine.CreateWorkflowAsync(definition);
        
        // Assert
        Assert.False(result.IsSuccess);
        Assert.Contains("工作流名称不能为空", result.Error);
    }
}
```

#### 9.1.3 集成测试示例
```csharp
/// <summary>
/// 工作流API集成测试
/// </summary>
public class WorkflowApiIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    
    public WorkflowApiIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }
    
    [Fact]
    public async Task CreateWorkflow_ValidRequest_ReturnsCreated()
    {
        // Arrange
        var request = new CreateWorkflowRequest
        {
            Name = "集成测试工作流",
            Description = "集成测试描述",
            Definition = new WorkflowDefinition
            {
                Nodes = new List<NodeDefinition>
                {
                    new NodeDefinition { Id = "node1", Type = "start", Name = "开始" }
                },
                Connections = new List<ConnectionDefinition>()
            }
        };
        
        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        // Act
        var response = await _client.PostAsync("/api/v2/workflows", content);
        
        // Assert
        Assert.Equal(HttpStatusCode.Created, response.StatusCode);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        var workflow = JsonSerializer.Deserialize<WorkflowResponse>(responseContent);
        
        Assert.Equal(request.Name, workflow.Name);
        Assert.Equal(request.Description, workflow.Description);
    }
}
```

### 9.2 性能测试

#### 9.2.1 负载测试配置
```yaml
# k6 负载测试脚本
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100个用户
    { duration: '5m', target: 100 }, // 保持100个用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200个用户
    { duration: '5m', target: 200 }, // 保持200个用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0个用户
  ],
  thresholds: {
    http_req_duration: ['p(95)<100'], // 95%的请求响应时间小于100ms
    http_req_failed: ['rate<0.1'],    // 错误率小于10%
  },
};

export default function () {
  // 创建工作流测试
  let createPayload = JSON.stringify({
    name: `测试工作流-${__VU}-${__ITER}`,
    description: '性能测试工作流',
    definition: {
      nodes: [
        { id: 'node1', type: 'start', name: '开始' },
        { id: 'node2', type: 'end', name: '结束' }
      ],
      connections: [
        { sourceNodeId: 'node1', targetNodeId: 'node2' }
      ]
    }
  });
  
  let createResponse = http.post('http://localhost:5000/api/v2/workflows', createPayload, {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(createResponse, {
    '创建工作流状态为201': (r) => r.status === 201,
    '创建工作流响应时间<100ms': (r) => r.timings.duration < 100,
  });
  
  if (createResponse.status === 201) {
    let workflow = JSON.parse(createResponse.body);
    
    // 执行工作流测试
    let executePayload = JSON.stringify({
      input: { testData: 'performance test' }
    });
    
    let executeResponse = http.post(
      `http://localhost:5000/api/v2/workflows/${workflow.id}/executions`,
      executePayload,
      { headers: { 'Content-Type': 'application/json' } }
    );
    
    check(executeResponse, {
      '执行工作流状态为202': (r) => r.status === 202,
      '执行工作流响应时间<200ms': (r) => r.timings.duration < 200,
    });
  }
  
  sleep(1);
}
```

## 🚀 10. 部署设计

### 10.1 容器化部署

#### 10.1.1 Dockerfile
```dockerfile
# 后端API Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

COPY . .
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll"]
```

#### 10.1.2 Docker Compose
```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - api-gateway
    environment:
      - VITE_API_BASE_URL=http://api-gateway:8000
    networks:
      - flowcustom-network

  # API网关
  api-gateway:
    image: kong:3.4
    ports:
      - "8000:8000"
      - "8001:8001"
    environment:
      - KONG_DATABASE=off
      - KONG_DECLARATIVE_CONFIG=/kong/declarative/kong.yml
      - KONG_PROXY_ACCESS_LOG=/dev/stdout
      - KONG_ADMIN_ACCESS_LOG=/dev/stdout
      - KONG_PROXY_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_LISTEN=0.0.0.0:8001
    volumes:
      - ./kong/kong.yml:/kong/declarative/kong.yml
    networks:
      - flowcustom-network

  # 工作流API服务
  workflow-api:
    build:
      context: .
      dockerfile: src/FlowCustomV1.Api/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=flowcustom;Username=flowcustom;Password=flowcustom123
      - Redis__ConnectionString=redis:6379
      - NATS__Servers=nats://nats:4222
    depends_on:
      - postgres
      - redis
      - nats
    deploy:
      replicas: 3
    networks:
      - flowcustom-network

  # PostgreSQL数据库
  postgres:
    image: postgres:16
    environment:
      - POSTGRES_DB=flowcustom
      - POSTGRES_USER=flowcustom
      - POSTGRES_PASSWORD=flowcustom123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - flowcustom-network

  # Redis缓存
  redis:
    image: redis:7.2-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - flowcustom-network

  # NATS消息队列
  nats:
    image: nats:2.10-alpine
    command: ["-js", "-m", "8222"]
    ports:
      - "4222:4222"
      - "8222:8222"
    networks:
      - flowcustom-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.45.0
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - flowcustom-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:10.0.0
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - flowcustom-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  flowcustom-network:
    driver: bridge
```

### 10.2 Kubernetes部署

#### 10.2.1 命名空间和配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: flowcustom
  labels:
    name: flowcustom

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: flowcustom-config
  namespace: flowcustom
data:
  appsettings.json: |
    {
      "ConnectionStrings": {
        "DefaultConnection": "Host=postgres-service;Database=flowcustom;Username=flowcustom;Password=flowcustom123"
      },
      "Redis": {
        "ConnectionString": "redis-service:6379"
      },
      "NATS": {
        "Servers": ["nats://nats-service:4222"]
      },
      "Logging": {
        "LogLevel": {
          "Default": "Information",
          "Microsoft.AspNetCore": "Warning"
        }
      }
    }
```

#### 10.2.2 工作流API部署
```yaml
# workflow-api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-api
  namespace: flowcustom
  labels:
    app: workflow-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-api
  template:
    metadata:
      labels:
        app: workflow-api
    spec:
      containers:
      - name: workflow-api
        image: flowcustom/workflow-api:v2.0.0
        ports:
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"        
        volumeMounts:
        - name: config-volume
          mountPath: /app/appsettings.json
          subPath: appsettings.json
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: flowcustom-config

---
# workflow-api-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: workflow-api-service
  namespace: flowcustom
spec:
  selector:
    app: workflow-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP

---
# workflow-api-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: workflow-api-hpa
  namespace: flowcustom
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workflow-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
#### 10.2.3 数据库部署
```yaml
# postgres-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: flowcustom
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:16
        env:
        - name: POSTGRES_DB
          value: "flowcustom"
        - name: POSTGRES_USER
          value: "flowcustom"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi

---
# postgres-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: flowcustom
spec:
  selector:
    app: postgres
  ports:
  - protocol: TCP
    port: 5432
    targetPort: 5432
  type: ClusterIP

---
# postgres-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: flowcustom
type: Opaque
data:
  password: Zmxvd2N1c3RvbTEyMw== # base64编码的"flowcustom123"
```

### 10.3 CI/CD 流水线

#### 10.3.1 GitHub Actions 工作流
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
    
    - name: Run tests
      run: dotnet test --no-build --configuration Release --collect:"XPlat Code Coverage"
    
    - name: Code Coverage Report
      uses: codecov/codecov-action@v3
      with:
        files: ./coverage.cobertura.xml
        fail_ci_if_error: true
    
    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 构建和推送镜像
  build-and-push:
    needs: [code-quality, security-scan]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./src/FlowCustomV1.Api/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  # 部署到测试环境
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Deploy to Staging
      uses: azure/k8s-deploy@v1
      with:
        manifests: |
          k8s/staging/namespace.yaml
          k8s/staging/configmap.yaml
          k8s/staging/deployment.yaml
          k8s/staging/service.yaml
        images: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

  # 部署到生产环境
  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Deploy to Production
      uses: azure/k8s-deploy@v1
      with:
        manifests: |
          k8s/production/namespace.yaml
          k8s/production/configmap.yaml
          k8s/production/deployment.yaml
          k8s/production/service.yaml
        images: |
          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Run Smoke Tests
      run: |
        # 等待部署完成
        sleep 60
        # 运行冒烟测试
        curl -f https://api.flowcustom.com/health || exit 1
        curl -f https://api.flowcustom.com/api/v2/workflows || exit 1
```

## 📋 11. 质量保证

### 11.1 代码质量标准

#### 11.1.1 代码规范
```csharp
// 代码规范示例
namespace FlowCustomV1.Core.Domain.Workflows
{
    /// <summary>
    /// 工作流聚合根
    /// 遵循DDD设计原则，封装业务逻辑
    /// </summary>
    public sealed class Workflow : AggregateRoot<WorkflowId>
    {
        private readonly List<WorkflowNode> _nodes = new();
        private readonly List<WorkflowConnection> _connections = new();
        
        /// <summary>
        /// 工作流名称
        /// 必须唯一且不能为空
        /// </summary>
        public WorkflowName Name { get; private set; }
        
        /// <summary>
        /// 工作流描述
        /// 可选字段，用于说明工作流用途
        /// </summary>
        public string? Description { get; private set; }
        
        /// <summary>
        /// 工作流状态
        /// 控制工作流的生命周期
        /// </summary>
        public WorkflowStatus Status { get; private set; }
        
        /// <summary>
        /// 只读节点集合
        /// 外部不能直接修改节点集合
        /// </summary>
        public IReadOnlyList<WorkflowNode> Nodes => _nodes.AsReadOnly();
        
        /// <summary>
        /// 只读连接集合
        /// 外部不能直接修改连接集合
        /// </summary>
        public IReadOnlyList<WorkflowConnection> Connections => _connections.AsReadOnly();
        
        // 私有构造函数，强制使用工厂方法
        private Workflow() { }
        
        /// <summary>
        /// 创建新的工作流
        /// </summary>
        /// <param name="name">工作流名称</param>
        /// <param name="description">工作流描述</param>
        /// <param name="createdBy">创建者</param>
        /// <returns>新创建的工作流实例</returns>
        /// <exception cref="ArgumentException">当名称无效时抛出</exception>
        public static Workflow Create(WorkflowName name, string? description, UserId createdBy)
        {
            Guard.Against.Null(name, nameof(name));
            Guard.Against.Null(createdBy, nameof(createdBy));
            
            var workflow = new Workflow
            {
                Id = WorkflowId.New(),
                Name = name,
                Description = description,
                Status = WorkflowStatus.Draft,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = createdBy
            };
            
            workflow.AddDomainEvent(new WorkflowCreatedDomainEvent(workflow.Id, workflow.Name));
            
            return workflow;
        }
        
        /// <summary>
        /// 添加节点到工作流
        /// </summary>
        /// <param name="nodeDefinition">节点定义</param>
        /// <exception cref="InvalidOperationException">当工作流状态不允许修改时抛出</exception>
        public void AddNode(NodeDefinition nodeDefinition)
        {
            Guard.Against.Null(nodeDefinition, nameof(nodeDefinition));
            EnsureCanModify();
            
            if (_nodes.Any(n => n.Id == nodeDefinition.Id))
            {
                throw new DomainException($"节点ID已存在: {nodeDefinition.Id}");
            }
            
            var node = WorkflowNode.Create(nodeDefinition);
            _nodes.Add(node);
            
            AddDomainEvent(new NodeAddedDomainEvent(Id, node.Id));
        }
        
        /// <summary>
        /// 验证工作流完整性
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var validator = new WorkflowValidator();
            return validator.Validate(this);
        }
        
        /// <summary>
        /// 发布工作流
        /// </summary>
        /// <exception cref="InvalidOperationException">当工作流状态不允许发布时抛出</exception>
        public void Publish()
        {
            if (Status != WorkflowStatus.Draft)
            {
                throw new InvalidOperationException($"只有草稿状态的工作流可以发布，当前状态: {Status}");
            }
            
            var validationResult = Validate();
            if (!validationResult.IsValid)
            {
                throw new DomainException($"工作流验证失败: {string.Join(", ", validationResult.Errors)}");
            }
            
            Status = WorkflowStatus.Published;
            UpdatedAt = DateTime.UtcNow;
            
            AddDomainEvent(new WorkflowPublishedDomainEvent(Id, Name));
        }
        
        /// <summary>
        /// 确保工作流可以被修改
        /// </summary>
        private void EnsureCanModify()
        {
            if (Status == WorkflowStatus.Published)
            {
                throw new InvalidOperationException("已发布的工作流不能修改");
            }
        }
    }
}
```

#### 11.1.2 EditorConfig 配置
```ini
# .editorconfig
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{cs,csx,vb,vbx}]
indent_size = 4

[*.{json,js,ts,tsx,css,scss,html,yml,yaml}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false

# C# 代码风格规则
[*.cs]
# 使用 var 关键字
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = false:suggestion

# 表达式偏好
csharp_prefer_simple_using_statement = true:suggestion
csharp_prefer_braces = true:silent
csharp_style_namespace_declarations = file_scoped:warning

# 组织 using 语句
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = false

# 命名约定
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.severity = warning
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.symbols = interface
dotnet_naming_rule.interfaces_should_be_prefixed_with_i.style = prefix_interface_with_i

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected

dotnet_naming_style.prefix_interface_with_i.required_prefix = I
dotnet_naming_style.prefix_interface_with_i.capitalization = pascal_case
```

### 11.2 测试覆盖率要求

#### 11.2.1 覆盖率目标
- **单元测试覆盖率**: ≥ 90%
- **集成测试覆盖率**: ≥ 70%
- **端到端测试覆盖率**: ≥ 50%
- **关键路径覆盖率**: 100%

#### 11.2.2 测试配置
```xml
<!-- Directory.Build.props -->
<Project>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup Condition="'$(IsTestProject)' == 'true'">
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.1" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Testcontainers" Version="3.5.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
  </ItemGroup>
</Project>
```

### 11.3 性能基准

#### 11.3.1 性能指标
| 指标 | 目标值 | 测量方法 |
|------|--------|----------|
| API响应时间(P95) | < 100ms | APM监控 |
| API响应时间(P99) | < 200ms | APM监控 |
| 工作流执行延迟 | < 50ms | 业务监控 |
| 数据库查询时间 | < 10ms | 数据库监控 |
| 内存使用率 | < 80% | 系统监控 |
| CPU使用率 | < 70% | 系统监控 |
| 并发用户数 | > 1000 | 负载测试 |
| 吞吐量 | > 10000 RPS | 负载测试 |

#### 11.3.2 性能测试自动化
```csharp
/// <summary>
/// 性能基准测试
/// </summary>
[MemoryDiagnoser]
[SimpleJob(RuntimeMoniker.Net80)]
public class WorkflowEngineBenchmarks
{
    private WorkflowEngine _workflowEngine;
    private WorkflowDefinition _simpleWorkflow;
    private WorkflowDefinition _complexWorkflow;
    
    [GlobalSetup]
    public void Setup()
    {
        // 初始化测试环境
        var services = new ServiceCollection();
        services.AddWorkflowEngine();
        var serviceProvider = services.BuildServiceProvider();
        
        _workflowEngine = serviceProvider.GetRequiredService<WorkflowEngine>();
        
        // 创建测试工作流
        _simpleWorkflow = CreateSimpleWorkflow();
        _complexWorkflow = CreateComplexWorkflow();
    }
    
    [Benchmark]
    public async Task CreateSimpleWorkflow()
    {
        await _workflowEngine.CreateWorkflowAsync(_simpleWorkflow);
    }
    
    [Benchmark]
    public async Task CreateComplexWorkflow()
    {
        await _workflowEngine.CreateWorkflowAsync(_complexWorkflow);
    }
    
    [Benchmark]
    [Arguments(10)]
    [Arguments(100)]
    [Arguments(1000)]
    public async Task ExecuteWorkflowsConcurrently(int concurrency)
    {
        var tasks = new Task[concurrency];
        for (int i = 0; i < concurrency; i++)
        {
            tasks[i] = _workflowEngine.ExecuteWorkflowAsync(
                _simpleWorkflow.Id, 
                new Dictionary<string, object>());
        }
        
        await Task.WhenAll(tasks);
    }
    
    private WorkflowDefinition CreateSimpleWorkflow()
    {
        return new WorkflowDefinition
        {
            Name = "简单工作流",
            Description = "性能测试用简单工作流",
            Version = "1.0.0",
            Nodes = new List<NodeDefinition>
            {
                new NodeDefinition { Id = "start", Type = "start", Name = "开始" },
                new NodeDefinition { Id = "end", Type = "end", Name = "结束" }
            },
            Connections = new List<ConnectionDefinition>
            {
                new ConnectionDefinition { SourceNodeId = "start", TargetNodeId = "end" }
            },
            Metadata = new WorkflowMetadata()
        };
    }
    
    private WorkflowDefinition CreateComplexWorkflow()
    {
        // 创建包含50个节点的复杂工作流
        var nodes = new List<NodeDefinition>();
        var connections = new List<ConnectionDefinition>();
        
        // 添加开始节点
        nodes.Add(new NodeDefinition { Id = "start", Type = "start", Name = "开始" });
        
        // 添加中间节点
        for (int i = 1; i <= 48; i++)
        {
            nodes.Add(new NodeDefinition 
            { 
                Id = $"node{i}", 
                Type = "process", 
                Name = $"处理节点{i}" 
            });
            
            connections.Add(new ConnectionDefinition 
            { 
                SourceNodeId = i == 1 ? "start" : $"node{i-1}", 
                TargetNodeId = $"node{i}" 
            });
        }
        
        // 添加结束节点
        nodes.Add(new NodeDefinition { Id = "end", Type = "end", Name = "结束" });
        connections.Add(new ConnectionDefinition { SourceNodeId = "node48", TargetNodeId = "end" });
        
        return new WorkflowDefinition
        {
            Name = "复杂工作流",
            Description = "性能测试用复杂工作流",
            Version = "1.0.0",
            Nodes = nodes,
            Connections = connections,
            Metadata = new WorkflowMetadata()
        };
    }
}
```

## 🔄 12. 维护和演进

### 12.1 版本管理策略

#### 12.1.1 语义化版本控制
```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)

- MAJOR: 不兼容的API修改
- MINOR: 向后兼容的功能性新增
- PATCH: 向后兼容的问题修正

示例:
- v2.0.0: 重大架构升级
- v2.1.0: 新增插件系统
- v2.1.1: 修复执行引擎bug
```

#### 12.1.2 API版本控制
```csharp
/// <summary>
/// API版本控制配置
/// </summary>
public static class ApiVersioningConfiguration
{
    public static IServiceCollection AddApiVersioning(this IServiceCollection services)
    {
        services.AddApiVersioning(options =>
        {
            options.DefaultApiVersion = new ApiVersion(2, 0);
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.ApiVersionReader = ApiVersionReader.Combine(
                new UrlSegmentApiVersionReader(),
                new HeaderApiVersionReader("X-Version"),
                new QueryStringApiVersionReader("version")
            );
        });
        
        services.AddVersionedApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'VVV";
            options.SubstituteApiVersionInUrl = true;
        });
        
        return services;
    }
}

// 控制器版本示例
[ApiController]
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/workflows")]
public class WorkflowsV2Controller : ControllerBase
{
    // V2.0 API实现
}

[ApiController]
[ApiVersion("1.0", Deprecated = true)]
[Route("api/v{version:apiVersion}/workflows")]
public class WorkflowsV1Controller : ControllerBase
{
    // V1.0 API实现（已弃用）
}
```

### 12.2 数据库迁移策略

#### 12.2.1 Entity Framework 迁移
```csharp
/// <summary>
/// 数据库迁移基类
/// 提供标准化的迁移模板
/// </summary>
public abstract class BaseMigration : Migration
{
    protected void CreateTableWithAudit(string tableName, Action<CreateTableBuilder<Dictionary<string, object>>> buildAction)
    {
        migrationBuilder.CreateTable(
            name: tableName,
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uuid", nullable: false, defaultValueSql: "gen_random_uuid()"),
                CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                CreatedBy = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                UpdatedBy = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                Version = table.Column<long>(type: "bigint", nullable: false, defaultValue: 1L)
            },
            constraints: table =>
            {
                table.PrimaryKey($"PK_{tableName}", x => x.Id);
            });
            
        // 添加更新时间触发器
        migrationBuilder.Sql($@"
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                NEW.version = OLD.version + 1;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
            
            CREATE TRIGGER update_{tableName}_updated_at 
                BEFORE UPDATE ON {tableName} 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        ");
    }
}

/// <summary>
/// 示例迁移：添加工作流标签功能
/// </summary>
[DbContext(typeof(FlowCustomDbContext))]
[Migration("20250127100000_AddWorkflowTags")]
public partial class AddWorkflowTags : BaseMigration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 创建标签表
        CreateTableWithAudit("workflow_tags", table => new
        {
            Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
            Color = table.Column<string>(type: "character varying(7)", maxLength: 7, nullable: false, defaultValue: "#007bff"),
            Description = table.Column<string>(type: "text", nullable: true)
        });
        
        // 创建工作流标签关联表
        migrationBuilder.CreateTable(
            name: "workflow_tag_associations",
            columns: table => new
            {
                WorkflowId = table.Column<Guid>(type: "uuid", nullable: false),
                TagId = table.Column<Guid>(type: "uuid", nullable: false),
                CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()")
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_workflow_tag_associations", x => new { x.WorkflowId, x.TagId });
                table.ForeignKey(
                    name: "FK_workflow_tag_associations_workflows_WorkflowId",
                    column: x => x.WorkflowId,
                    principalTable: "workflows",
                    principalColumn: "id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_workflow_tag_associations_workflow_tags_TagId",
                    column: x => x.TagId,
                    principalTable: "workflow_tags",
                    principalColumn: "id",
                    onDelete: ReferentialAction.Cascade);
            });
        
        // 添加索引
        migrationBuilder.CreateIndex(
            name: "IX_workflow_tags_name",
            table: "workflow_tags",
            column: "name",
            unique: true);
            
        migrationBuilder.CreateIndex(
            name: "IX_workflow_tag_associations_TagId",
            table: "workflow_tag_associations",
            column: "TagId");
    }
    
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable("workflow_tag_associations");
        migrationBuilder.DropTable("workflow_tags");
    }
}
```

### 12.3 监控和告警

#### 12.3.1 健康检查配置
```csharp
/// <summary>
/// 健康检查配置
/// </summary>
public static class HealthCheckConfiguration
{
    public static IServiceCollection AddHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            // 数据库健康检查
            .AddNpgSql(
                configuration.GetConnectionString("DefaultConnection"),
                name: "postgres",
                tags: new[] { "database", "postgres" })
            
            // Redis健康检查
            .AddRedis(
                configuration.GetConnectionString("Redis"),
                name: "redis",
                tags: new[] { "cache", "redis" })
            
            // NATS健康检查
            .AddNats(
                options => options.Url = configuration.GetConnectionString("NATS"),
                name: "nats",
                tags: new[] { "messaging", "nats" })
            
            // 自定义业务健康检查
            .AddCheck<WorkflowEngineHealthCheck>("workflow-engine", tags: new[] { "business" })
            .AddCheck<PluginManagerHealthCheck>("plugin-manager", tags: new[] { "business" });
        
        return services;
    }
}

/// <summary>
/// 工作流引擎健康检查
/// </summary>
public class WorkflowEngineHealthCheck : IHealthCheck
{
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ILogger<WorkflowEngineHealthCheck> _logger;
    
    public WorkflowEngineHealthCheck(IWorkflowEngine workflowEngine, ILogger<WorkflowEngineHealthCheck> logger)
    {
        _workflowEngine = workflowEngine;
        _logger = logger;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查工作流引擎是否正常工作
            var testWorkflow = CreateTestWorkflow();
            var result = await _workflowEngine.ValidateWorkflowAsync(testWorkflow.Id, cancellationToken);
            
            if (result.IsValid)
            {
                return HealthCheckResult.Healthy("工作流引擎运行正常");
            }
            else
            {
                return HealthCheckResult.Degraded($"工作流引擎存在问题: {string.Join(", ", result.Errors)}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作流引擎健康检查失败");
            return HealthCheckResult.Unhealthy("工作流引擎不可用", ex);
        }
    }
    
    private WorkflowDefinition CreateTestWorkflow()
    {
        return new WorkflowDefinition
        {
            Name = "健康检查测试工作流",
            Description = "用于健康检查的测试工作流",
            Version = "1.0.0",
            Nodes = new List<NodeDefinition>
            {
                new NodeDefinition { Id = "start", Type = "start", Name = "开始" },
                new NodeDefinition { Id = "end", Type = "end", Name = "结束" }
            },
            Connections = new List<ConnectionDefinition>
            {
                new ConnectionDefinition { SourceNodeId = "start", TargetNodeId = "end" }
            },
            Metadata = new WorkflowMetadata()
        };
    }
}
```

#### 12.3.2 Grafana 仪表板配置
```json
{
  "dashboard": {
    "id": null,
    "title": "FlowCustomV1 系统监控",
    "tags": ["flowcustom", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "API请求率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(api_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "yAxes": [
          {
            "label": "请求/秒",
            "min": 0
          }
        ]
      },
      {
        "id": 2,
        "title": "API响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "P99"
          }
        ],
        "yAxes": [
          {
            "label": "秒",
            "min": 0
          }
        ]
      },
      {
        "id": 3,
        "title": "工作流执行状态",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(workflow_executions_total{status=\"success\"}[5m]))",
            "legendFormat": "成功"
          },
          {
            "expr": "sum(rate(workflow_executions_total{status=\"failed\"}[5m]))",
            "legendFormat": "失败"
          }
        ]
      },
      {
        "id": 4,
        "title": "系统资源使用",
        "type": "graph",
        "targets": [
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "legendFormat": "内存使用(MB)"
          },
          {
            "expr": "rate(process_cpu_seconds_total[5m]) * 100",
            "legendFormat": "CPU使用率(%)"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## 📚 13. 文档和培训

### 13.1 技术文档结构

```
docs/
├── architecture/           # 架构文档
│   ├── system-overview.md
│   ├── component-design.md
│   └── deployment-guide.md
├── api/                   # API文档
│   ├── rest-api.md
│   ├── event-api.md
│   └── openapi.yaml
├── development/           # 开发文档
│   ├── getting-started.md
│   ├── coding-standards.md
│   ├── testing-guide.md
│   └── debugging-guide.md
├── operations/            # 运维文档
│   ├── installation.md
│   ├── configuration.md
│   ├── monitoring.md
│   └── troubleshooting.md
├── user/                  # 用户文档
│   ├── user-guide.md
│   ├── workflow-design.md
│   └── plugin-development.md
└── changelog/             # 变更日志
    ├── v2.0.0.md
    ├── v2.1.0.md
    └── migration-guide.md
```

### 13.2 开发者培训计划

#### 13.2.1 新员工入职培训
```markdown
# FlowCustomV1 开发者入职培训计划

## 第一周：基础知识
- [ ] 系统架构概览
- [ ] 开发环境搭建
- [ ] 代码规范学习
- [ ] Git工作流程
- [ ] 基础功能演示

## 第二周：核心模块
- [ ] 工作流引擎原理
- [ ] 执行引擎设计
- [ ] 插件系统架构
- [ ] 数据模型理解
- [ ] API设计原则

## 第三周：实践项目
- [ ] 开发简单插件
- [ ] 编写单元测试
- [ ] 参与代码评审
- [ ] 修复入门级Bug
- [ ] 文档编写练习

## 第四周：高级主题
- [ ] 性能优化技巧
- [ ] 监控和日志
- [ ] 部署和运维
- [ ] 安全最佳实践
- [ ] 故障排查方法
```

## 📋 14. 总结

### 14.1 设计亮点

1. **清晰的架构分层**: 采用DDD和清洁架构原则，确保代码的可维护性和可扩展性
2. **高性能设计**: 通过缓存、并发控制和异步处理，实现高性能的工作流执行
3. **完善的监控体系**: 集成Prometheus、Grafana等监控工具，提供全方位的系统监控
4. **自动化CI/CD**: 完整的自动化构建、测试和部署流水线
5. **云原生支持**: 支持容器化部署和Kubernetes编排

### 14.2 技术创新

1. **自适应并发控制**: 根据系统负载自动调整并发度
2. **多级缓存策略**: 内存缓存+分布式缓存的多级缓存架构
3. **事件驱动架构**: 基于NATS的高性能事件总线
4. **插件热加载**: 支持插件的动态加载和卸载
5. **智能健康检查**: 业务级别的健康检查机制

### 14.3 质量保证

1. **高测试覆盖率**: 单元测试覆盖率≥90%，集成测试覆盖率≥70%
2. **性能基准测试**: 自动化的性能基准测试和监控
3. **安全扫描**: 集成安全扫描工具，确保代码安全
4. **代码质量检查**: SonarCloud代码质量分析
5. **文档完整性**: 完整的技术文档和用户文档

### 14.4 未来演进方向

1. **微服务拆分**: 支持向微服务架构的平滑演进
2. **AI集成**: 集成机器学习能力，提供智能工作流优化
3. **多云支持**: 支持多云部署和混合云架构
4. **可视化增强**: 更丰富的工作流可视化和监控界面
5. **生态扩展**: 构建更完善的插件生态系统

---

**文档状态**: 待审核  
**下一步行动**: 技术委员会评审 → 项目总监批准 → 开发团队实施  
**预计完成时间**: 2025年Q2

---

*本文档遵循CMMI Level 5标准编写，确保软件开发过程的规范性和可追溯性。*