@echo off
echo 🚀 启动FlowCustomV1 Master节点 (基于FlowCustomV1.Api)
echo ================================================

echo ℹ️  检查基础设施服务...
docker ps --filter "name=flowcustom-mysql-debug" --filter "name=flowcustom-redis-debug" --filter "name=flowcustom-nats-debug" --format "table {{.Names}}\t{{.Status}}"

echo.
echo ℹ️  启动Master节点...
echo ℹ️  节点模式: Master
echo ℹ️  API端口: 5279
echo ℹ️  配置文件: appsettings.Master.json
echo ℹ️  Swagger: http://localhost:5279/swagger
echo ℹ️  集群API: http://localhost:5279/api/cluster/status
echo ℹ️  按 Ctrl+C 停止服务
echo.

set ASPNETCORE_ENVIRONMENT=Master
dotnet run --project src/FlowCustomV1.Api -- --urls "http://localhost:5279"
