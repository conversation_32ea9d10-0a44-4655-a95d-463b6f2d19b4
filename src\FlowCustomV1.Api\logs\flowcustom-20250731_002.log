[2025-07-31 16:14:08.298 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 16:14:08.436 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 16:14:08.444 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 16:14:08.457 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 16:14:08.462 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 16:14:08.467 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 16:14:08.472 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 16:14:08.474 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 16:14:08.477 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 16:14:08.483 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 16:14:08.485 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 16:14:08.495 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 16:14:08.498 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 16:14:08.501 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 16:14:08.504 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 16:14:08.523 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 16:14:08.530 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 16:14:08.540 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 16:14:08.543 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 16:14:08.547 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 16:14:08.553 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 16:14:08.555 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 16:14:08.562 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 16:14:08.566 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 16:14:08.572 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 16:14:08.575 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 16:14:08.594 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,507,768 字节
[2025-07-31 16:14:08.600 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 16:14:08.607 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Worker", 节点ID: worker-001
[2025-07-31 16:14:08.620 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: system.notification
[2025-07-31 16:14:08.624 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.heartbeat
[2025-07-31 16:14:08.628 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.events
[2025-07-31 16:14:08.638 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.node_registration
[2025-07-31 16:14:08.638 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.heartbeat
[2025-07-31 16:14:08.642 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: worker-001
[2025-07-31 16:14:08.649 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 16:14:08.654 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 16:14:08.659 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 16:14:08.669 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\debug
[2025-07-31 16:14:08.674 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\info
[2025-07-31 16:14:08.678 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\warn
[2025-07-31 16:14:08.683 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\error
[2025-07-31 16:14:08.687 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\trace
[2025-07-31 16:14:08.691 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 16:14:08.704 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 16:14:08.707 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 16:14:09.979 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:14:09.989 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:14:09.998 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:14:10.007 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:14:10.017 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:14:10.378 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 16:14:10.390 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 16:14:10.561 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 16:14:10.568 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 16:14:10.575 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 16:14:10.807 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:14:10.848 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 16:14:10.900 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 16:14:10.907 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 16:14:11.211 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 16:14:11.229 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 16:14:11.239 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 16:14:11.246 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 16:14:11.253 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 16:14:11.261 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 16:14:11.416 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 16:14:11.454 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 16:14:11.461 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 16:14:11.465 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 16:14:11.470 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 16:14:11.478 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 16:14:11.484 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 16:14:11.491 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 16:14:11.503 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 16:14:11.513 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 16:14:11.523 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 16:14:11.534 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 16:14:11.543 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 16:14:11.554 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 16:14:11.565 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 16:14:11.576 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 16:14:11.588 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 16:14:11.605 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 16:14:11.635 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 16:14:11.648 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 16:14:11.659 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 16:14:11.668 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 16:14:11.676 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 16:14:11.686 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 16:14:11.696 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 16:14:11.705 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 16:14:11.712 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 16:14:11.720 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 16:14:11.727 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 16:14:11.733 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 16:14:11.741 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 16:14:11.746 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 16:14:11.764 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 16:14:11.777 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 16:14:11.782 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 16:14:11.794 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 16:14:11.799 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:14:11.804 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:14:11.808 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 16:14:11.812 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 16:14:11.816 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 16:14:11.827 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 16:14:11.833 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 16:14:11.838 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 16:14:11.848 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 16:14:12.033 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 16:14:12.040 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 16:14:12.044 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 16:14:12.048 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 16:14:12.053 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 16:14:12.056 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 16:14:12.070 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 16:14:12.074 +08:00 INF] Program: 后台初始化完成
[2025-07-31 16:14:13.568 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 16:14:18.561 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 16:14:38.475 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 16:14:41.692 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:14:53.468 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 16:14:53.502 +08:00 INF] Program: 请求完成: GET /api/cluster/health - 200 - 27ms
[2025-07-31 16:15:11.689 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:15:41.690 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:16:11.692 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:16:41.697 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:17:11.688 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:17:41.687 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:18:11.691 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:18:41.695 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:19:11.694 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:19:41.700 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:20:11.692 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:20:41.699 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:21:11.693 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:21:41.693 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:22:11.690 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:22:41.687 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:23:11.698 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:24:37.364 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 16:24:37.448 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 16:24:37.455 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 16:24:37.471 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 16:24:37.478 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 16:24:37.485 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 16:24:37.491 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 16:24:37.497 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 16:24:37.503 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 16:24:37.508 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 16:24:37.514 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 16:24:37.523 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 16:24:37.527 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 16:24:37.532 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 16:24:37.536 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 16:24:37.565 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 16:24:37.566 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 16:24:37.584 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 16:24:37.590 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 16:24:37.593 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 16:24:37.595 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 16:24:37.614 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 16:24:37.615 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 16:24:37.628 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 16:24:37.632 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,273,504 字节
[2025-07-31 16:24:37.635 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 16:24:37.641 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 16:24:37.647 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 16:24:37.654 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Master", 节点ID: master-001
[2025-07-31 16:24:37.666 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.heartbeat
[2025-07-31 16:24:37.672 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: NATS未连接，无法订阅主题: cluster.events
[2025-07-31 16:24:37.683 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.heartbeat
[2025-07-31 16:24:37.684 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: system.notification.node_registration
[2025-07-31 16:24:37.695 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: master-001
[2025-07-31 16:24:37.700 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 16:24:37.707 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 16:24:37.714 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 16:24:37.727 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\debug
[2025-07-31 16:24:37.735 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\info
[2025-07-31 16:24:37.743 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\warn
[2025-07-31 16:24:37.751 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\error
[2025-07-31 16:24:37.760 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\bin\Debug\net8.0\logs\frontend\trace
[2025-07-31 16:24:37.770 +08:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 16:24:37.794 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 16:24:37.801 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 16:24:39.027 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:24:39.036 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:24:39.045 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:24:39.054 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:24:39.062 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 16:24:39.415 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 16:24:39.424 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 16:24:39.569 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 16:24:39.579 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 16:24:39.586 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 16:24:39.797 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 16:24:39.831 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 16:24:39.907 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 16:24:39.915 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 16:24:40.250 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 16:24:40.262 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 16:24:40.272 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 16:24:40.278 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 16:24:40.284 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 16:24:40.292 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 16:24:40.449 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 16:24:40.455 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 16:24:40.465 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 16:24:40.471 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 16:24:40.477 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 16:24:40.485 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 16:24:40.493 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 16:24:40.500 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 16:24:40.513 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 16:24:40.524 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 16:24:40.533 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 16:24:40.544 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 16:24:40.558 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 16:24:40.569 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 16:24:40.582 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 16:24:40.595 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 16:24:40.608 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 16:24:40.669 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 16:24:40.694 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 16:24:40.704 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 16:24:40.712 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 16:24:40.720 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 16:24:40.726 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 16:24:40.731 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 16:24:40.737 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 16:24:40.742 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 16:24:40.747 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 16:24:40.752 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 16:24:40.757 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 16:24:40.761 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 16:24:40.765 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 16:24:40.769 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 16:24:40.785 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 16:24:40.796 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 16:24:40.800 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 16:24:40.805 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 16:24:40.809 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:24:40.813 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 16:24:40.818 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 16:24:40.821 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 16:24:40.825 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 16:24:40.839 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 16:24:40.845 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 16:24:40.849 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 16:24:40.862 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 16:24:41.066 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 16:24:41.073 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 16:24:41.079 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 16:24:41.084 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 16:24:41.090 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 16:24:41.096 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 16:24:41.110 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 16:24:41.116 +08:00 INF] Program: 后台初始化完成
[2025-07-31 16:24:42.623 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 16:24:47.599 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 16:25:07.476 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 16:25:10.745 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:25:40.738 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:26:10.737 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:26:40.732 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:27:14.948 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:27:44.948 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:28:14.948 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:28:44.949 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:29:14.957 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 16:29:44.954 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
