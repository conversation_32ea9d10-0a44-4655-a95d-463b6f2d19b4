import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  ReactFlow,
  ConnectionMode,
  useNodesState,
  useEdgesState,
  useReactFlow,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  MarkerType,
  Panel,
  SelectionMode,
} from '@xyflow/react';
import type { Node, Edge, Connection, ReactFlowProps } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useWorkflowStore } from '../stores/workflowStore';
import { useExecutionStore } from '../stores/executionStore';
import { useNodeStore } from '../stores/nodeStore';
import { useNotifications } from './NotificationToast';
import { pluginApi, executionApi } from '../services/api';

import { NodeFactory } from '../services/nodeFactory';
import { nodeStatusManager } from '../services/nodeStatusManager';
import { useNodeDeletion } from '../hooks/useNodeDeletion';
import { useWorkflowOperations } from '../hooks/useWorkflowOperations';
import type { WorkflowNode, WorkflowConnection, NodeTypeDefinition, WorkflowExecution } from '../types/workflow';
import { EnhancedNodePalette } from './EnhancedNodePalette';
import { NodeConfigManager } from './NodeConfig/NodeConfigManager';
import { WorkflowToolbar } from './WorkflowToolbar';
import { CustomNode } from './CustomNode';
import { AnimatedEdge } from './AnimatedEdge';
import { LogMonitor } from './LogMonitor';
import { ExecutionHistory } from './ExecutionHistory';
import { ExecutionDetailModal } from './ExecutionDetailModal';
import { ExecutionStatusPanel } from './ExecutionStatusPanel';
import { ReactFlowPerformanceMonitor } from './ReactFlowPerformanceMonitor';
import { VersionManagementPanel } from './version/VersionManagementPanel';

import { useReactFlowPerformance, useReactFlowMonitor } from '../hooks/useReactFlowPerformance';
import { ChevronDown, ChevronRight } from 'lucide-react';

// 定义节点和边类型在组件外部，避免每次渲染重新创建
const NODE_TYPES = {
  custom: CustomNode,
  customNode: CustomNode, // 保持兼容性
} as const;

const EDGE_TYPES = {
  animated: AnimatedEdge,
} as const;

// 默认视口设置
const DEFAULT_VIEWPORT = { x: 0, y: 0, zoom: 1 };

export const WorkflowDesigner: React.FC = () => {
  // 工作流状态
  const {
    currentWorkflow,
    loading,
    error,
    loadWorkflow,
    createWorkflow,
    updateWorkflow,
    clearError,
  } = useWorkflowStore();

  // 执行状态
  const {
    currentExecution,
    isExecuting,
    nodeExecutions,
    startExecution,
    stopExecution,
    pauseExecution,
    resumeExecution,
  } = useExecutionStore();

  // 节点状态
  const { nodeTypes, loadNodeTypes } = useNodeStore();

  // 通知系统
  const { success, error: showError, warning } = useNotifications();

  // ReactFlow状态
  const [flowNodes, setFlowNodes, onNodesChange] = useNodesState([]);
  const [flowEdges, setFlowEdges, onEdgesChange] = useEdgesState([]);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);
  const { screenToFlowPosition, fitView, getViewport, setViewport } = useReactFlow();

  // UI状态
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isConfigPanelOpen, setIsConfigPanelOpen] = useState(false);
  const [isPerformanceMonitorVisible, setIsPerformanceMonitorVisible] = useState(false);
  const [isVersionPanelOpen, setIsVersionPanelOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showMinimap, setShowMinimap] = useState(true);
  const [canvasInfo, setCanvasInfo] = useState({
    zoom: 100,
    nodeCount: 0,
    connectionCount: 0,
  });

  // 性能监控
  const { metrics: performanceMetrics } = useReactFlowPerformance();
  const { fps, renderingTime } = useReactFlowMonitor();

  // 节点删除Hook
  const { deleteNode } = useNodeDeletion();

  // 工作流操作Hook
  const { addNode, addConnection, removeNode, removeConnection } = useWorkflowOperations();

  // 加载工作流和节点类型
  useEffect(() => {
    const initialize = async () => {
      try {
        await loadNodeTypes();
        console.log('✅ 节点类型加载完成');
      } catch (err) {
        console.error('❌ 节点类型加载失败:', err);
        showError('节点类型加载失败', err instanceof Error ? err.message : '未知错误');
      }
    };

    initialize();
  }, [loadNodeTypes, showError]);

  // 当前工作流变化时更新ReactFlow节点和边
  useEffect(() => {
    if (currentWorkflow) {
      const { nodes, edges } = convertToFlowFormat(currentWorkflow);
      setFlowNodes(nodes);
      setFlowEdges(edges);
      
      // 更新画布信息
      setCanvasInfo({
        zoom: Math.round(100),
        nodeCount: nodes.length,
        connectionCount: edges.length,
      });
    }
  }, [currentWorkflow, setFlowNodes, setFlowEdges]);

  // 转换工作流数据为ReactFlow格式
  const convertToFlowFormat = useCallback(() => {
    if (!currentWorkflow) return { nodes: [], edges: [] };

    const nodes: Node[] = currentWorkflow.nodes.map(node => ({
      id: node.id,
      type: 'custom',
      position: node.position,
      data: {
        ...node,
        onUpdate: (updates: Partial<WorkflowNode>) => {
          updateWorkflow(currentWorkflow.id, {
            nodes: currentWorkflow.nodes.map(n => 
              n.id === node.id ? { ...n, ...updates } : n
            )
          });
        },
        onDelete: () => {
          removeNode(node.id);
        },
      },
      dragHandle: '.node-drag-handle',
    }));

    const edges: Edge[] = currentWorkflow.connections.map(conn => ({
      id: conn.id,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceEndpointId,
      targetHandle: conn.targetEndpointId,
      type: 'animated',
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
      },
      data: {
        onDelete: () => {
          removeConnection(conn.id);
        },
      },
    }));

    return { nodes, edges };
  }, [currentWorkflow, updateWorkflow, removeNode, removeConnection]);

  // 处理节点点击
  const onNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id);
    setIsConfigPanelOpen(true);
  }, []);

  // 处理画布点击（取消选择）
  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    setIsConfigPanelOpen(false);
  }, []);

  // 处理连接线
  const onConnect = useCallback((connection: Connection) => {
    if (!currentWorkflow) return;
    
    try {
      const newConnection: WorkflowConnection = {
        id: `connection_${Date.now()}`,
        sourceNodeId: connection.source!,
        targetNodeId: connection.target!,
        sourceEndpointId: connection.sourceHandle || 'default',
        targetEndpointId: connection.targetHandle || 'default',
        workflowId: currentWorkflow.id,
      };

      addConnection(newConnection);
      success('连接创建成功', '已成功创建新的节点连接');
    } catch (err) {
      showError('连接创建失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentWorkflow, addConnection, success, showError]);

  // 处理节点拖拽结束
  const onNodeDragStop = useCallback((_: React.MouseEvent, node: Node) => {
    if (!currentWorkflow) return;
    
    try {
      updateWorkflow(currentWorkflow.id, {
        nodes: currentWorkflow.nodes.map(n => 
          n.id === node.id ? { ...n, position: node.position } : n
        )
      });
    } catch (err) {
      showError('节点位置更新失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentWorkflow, updateWorkflow, showError]);

  // 处理视口变化
  const onViewportChange = useCallback((viewport: { x: number; y: number; zoom: number }) => {
    setCanvasInfo(prev => ({
      ...prev,
      zoom: Math.round(viewport.zoom * 100)
    }));
  }, []);

  // 添加节点
  const handleAddNode = useCallback((nodeType: string, position: { x: number; y: number }) => {
    if (!currentWorkflow) return;
    
    try {
      const nodeDefinition = nodeTypes.find(nt => nt.type === nodeType);
      if (!nodeDefinition) {
        throw new Error(`未找到节点类型: ${nodeType}`);
      }

      const newNode = NodeFactory.createNode(nodeType, {
        id: `node_${Date.now()}`,
        name: `${nodeDefinition.displayName || nodeType}节点`,
        type: nodeType,
        position,
        parameters: {},
        inputEndpoints: nodeDefinition.inputEndpoints || [],
        outputEndpoints: nodeDefinition.outputEndpoints || [],
      });

      addNode(newNode);
      success('节点添加成功', `已成功添加${newNode.name}节点`);
    } catch (err) {
      showError('节点添加失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentWorkflow, nodeTypes, addNode, success, showError]);

  // 删除节点
  const handleDeleteNode = useCallback((nodeId: string) => {
    try {
      deleteNode(nodeId);
      success('节点删除成功', '已成功删除选中的节点');
    } catch (err) {
      showError('节点删除失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [deleteNode, success, showError]);

  // 执行工作流
  const handleExecuteWorkflow = useCallback(async (inputData?: Record<string, any>) => {
    if (!currentWorkflow) return;
    
    try {
      await startExecution(currentWorkflow.id, inputData);
      success('执行启动成功', '工作流开始执行');
    } catch (err) {
      showError('执行启动失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentWorkflow, startExecution, success, showError]);

  // 停止执行
  const handleStopExecution = useCallback(async () => {
    if (!currentExecution) return;
    
    try {
      await stopExecution(currentExecution.id);
      success('执行停止成功', '工作流执行已停止');
    } catch (err) {
      showError('执行停止失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentExecution, stopExecution, success, showError]);

  // 暂停执行
  const handlePauseExecution = useCallback(async () => {
    if (!currentExecution) return;
    
    try {
      await pauseExecution(currentExecution.id);
      success('执行暂停成功', '工作流执行已暂停');
    } catch (err) {
      showError('执行暂停失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentExecution, pauseExecution, success, showError]);

  // 恢复执行
  const handleResumeExecution = useCallback(async () => {
    if (!currentExecution) return;
    
    try {
      await resumeExecution(currentExecution.id);
      success('执行恢复成功', '工作流执行已恢复');
    } catch (err) {
      showError('执行恢复失败', err instanceof Error ? err.message : '未知错误');
    }
  }, [currentExecution, resumeExecution, success, showError]);

  // 适配视图
  const handleFitView = useCallback(() => {
    fitView({ duration: 200 });
  }, [fitView]);

  // 放大
  const handleZoomIn = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.zoomIn({ duration: 200 });
    }
  }, [reactFlowInstance]);

  // 缩小
  const handleZoomOut = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.zoomOut({ duration: 200 });
    }
  }, [reactFlowInstance]);

  // 切换侧边栏
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev);
  }, []);

  // 切换小地图
  const toggleMinimap = useCallback(() => {
    setShowMinimap(prev => !prev);
  }, []);

  // 切换配置面板
  const toggleConfigPanel = useCallback(() => {
    setIsConfigPanelOpen(prev => !prev);
  }, []);

  // 切换版本面板
  const toggleVersionPanel = useCallback(() => {
    setIsVersionPanelOpen(prev => !prev);
  }, []);

  // 切换性能监控
  const togglePerformanceMonitor = useCallback(() => {
    setIsPerformanceMonitorVisible(prev => !prev);
  }, []);

  // 获取节点执行状态
  const getNodeExecutionStatus = useCallback((nodeId: string) => {
    if (!currentExecution || !nodeExecutions) return 'idle';
    return nodeExecutions[nodeId]?.status || 'idle';
  }, [currentExecution, nodeExecutions]);

  // 获取节点执行结果
  const getNodeExecutionResult = useCallback((nodeId: string) => {
    if (!currentExecution || !nodeExecutions) return null;
    return nodeExecutions[nodeId]?.result || null;
  }, [currentExecution, nodeExecutions]);

  // 渲染节点状态指示器
  const renderNodeStatusIndicator = useCallback((nodeId: string) => {
    const status = getNodeExecutionStatus(nodeId);
    const colors = {
      idle: 'bg-gray-400',
      running: 'bg-blue-500',
      completed: 'bg-green-500',
      failed: 'bg-red-500',
      paused: 'bg-yellow-500',
    };

    return (
      <div className={`w-3 h-3 rounded-full ${colors[status as keyof typeof colors] || colors.idle}`} />
    );
  }, [getNodeExecutionStatus]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500">
          <div className="text-lg font-semibold">加载失败</div>
          <div className="text-sm">{error}</div>
          <button 
            onClick={clearError}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full w-full">
      <ReactFlowProvider>
        {/* 节点面板 */}
        {!sidebarCollapsed && (
          <div className="w-80 border-r border-gray-200 bg-white flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold">节点库</h2>
            </div>
            <div className="flex-1 overflow-y-auto">
              <EnhancedNodePalette 
                onNodeAdd={(nodeType, position) => handleAddNode(nodeType, position)}
              />
            </div>
          </div>
        )}

        {/* 主要画布区域 */}
        <div className="flex-1 flex flex-col">
          {/* 工具栏 */}
          <WorkflowToolbar
            isExecuting={isExecuting}
            sidebarCollapsed={sidebarCollapsed}
            showMinimap={showMinimap}
            canvasInfo={canvasInfo}
            onToggleSidebar={toggleSidebar}
            onToggleMinimap={toggleMinimap}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onFitView={handleFitView}
            onExecute={handleExecuteWorkflow}
            onStop={handleStopExecution}
            onPause={handlePauseExecution}
            onResume={handleResumeExecution}
            onTogglePerformanceMonitor={togglePerformanceMonitor}
          />

          {/* ReactFlow画布 */}
          <div className="flex-1 relative">
            <ReactFlow
              nodes={flowNodes}
              edges={flowEdges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onNodeClick={onNodeClick}
              onPaneClick={onPaneClick}
              onConnect={onConnect}
              onNodeDragStop={onNodeDragStop}
              onViewportChange={onViewportChange}
              onInit={setReactFlowInstance}
              nodeTypes={NODE_TYPES}
              edgeTypes={EDGE_TYPES}
              connectionMode={ConnectionMode.Loose}
              fitView
              fitViewOptions={{ duration: 200 }}
              defaultViewport={DEFAULT_VIEWPORT}
              minZoom={0.1}
              maxZoom={4}
              selectionOnDrag
              selectionMode={SelectionMode.Partial}
              elevateNodesOnSelect
              elevateEdgesOnSelect
              snapToGrid
              connectionRadius={20}
            >
              {showMinimap && (
                <MiniMap 
                  nodeColor={(n) => {
                    const status = getNodeExecutionStatus(n.id);
                    const colors = {
                      idle: '#E5E7EB',
                      running: '#3B82F6',
                      completed: '#10B981',
                      failed: '#EF4444',
                      paused: '#F59E0B',
                    };
                    return colors[status as keyof typeof colors] || colors.idle;
                  }}
                  maskColor="rgba(0, 0, 0, 0.3)"
                />
              )}
              <Controls showInteractive={false} />
              <Background 
                variant={BackgroundVariant.Lines} 
                gap={20} 
                size={1}
                color="#E5E7EB"
              />

              {/* 性能监控面板 */}
              {isPerformanceMonitorVisible && (
                <Panel position="top-left">
                  <ReactFlowPerformanceMonitor 
                    performanceData={{
                      fps,
                      renderingTime,
                      visibleNodes: flowNodes.length,
                      visibleEdges: flowEdges.length,
                      totalNodes: currentWorkflow?.nodes.length || 0,
                      totalEdges: currentWorkflow?.connections.length || 0,
                      ...performanceMetrics
                    }}
                    isVisible={isPerformanceMonitorVisible}
                    position="top-left"
                  />
                </Panel>
              )}

              {/* 版本管理面板 */}
              {isVersionPanelOpen && (
                <Panel position="top-right">
                  <VersionManagementPanel 
                    workflowId={currentWorkflow?.id || ''}
                    onClose={() => setIsVersionPanelOpen(false)}
                  />
                </Panel>
              )}
            </ReactFlow>
          </div>
        </div>

        {/* 节点配置面板 */}
        {isConfigPanelOpen && selectedNode && (
          <div className="w-96 border-l border-gray-200 bg-white flex flex-col">
            <NodeConfigManager 
              nodeId={selectedNode}
              onClose={() => {
                setIsConfigPanelOpen(false);
                setSelectedNode(null);
              }}
            />
          </div>
        )}

        {/* 执行详情模态框 */}
        {currentExecution && (
          <ExecutionDetailModal
            execution={currentExecution}
            onClose={() => {
              // 这里可以添加停止执行的逻辑
            }}
          />
        )}
      </ReactFlowProvider>
    </div>
  );
};