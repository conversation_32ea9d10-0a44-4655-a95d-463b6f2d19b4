{"version": 3, "file": "MessageBuffer.js", "sourceRoot": "", "sources": ["../../src/MessageBuffer.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAGvE,OAAO,EAAwC,WAAW,EAAmB,MAAM,gBAAgB,CAAC;AACpG,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC,eAAe;AACf,MAAM,OAAO,aAAa;IAkBtB,YAAY,QAAsB,EAAE,UAAuB,EAAE,UAAkB;QAd9D,gBAAW,GAAW,MAAO,CAAC;QAEvC,cAAS,GAAmB,EAAE,CAAC;QAC/B,uBAAkB,GAAW,CAAC,CAAC;QAC/B,4BAAuB,GAAY,KAAK,CAAC;QAEjD,mDAAmD;QAC3C,6BAAwB,GAAG,CAAC,CAAC;QAC7B,8BAAyB,GAAG,CAAC,CAAC;QAC9B,uBAAkB,GAAW,CAAC,CAAC;QAC/B,yBAAoB,GAAY,KAAK,CAAC;QAK1C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,OAAmB;QAClC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE/D,IAAI,mBAAmB,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;QAE3D,yFAAyF;QACzF,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,2BAA2B,GAA0B,GAAG,EAAE,GAAE,CAAC,CAAC;YAClE,IAAI,2BAA2B,GAA2B,GAAG,EAAE,GAAE,CAAC,CAAC;YAEnE,IAAI,aAAa,CAAC,iBAAiB,CAAC,EAAE;gBAClC,IAAI,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,UAAU,CAAC;aAC3D;iBAAM;gBACH,IAAI,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,MAAM,CAAC;aACvD;YAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC7C,mBAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAClD,2BAA2B,GAAG,OAAO,CAAC;oBACtC,2BAA2B,GAAG,MAAM,CAAC;gBACzC,CAAC,CAAC,CAAC;aACN;YAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAC3E,2BAA2B,EAAE,2BAA2B,CAAC,CAAC,CAAC;SAClE;QAED,IAAI;YACA,2DAA2D;YAC3D,qDAAqD;YACrD,+EAA+E;YAC/E,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAClD;SACJ;QAAC,MAAM;YACJ,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QACD,MAAM,mBAAmB,CAAC;IAC9B,CAAC;IAEM,IAAI,CAAC,UAAsB;QAC9B,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;QAE5B,2CAA2C;QAC3C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,OAAO,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE;gBACtC,kBAAkB,GAAG,KAAK,CAAC;gBAC3B,IAAI,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBACjC,IAAI,CAAC,kBAAkB,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;iBAC1D;qBAAM;oBACH,IAAI,CAAC,kBAAkB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;iBACtD;gBACD,sDAAsD;gBACtD,OAAO,CAAC,SAAS,EAAE,CAAC;aACvB;iBAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE;gBACnD,4EAA4E;gBAC5E,OAAO,CAAC,SAAS,EAAE,CAAC;aACvB;iBAAM;gBACH,MAAM;aACT;SACJ;QAED,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE;YAC3B,uEAAuE;YACvE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;SACjE;IACL,CAAC;IAEM,qBAAqB,CAAC,OAAmB;QAC5C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,QAAQ,EAAE;gBACvC,OAAO,KAAK,CAAC;aAChB;iBAAM;gBACH,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACrC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,8CAA8C;QAC9C,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAChD,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,SAAS,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAC7C,IAAI,SAAS,KAAK,IAAI,CAAC,yBAAyB,EAAE;gBAC9C,wEAAwE;gBACxE,sFAAsF;gBACtF,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;YACD,sCAAsC;YACtC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;QAE3C,+GAA+G;QAC/G,wGAAwG;QACxG,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,OAAwB;QAC1C,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE;YACpD,mEAAmE;YACnE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC,CAAC;YAChG,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU,CAAC;IACvD,CAAC;IAEM,aAAa;QAChB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAEM,KAAK,CAAC,OAAO;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAC1C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG;YACvB,CAAC,CAAE,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QACnC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;QAErG,yFAAyF;QACzF,mEAAmE;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAEM,QAAQ,CAAC,KAAa;QACzB,KAAK,aAAL,KAAK,cAAL,KAAK,IAAL,KAAK,GAAK,IAAI,KAAK,CAAC,gCAAgC,CAAC,EAAA;QAErD,8BAA8B;QAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC5B;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAmB;QAC5C,iEAAiE;QACjE,+DAA+D;QAC/D,uEAAuE;QACvE,yDAAyD;QACzD,qFAAqF;QACrF,QAAQ,OAAO,CAAC,IAAI,EAAE;YAClB,KAAK,WAAW,CAAC,UAAU,CAAC;YAC5B,KAAK,WAAW,CAAC,UAAU,CAAC;YAC5B,KAAK,WAAW,CAAC,UAAU,CAAC;YAC5B,KAAK,WAAW,CAAC,gBAAgB,CAAC;YAClC,KAAK,WAAW,CAAC,gBAAgB;gBAC7B,OAAO,IAAI,CAAC;YAChB,KAAK,WAAW,CAAC,KAAK,CAAC;YACvB,KAAK,WAAW,CAAC,QAAQ,CAAC;YAC1B,KAAK,WAAW,CAAC,IAAI,CAAC;YACtB,KAAK,WAAW,CAAC,GAAG;gBAChB,OAAO,KAAK,CAAC;SACpB;IACL,CAAC;IAEO,SAAS;QACb,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACpC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;gBACzC,IAAI;oBACA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;wBAC5B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAA;qBAClI;oBACL,sGAAsG;iBACrG;gBAAC,MAAM,GAAG;gBAEX,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACnC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;gBACrC,kGAAkG;YAClG,CAAC,EAAE,IAAI,CAAC,CAAC;SACZ;IACL,CAAC;CACJ;AAED,MAAM,YAAY;IACd,YAAY,OAA6B,EAAE,EAAU,EAAE,QAA+B,EAAE,QAA+B;QACnH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CAMJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AckMessage, HubMessage, IHubProtocol, MessageType, SequenceMessage } from \"./IHubProtocol\";\r\nimport { is<PERSON><PERSON><PERSON><PERSON>uffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class MessageBuffer {\r\n    private readonly _protocol: IHubProtocol;\r\n    private readonly _connection: IConnection;\r\n\r\n    private readonly _bufferSize: number = 100_000;\r\n\r\n    private _messages: BufferedItem[] = [];\r\n    private _totalMessageCount: number = 0;\r\n    private _waitForSequenceMessage: boolean = false;\r\n\r\n    // Message IDs start at 1 and always increment by 1\r\n    private _nextReceivingSequenceId = 1;\r\n    private _latestReceivedSequenceId = 0;\r\n    private _bufferedByteCount: number = 0;\r\n    private _reconnectInProgress: boolean = false;\r\n\r\n    private _ackTimerHandle?: any;\r\n\r\n    constructor(protocol: IHubProtocol, connection: IConnection, bufferSize: number) {\r\n        this._protocol = protocol;\r\n        this._connection = connection;\r\n        this._bufferSize = bufferSize;\r\n    }\r\n\r\n    public async _send(message: HubMessage): Promise<void> {\r\n        const serializedMessage = this._protocol.writeMessage(message);\r\n\r\n        let backpressurePromise: Promise<void> = Promise.resolve();\r\n\r\n        // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\r\n        if (this._isInvocationMessage(message)) {\r\n            this._totalMessageCount++;\r\n            let backpressurePromiseResolver: (value: void) => void = () => {};\r\n            let backpressurePromiseRejector: (value?: void) => void = () => {};\r\n\r\n            if (isArrayBuffer(serializedMessage)) {\r\n                this._bufferedByteCount += serializedMessage.byteLength;\r\n            } else {\r\n                this._bufferedByteCount += serializedMessage.length;\r\n            }\r\n\r\n            if (this._bufferedByteCount >= this._bufferSize) {\r\n                backpressurePromise = new Promise((resolve, reject) => {\r\n                    backpressurePromiseResolver = resolve;\r\n                    backpressurePromiseRejector = reject;\r\n                });\r\n            }\r\n\r\n            this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount,\r\n                backpressurePromiseResolver, backpressurePromiseRejector));\r\n        }\r\n\r\n        try {\r\n            // If this is set it means we are reconnecting or resending\r\n            // We don't want to send on a disconnected connection\r\n            // And we don't want to send if resend is running since that would mean sending\r\n            // this message twice\r\n            if (!this._reconnectInProgress) {\r\n                await this._connection.send(serializedMessage);\r\n            }\r\n        } catch {\r\n            this._disconnected();\r\n        }\r\n        await backpressurePromise;\r\n    }\r\n\r\n    public _ack(ackMessage: AckMessage): void {\r\n        let newestAckedMessage = -1;\r\n\r\n        // Find index of newest message being acked\r\n        for (let index = 0; index < this._messages.length; index++) {\r\n            const element = this._messages[index];\r\n            if (element._id <= ackMessage.sequenceId) {\r\n                newestAckedMessage = index;\r\n                if (isArrayBuffer(element._message)) {\r\n                    this._bufferedByteCount -= element._message.byteLength;\r\n                } else {\r\n                    this._bufferedByteCount -= element._message.length;\r\n                }\r\n                // resolve items that have already been sent and acked\r\n                element._resolver();\r\n            } else if (this._bufferedByteCount < this._bufferSize) {\r\n                // resolve items that now fall under the buffer limit but haven't been acked\r\n                element._resolver();\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (newestAckedMessage !== -1) {\r\n            // We're removing everything including the message pointed to, so add 1\r\n            this._messages = this._messages.slice(newestAckedMessage + 1);\r\n        }\r\n    }\r\n\r\n    public _shouldProcessMessage(message: HubMessage): boolean {\r\n        if (this._waitForSequenceMessage) {\r\n            if (message.type !== MessageType.Sequence) {\r\n                return false;\r\n            } else {\r\n                this._waitForSequenceMessage = false;\r\n                return true;\r\n            }\r\n        }\r\n\r\n        // No special processing for acks, pings, etc.\r\n        if (!this._isInvocationMessage(message)) {\r\n            return true;\r\n        }\r\n\r\n        const currentId = this._nextReceivingSequenceId;\r\n        this._nextReceivingSequenceId++;\r\n        if (currentId <= this._latestReceivedSequenceId) {\r\n            if (currentId === this._latestReceivedSequenceId) {\r\n                // Should only hit this if we just reconnected and the server is sending\r\n                // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\r\n                this._ackTimer();\r\n            }\r\n            // Ignore, this is a duplicate message\r\n            return false;\r\n        }\r\n\r\n        this._latestReceivedSequenceId = currentId;\r\n\r\n        // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\r\n        // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\r\n        this._ackTimer();\r\n        return true;\r\n    }\r\n\r\n    public _resetSequence(message: SequenceMessage): void {\r\n        if (message.sequenceId > this._nextReceivingSequenceId) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\r\n            return;\r\n        }\r\n\r\n        this._nextReceivingSequenceId = message.sequenceId;\r\n    }\r\n\r\n    public _disconnected(): void {\r\n        this._reconnectInProgress = true;\r\n        this._waitForSequenceMessage = true;\r\n    }\r\n\r\n    public async _resend(): Promise<void> {\r\n        const sequenceId = this._messages.length !== 0\r\n            ? this._messages[0]._id\r\n            :  this._totalMessageCount + 1;\r\n        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Sequence, sequenceId }));\r\n\r\n        // Get a local variable to the _messages, just in case messages are acked while resending\r\n        // Which would slice the _messages array (which creates a new copy)\r\n        const messages = this._messages;\r\n        for (const element of messages) {\r\n            await this._connection.send(element._message);\r\n        }\r\n\r\n        this._reconnectInProgress = false;\r\n    }\r\n\r\n    public _dispose(error?: Error): void {\r\n        error ??= new Error(\"Unable to reconnect to server.\")\r\n\r\n        // Unblock backpressure if any\r\n        for (const element of this._messages) {\r\n            element._rejector(error);\r\n        }\r\n    }\r\n\r\n    private _isInvocationMessage(message: HubMessage): boolean {\r\n        // There is no way to check if something implements an interface.\r\n        // So we individually check the messages in a switch statement.\r\n        // To make sure we don't miss any message types we rely on the compiler\r\n        // seeing the function returns a value and it will do the\r\n        // exhaustive check for us on the switch statement, since we don't use 'case default'\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n            case MessageType.StreamItem:\r\n            case MessageType.Completion:\r\n            case MessageType.StreamInvocation:\r\n            case MessageType.CancelInvocation:\r\n                return true;\r\n            case MessageType.Close:\r\n            case MessageType.Sequence:\r\n            case MessageType.Ping:\r\n            case MessageType.Ack:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    private _ackTimer(): void {\r\n        if (this._ackTimerHandle === undefined) {\r\n            this._ackTimerHandle = setTimeout(async () => {\r\n                try {\r\n                    if (!this._reconnectInProgress) {\r\n                        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Ack, sequenceId: this._latestReceivedSequenceId }))\r\n                    }\r\n                // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\r\n                } catch { }\r\n\r\n                clearTimeout(this._ackTimerHandle);\r\n                this._ackTimerHandle = undefined;\r\n            // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\r\n            }, 1000);\r\n        }\r\n    }\r\n}\r\n\r\nclass BufferedItem {\r\n    constructor(message: string | ArrayBuffer, id: number, resolver: (value: void) => void, rejector: (value?: any) => void) {\r\n        this._message = message;\r\n        this._id = id;\r\n        this._resolver = resolver;\r\n        this._rejector = rejector;\r\n    }\r\n\r\n    _message: string | ArrayBuffer;\r\n    _id: number;\r\n    _resolver: (value: void) => void;\r\n    _rejector: (value?: any) => void;\r\n}\r\n"]}