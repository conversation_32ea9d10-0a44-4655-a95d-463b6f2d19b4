# FlowCustomV1 详细设计规格说明书 V3.0

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| 文档名称 | FlowCustomV1 详细设计规格说明书 |
| 文档版本 | v3.0.0 |
| 创建日期 | 2025-01-27 |
| 作者 | 系统架构师 |
| 状态 | 设计中 |

## 🎯 1. 系统层次结构

### 1.1 系统分解结构
```mermaid
graph TB
    subgraph "FlowCustomV1 工作流管理系统"
        subgraph "前端子系统"
            UI1[工作流设计器软件]
            UI2[执行监控器软件]
            UI3[管理界面软件]
        end
        
        subgraph "后端子系统"
            API1[API网关软件]
            BIZ1[业务逻辑软件]
            DATA1[数据访问软件]
        end
        
        subgraph "基础设施子系统"
            DB1[数据库软件]
            MSG1[消息队列软件]
            CACHE1[缓存软件]
        end
    end
```

### 1.2 系统层次表
| 层次 | 名称 | 描述 |
|------|------|------|
| 系统 | FlowCustomV1工作流管理系统 | 完整的工作流管理解决方案，支持可视化设计、分布式执行和实时监控 |
| 子系统 | 前端子系统 | 基于React 18的用户界面，提供工作流设计、监控和管理功能 |
| 子系统 | 后端子系统 | 基于.NET 8的服务端，处理业务逻辑、工作流执行和数据管理 |
| 子系统 | 基础设施子系统 | 数据存储、消息通信和缓存服务的基础设施支撑 |
| 软件 | 工作流设计器软件 | 可视化拖拽式工作流设计工具，支持节点配置和连接管理 |
| 软件 | 执行监控器软件 | 实时监控工作流执行状态，提供日志查看和控制操作 |
| 软件 | API网关软件 | RESTful API服务网关，处理HTTP请求和响应 |
| 软件 | 业务逻辑软件 | 工作流引擎核心，负责工作流解析、调度和执行 |

## 🏗️ 2. 前端子系统详细设计

### 2.1 工作流设计器软件

**软件描述**: 基于ReactFlow的可视化工作流设计器，提供拖拽式节点编辑、连接管理和属性配置功能，支持实时预览和验证。

#### 2.1.1 功能模块分解
```mermaid
graph TB
    subgraph "工作流设计器软件"
        M1[画布管理模块]
        M2[节点管理模块]
        M3[连接管理模块]
        M4[属性配置模块]
        M5[工具栏模块]
        M6[状态管理模块]
    end
    
    M1 --> M2
    M1 --> M3
    M2 --> M4
    M5 --> M1
    M6 --> M1
    M6 --> M2
    M6 --> M3
```

#### 2.1.2 画布管理模块

**模块描述**: 负责工作流画布的渲染、视图控制和用户交互处理，包括缩放、平移、节点拖拽等操作。

**功能职责**:
- 画布视图管理（缩放、平移、适应窗口）
- 节点位置跟踪和更新
- 用户交互事件处理
- 画布状态持久化

**输入接口**:
- 用户拖拽操作事件
- 节点位置数据更新
- 缩放和平移指令
- 画布尺寸变更事件

**输出接口**:
- 画布状态更新事件
- 节点位置变更事件
- 视图变更通知
- 用户操作反馈

**处理过程**:
```mermaid
flowchart TD
    A[接收用户操作] --> B{操作类型判断}
    B -->|拖拽节点| C[计算新位置]
    B -->|缩放画布| D[调整视图比例]
    B -->|平移画布| E[移动视图中心]
    B -->|选择节点| F[更新选择状态]
    
    C --> G[验证位置有效性]
    G --> H[更新节点位置]
    H --> I[触发位置更新事件]
    
    D --> J[限制缩放范围]
    J --> K[应用缩放变换]
    K --> L[触发缩放事件]
    
    E --> M[计算平移边界]
    M --> N[应用平移变换]
    N --> O[触发平移事件]
    
    F --> P[更新选择集合]
    P --> Q[触发选择事件]
    
    I --> R[更新画布状态]
    L --> R
    O --> R
    Q --> R
    R --> S[重新渲染画布]
```

**对应代码**:
```typescript
// WorkflowCanvas.tsx
export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onSelectionChange
}) => {
  const [viewport, setViewport] = useState<Viewport>({ x: 0, y: 0, zoom: 1 });
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);

  // 处理节点拖拽
  const handleNodeDrag = useCallback((event: NodeDragEvent, node: Node) => {
    const updatedNode = { 
      ...node, 
      position: {
        x: Math.max(0, event.position.x),
        y: Math.max(0, event.position.y)
      }
    };
    
    onNodesChange([{ 
      type: 'position', 
      id: node.id, 
      position: updatedNode.position 
    }]);
  }, [onNodesChange]);

  // 处理视图变更
  const handleViewportChange = useCallback((newViewport: Viewport) => {
    // 限制缩放范围
    const clampedViewport = {
      ...newViewport,
      zoom: Math.max(0.1, Math.min(2, newViewport.zoom))
    };
    
    setViewport(clampedViewport);
  }, []);

  // 处理节点选择
  const handleSelectionChange = useCallback((params: OnSelectionChangeParams) => {
    const nodeIds = params.nodes.map(node => node.id);
    setSelectedNodes(nodeIds);
    onSelectionChange?.(nodeIds);
  }, [onSelectionChange]);

  return (
    <div className="workflow-canvas h-full w-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDrag={handleNodeDrag}
        onSelectionChange={handleSelectionChange}
        viewport={viewport}
        onViewportChange={handleViewportChange}
        fitView
        attributionPosition="bottom-left"
      >
        <Background color="#f1f5f9" gap={20} />
        <Controls position="top-right" />
        <MiniMap 
          position="bottom-right"
          nodeColor="#3b82f6"
          maskColor="rgba(0, 0, 0, 0.1)"
        />
      </ReactFlow>
    </div>
  );
};
```

#### 2.1.3 节点管理模块

**模块描述**: 负责工作流节点的生命周期管理，包括节点创建、更新、删除、验证和类型管理。

**功能职责**:
- 节点类型定义和注册
- 节点实例创建和销毁
- 节点配置验证和更新
- 节点状态跟踪和同步

**输入接口**:
- 节点类型定义数据
- 节点创建请求
- 节点配置更新数据
- 节点删除指令

**输出接口**:
- 节点创建完成事件
- 节点更新完成事件
- 节点删除完成事件
- 节点验证结果

**类图**:
```mermaid
classDiagram
    class NodeManager {
        -nodeTypes: Map~string,NodeTypeDefinition~
        -activeNodes: Map~string,WorkflowNode~
        -validationRules: ValidationRule[]
        +registerNodeType(definition) void
        +createNode(type, position) WorkflowNode
        +updateNode(id, config) void
        +deleteNode(id) void
        +getNodeTypes() NodeTypeDefinition[]
        +validateNode(node) ValidationResult
        +getNodeById(id) WorkflowNode
        +getAllNodes() WorkflowNode[]
    }
    
    class NodeTypeDefinition {
        +id: string
        +name: string
        +displayName: string
        +category: string
        +description: string
        +icon: string
        +color: string
        +inputEndpoints: EndpointDefinition[]
        +outputEndpoints: EndpointDefinition[]
        +configSchema: ConfigurationSchema
        +defaultConfiguration: object
        +version: string
        +author: string
        +tags: string[]
    }
    
    class WorkflowNode {
        +id: string
        +type: string
        +name: string
        +displayName: string
        +position: Position
        +configuration: object
        +inputEndpoints: NodeEndpoint[]
        +outputEndpoints: NodeEndpoint[]
        +status: NodeStatus
        +createdAt: Date
        +updatedAt: Date
        +validate() ValidationResult
        +clone() WorkflowNode
        +toJSON() object
    }
    
    class ValidationResult {
        +isValid: boolean
        +errors: ValidationError[]
        +warnings: ValidationWarning[]
        +addError(message, field) void
        +addWarning(message, field) void
        +hasErrors() boolean
        +hasWarnings() boolean
    }
    
    NodeManager --> NodeTypeDefinition
    NodeManager --> WorkflowNode
    WorkflowNode --> ValidationResult
```

**对应代码**:
```typescript
// NodeManager.ts
export class NodeManager {
  private nodeTypes = new Map<string, NodeTypeDefinition>();
  private activeNodes = new Map<string, WorkflowNode>();
  private validationRules: ValidationRule[] = [];

  constructor() {
    this.initializeBuiltinNodeTypes();
    this.initializeValidationRules();
  }

  // 注册节点类型
  registerNodeType(definition: NodeTypeDefinition): void {
    if (this.nodeTypes.has(definition.id)) {
      throw new Error(`Node type ${definition.id} already registered`);
    }

    // 验证节点类型定义
    const validationResult = this.validateNodeTypeDefinition(definition);
    if (!validationResult.isValid) {
      throw new Error(`Invalid node type definition: ${validationResult.errors.join(', ')}`);
    }

    this.nodeTypes.set(definition.id, definition);
  }

  // 创建节点实例
  createNode(type: string, position: Position, initialConfig?: object): WorkflowNode {
    const nodeType = this.nodeTypes.get(type);
    if (!nodeType) {
      throw new Error(`Unknown node type: ${type}`);
    }

    const node: WorkflowNode = {
      id: generateUniqueId(),
      type,
      name: nodeType.name,
      displayName: nodeType.displayName,
      position: { ...position },
      configuration: { 
        ...nodeType.defaultConfiguration,
        ...initialConfig 
      },
      inputEndpoints: nodeType.inputEndpoints.map(ep => ({
        id: ep.id,
        name: ep.name,
        displayName: ep.displayName,
        dataType: ep.dataType,
        isRequired: ep.isRequired,
        isConnected: false,
        connectionId: null
      })),
      outputEndpoints: nodeType.outputEndpoints.map(ep => ({
        id: ep.id,
        name: ep.name,
        displayName: ep.displayName,
        dataType: ep.dataType,
        isConnected: false,
        connectionIds: []
      })),
      status: NodeStatus.Idle,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 验证新创建的节点
    const validationResult = this.validateNode(node);
    if (!validationResult.isValid) {
      throw new Error(`Failed to create valid node: ${validationResult.errors.join(', ')}`);
    }

    this.activeNodes.set(node.id, node);
    return node;
  }

  // 更新节点配置
  updateNode(id: string, updates: Partial<WorkflowNode>): void {
    const node = this.activeNodes.get(id);
    if (!node) {
      throw new Error(`Node ${id} not found`);
    }

    // 创建更新后的节点副本
    const updatedNode = {
      ...node,
      ...updates,
      updatedAt: new Date()
    };

    // 验证更新后的节点
    const validationResult = this.validateNode(updatedNode);
    if (!validationResult.isValid) {
      throw new Error(`Invalid node update: ${validationResult.errors.join(', ')}`);
    }

    this.activeNodes.set(id, updatedNode);
  }

  // 删除节点
  deleteNode(id: string): void {
    const node = this.activeNodes.get(id);
    if (!node) {
      throw new Error(`Node ${id} not found`);
    }

    // 检查是否有连接依赖
    const hasConnections = node.inputEndpoints.some(ep => ep.isConnected) ||
                          node.outputEndpoints.some(ep => ep.isConnected);
    
    if (hasConnections) {
      throw new Error(`Cannot delete node ${id}: has active connections`);
    }

    this.activeNodes.delete(id);
  }

  // 验证节点
  validateNode(node: WorkflowNode): ValidationResult {
    const result = new ValidationResult();
    const nodeType = this.nodeTypes.get(node.type);

    if (!nodeType) {
      result.addError(`Unknown node type: ${node.type}`, 'type');
      return result;
    }

    // 验证必需的输入端点
    for (const inputEp of node.inputEndpoints) {
      const epDef = nodeType.inputEndpoints.find(ep => ep.id === inputEp.id);
      if (epDef?.isRequired && !inputEp.isConnected) {
        result.addError(`Required input '${inputEp.displayName}' is not connected`, `input.${inputEp.id}`);
      }
    }

    // 验证配置参数
    if (nodeType.configSchema) {
      const configValidation = this.validateConfiguration(node.configuration, nodeType.configSchema);
      result.errors.push(...configValidation.errors);
      result.warnings.push(...configValidation.warnings);
    }

    // 应用自定义验证规则
    for (const rule of this.validationRules) {
      if (rule.appliesTo(node)) {
        const ruleResult = rule.validate(node);
        result.errors.push(...ruleResult.errors);
        result.warnings.push(...ruleResult.warnings);
      }
    }

    return result;
  }

  // 获取所有节点类型
  getNodeTypes(): NodeTypeDefinition[] {
    return Array.from(this.nodeTypes.values());
  }

  // 按分类获取节点类型
  getNodeTypesByCategory(category: string): NodeTypeDefinition[] {
    return Array.from(this.nodeTypes.values())
      .filter(nodeType => nodeType.category === category);
  }

  // 搜索节点类型
  searchNodeTypes(query: string): NodeTypeDefinition[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.nodeTypes.values())
      .filter(nodeType => 
        nodeType.name.toLowerCase().includes(lowerQuery) ||
        nodeType.displayName.toLowerCase().includes(lowerQuery) ||
        nodeType.description.toLowerCase().includes(lowerQuery) ||
        nodeType.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      );
  }

  private initializeBuiltinNodeTypes(): void {
    // 注册内置节点类型
    const builtinTypes: NodeTypeDefinition[] = [
      {
        id: 'start',
        name: 'Start',
        displayName: '开始',
        category: 'system',
        description: '工作流开始节点',
        icon: 'play',
        color: '#10b981',
        inputEndpoints: [],
        outputEndpoints: [
          {
            id: 'output',
            name: 'output',
            displayName: '输出',
            dataType: 'any',
            isRequired: false
          }
        ],
        configSchema: {
          type: 'object',
          properties: {
            name: { type: 'string', title: '节点名称', default: '开始' }
          }
        },
        defaultConfiguration: { name: '开始' },
        version: '1.0.0',
        author: 'FlowCustomV1',
        tags: ['system', 'trigger']
      },
      // ... 其他内置节点类型
    ];

    builtinTypes.forEach(type => this.registerNodeType(type));
  }

  private validateConfiguration(config: object, schema: ConfigurationSchema): ValidationResult {
    // 实现配置验证逻辑
    const result = new ValidationResult();
    // ... 验证实现
    return result;
  }
}
```

#### 2.1.4 连接管理模块

**模块描述**: 负责工作流节点间连接的创建、验证、更新和删除，确保数据流的正确性和完整性。

**功能职责**:
- 连接创建和删除
- 连接有效性验证
- 数据类型兼容性检查
- 连接状态跟踪

**输入接口**:
- 连接创建请求（源端点、目标端点）
- 连接删除请求
- 端点数据类型信息
- 连接验证规则

**输出接口**:
- 连接创建完成事件
- 连接删除完成事件
- 连接验证结果
- 数据流路径信息

**时序图**:
```mermaid
sequenceDiagram
    participant U as 用户
    participant CM as 连接管理模块
    participant VM as 验证模块
    participant NM as 节点管理模块
    participant SM as 状态管理模块
    
    U->>CM: 开始连接拖拽
    CM->>CM: 记录源端点信息
    CM->>NM: 获取源节点信息
    NM-->>CM: 返回节点和端点详情
    
    U->>CM: 拖拽到目标端点
    CM->>NM: 获取目标节点信息
    NM-->>CM: 返回节点和端点详情
    
    CM->>VM: 验证连接有效性
    VM->>VM: 检查数据类型兼容性
    VM->>VM: 检查循环依赖
    VM->>VM: 检查连接规则
    VM-->>CM: 返回验证结果
    
    alt 验证通过
        CM->>SM: 创建连接对象
        SM->>SM: 更新节点端点状态
        SM->>SM: 更新工作流状态
        SM-->>CM: 连接创建成功
        CM->>U: 显示连接线
        CM->>CM: 触发连接创建事件
    else 验证失败
        CM->>U: 显示错误提示
        CM->>CM: 取消连接操作
    end
```

**对应代码**:
```typescript
// ConnectionManager.ts
export class ConnectionManager {
  private connections = new Map<string, WorkflowConnection>();
  private validationRules: ConnectionValidationRule[] = [];

  constructor(private nodeManager: NodeManager) {
    this.initializeValidationRules();
  }

  // 创建连接
  createConnection(sourceNodeId: string, sourceEndpointId: string, 
                  targetNodeId: string, targetEndpointId: string): WorkflowConnection {
    
    // 获取源节点和目标节点
    const sourceNode = this.nodeManager.getNodeById(sourceNodeId);
    const targetNode = this.nodeManager.getNodeById(targetNodeId);
    
    if (!sourceNode || !targetNode) {
      throw new Error('Source or target node not found');
    }

    // 获取端点信息
    const sourceEndpoint = sourceNode.outputEndpoints.find(ep => ep.id === sourceEndpointId);
    const targetEndpoint = targetNode.inputEndpoints.find(ep => ep.id === targetEndpointId);
    
    if (!sourceEndpoint || !targetEndpoint) {
      throw new Error('Source or target endpoint not found');
    }

    // 创建连接对象
    const connection: WorkflowConnection = {
      id: generateUniqueId(),
      sourceNodeId,
      sourceEndpointId,
      targetNodeId,
      targetEndpointId,
      dataType: sourceEndpoint.dataType,
      status: ConnectionStatus.Active,
      createdAt: new Date(),
      metadata: {}
    };

    // 验证连接
    const validationResult = this.validateConnection(connection);
    if (!validationResult.isValid) {
      throw new Error(`Invalid connection: ${validationResult.errors.join(', ')}`);
    }

    // 更新端点状态
    sourceEndpoint.isConnected = true;
    sourceEndpoint.connectionIds.push(connection.id);
    targetEndpoint.isConnected = true;
    targetEndpoint.connectionId = connection.id;

    // 保存连接
    this.connections.set(connection.id, connection);

    return connection;
  }

  // 删除连接
  deleteConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      throw new Error(`Connection ${connectionId} not found`);
    }

    // 获取相关节点
    const sourceNode = this.nodeManager.getNodeById(connection.sourceNodeId);
    const targetNode = this.nodeManager.getNodeById(connection.targetNodeId);

    if (sourceNode && targetNode) {
      // 更新源端点状态
      const sourceEndpoint = sourceNode.outputEndpoints.find(ep => ep.id === connection.sourceEndpointId);
      if (sourceEndpoint) {
        sourceEndpoint.connectionIds = sourceEndpoint.connectionIds.filter(id => id !== connectionId);
        sourceEndpoint.isConnected = sourceEndpoint.connectionIds.length > 0;
      }

      // 更新目标端点状态
      const targetEndpoint = targetNode.inputEndpoints.find(ep => ep.id === connection.targetEndpointId);
      if (targetEndpoint) {
        targetEndpoint.isConnected = false;
        targetEndpoint.connectionId = null;
      }
    }

    // 删除连接
    this.connections.delete(connectionId);
  }

  // 验证连接
  validateConnection(connection: WorkflowConnection): ValidationResult {
    const result = new ValidationResult();

    // 获取节点和端点信息
    const sourceNode = this.nodeManager.getNodeById(connection.sourceNodeId);
    const targetNode = this.nodeManager.getNodeById(connection.targetNodeId);

    if (!sourceNode || !targetNode) {
      result.addError('Source or target node not found', 'nodes');
      return result;
    }

    const sourceEndpoint = sourceNode.outputEndpoints.find(ep => ep.id === connection.sourceEndpointId);
    const targetEndpoint = targetNode.inputEndpoints.find(ep => ep.id === connection.targetEndpointId);

    if (!sourceEndpoint || !targetEndpoint) {
      result.addError('Source or target endpoint not found', 'endpoints');
      return result;
    }

    // 检查自连接
    if (connection.sourceNodeId === connection.targetNodeId) {
      result.addError('Cannot connect node to itself', 'self-connection');
    }

    // 检查重复连接
    if (targetEndpoint.isConnected) {
      result.addError('Target endpoint already connected', 'duplicate-connection');
    }

    // 检查数据类型兼容性
    if (!this.isDataTypeCompatible(sourceEndpoint.dataType, targetEndpoint.dataType)) {
      result.addError(
        `Data type mismatch: ${sourceEndpoint.dataType} -> ${targetEndpoint.dataType}`,
        'data-type-mismatch'
      );
    }

    // 检查循环依赖
    if (this.wouldCreateCycle(connection)) {
      result.addError('Connection would create a cycle', 'circular-dependency');
    }

    // 应用自定义验证规则
    for (const rule of this.validationRules) {
      const ruleResult = rule.validate(connection, sourceNode, targetNode);
      result.errors.push(...ruleResult.errors);
      result.warnings.push(...ruleResult.warnings);
    }

    return result;
  }

  // 检查数据类型兼容性
  private isDataTypeCompatible(sourceType: string, targetType: string): boolean {
    // 'any' 类型兼容所有类型
    if (sourceType === 'any' || targetType === 'any') {
      return true;
    }

    // 相同类型直接兼容
    if (sourceType === targetType) {
      return true;
    }

    // 定义类型兼容性规则
    const compatibilityRules: Record<string, string[]> = {
      'string': ['text', 'json'],
      'number': ['integer', 'float', 'decimal'],
      'boolean': ['flag'],
      'object': ['json', 'array'],
      'array': ['list', 'collection']
    };

    return compatibilityRules[sourceType]?.includes(targetType) || false;
  }

  // 检查是否会创建循环依赖
  private wouldCreateCycle(newConnection: WorkflowConnection): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        return true;
      }

      if (visited.has(nodeId)) {
        return false;
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);

      // 获取当前节点的所有输出连接
      const outgoingConnections = Array.from(this.connections.values())
        .filter(conn => conn.sourceNodeId === nodeId);

      // 添加新连接到检查中
      if (newConnection.sourceNodeId === nodeId) {
        outgoingConnections.push(newConnection);
      }

      // 递归检查所有目标节点
      for (const connection of outgoingConnections) {
        if (hasCycle(connection.targetNodeId)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    return hasCycle(newConnection.targetNodeId);
  }

  // 获取节点的所有输入连接
  getNodeInputConnections(nodeId: string): WorkflowConnection[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.targetNodeId === nodeId);
  }

  // 获取节点的所有输出连接
  getNodeOutputConnections(nodeId: string): WorkflowConnection[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.sourceNodeId === nodeId);
  }

  // 获取工作流的执行路径
  getExecutionPath(): string[] {
    const path: string[] = [];
    const visited = new Set<string>();
    
    // 找到开始节点
    const startNodes = Array.from(this.nodeManager.getAllNodes())
      .filter(node => node.type === 'start');

    for (const startNode of startNodes) {
      this.traverseExecutionPath(startNode.id, path, visited);
    }

    return path;
  }

  private traverseExecutionPath(nodeId: string, path: string[], visited: Set<string>): void {
    if (visited.has(nodeId)) {
      return;
    }

    visited.add(nodeId);
    path.push(nodeId);

    // 获取输出连接并继续遍历
    const outputConnections = this.getNodeOutputConnections(nodeId);
    for (const connection of outputConnections) {
      this.traverseExecutionPath(connection.targetNodeId, path, visited);
    }
  }
}
```

## 📊 11. 性能优化规范

### 11.1 性能监控系统

**性能指标收集器**:
```csharp
// Performance/PerformanceMetricsCollector.cs
using System.Diagnostics;
using System.Runtime.GC;

namespace FlowCustomV1.Engine.Performance
{
    public interface IPerformanceMetricsCollector
    {
        Task<SystemMetrics> CollectSystemMetricsAsync();
        Task<ApplicationMetrics> CollectApplicationMetricsAsync();
        Task<WorkflowMetrics> CollectWorkflowMetricsAsync(Guid workflowId);
        void StartMetricsCollection();
        void StopMetricsCollection();
    }

    public class PerformanceMetricsCollector : IPerformanceMetricsCollector, IDisposable
    {
        private readonly ILogger<PerformanceMetricsCollector> _logger;
        private readonly Timer _metricsTimer;
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private readonly ConcurrentDictionary<Guid, WorkflowPerformanceTracker> _workflowTrackers;
        private readonly MetricsHistory _metricsHistory;

        public PerformanceMetricsCollector(ILogger<PerformanceMetricsCollector> logger)
        {
            _logger = logger;
            _workflowTrackers = new ConcurrentDictionary<Guid, WorkflowPerformanceTracker>();
            _metricsHistory = new MetricsHistory();

            // 初始化性能计数器
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法初始化性能计数器，将使用替代方案");
            }

            _metricsTimer = new Timer(CollectMetricsPeriodically, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task<SystemMetrics> CollectSystemMetricsAsync()
        {
            try
            {
                var metrics = new SystemMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsagePercent = await GetCpuUsageAsync(),
                    MemoryUsageMB = await GetMemoryUsageAsync(),
                    AvailableMemoryMB = await GetAvailableMemoryAsync(),
                    DiskUsagePercent = await GetDiskUsageAsync(),
                    NetworkBytesReceived = await GetNetworkBytesReceivedAsync(),
                    NetworkBytesSent = await GetNetworkBytesSentAsync(),
                    ActiveConnections = await GetActiveConnectionsAsync()
                };

                _metricsHistory.AddSystemMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集系统指标失败");
                return new SystemMetrics { Timestamp = DateTime.UtcNow };
            }
        }

        public async Task<ApplicationMetrics> CollectApplicationMetricsAsync()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var gcInfo = GC.GetTotalMemory(false);

                var metrics = new ApplicationMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    ProcessId = process.Id,
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount,
                    WorkingSetMB = process.WorkingSet64 / 1024 / 1024,
                    PrivateMemoryMB = process.PrivateMemorySize64 / 1024 / 1024,
                    GCMemoryMB = gcInfo / 1024 / 1024,
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2),
                    TotalProcessorTime = process.TotalProcessorTime,
                    UserProcessorTime = process.UserProcessorTime,
                    ActiveWorkflows = _workflowTrackers.Count,
                    TotalExecutions = _workflowTrackers.Values.Sum(t => t.ExecutionCount),
                    AverageExecutionTime = _workflowTrackers.Values.Any() 
                        ? _workflowTrackers.Values.Average(t => t.AverageExecutionTime.TotalMilliseconds)
                        : 0
                };

                _metricsHistory.AddApplicationMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集应用指标失败");
                return new ApplicationMetrics { Timestamp = DateTime.UtcNow };
            }
        }

        public async Task<WorkflowMetrics> CollectWorkflowMetricsAsync(Guid workflowId)
        {
            try
            {
                if (!_workflowTrackers.TryGetValue(workflowId, out var tracker))
                {
                    return new WorkflowMetrics
                    {
                        WorkflowId = workflowId,
                        Timestamp = DateTime.UtcNow
                    };
                }

                var metrics = new WorkflowMetrics
                {
                    WorkflowId = workflowId,
                    Timestamp = DateTime.UtcNow,
                    ExecutionCount = tracker.ExecutionCount,
                    SuccessCount = tracker.SuccessCount,
                    FailureCount = tracker.FailureCount,
                    AverageExecutionTime = tracker.AverageExecutionTime,
                    MinExecutionTime = tracker.MinExecutionTime,
                    MaxExecutionTime = tracker.MaxExecutionTime,
                    LastExecutionTime = tracker.LastExecutionTime,
                    ThroughputPerMinute = tracker.GetThroughputPerMinute(),
                    ErrorRate = tracker.GetErrorRate(),
                    ActiveExecutions = tracker.ActiveExecutions
                };

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集工作流指标失败: {WorkflowId}", workflowId);
                return new WorkflowMetrics
                {
                    WorkflowId = workflowId,
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        public void StartMetricsCollection()
        {
            _metricsTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(30));
            _logger.LogInformation("性能指标收集已启动");
        }

        public void StopMetricsCollection()
        {
            _metricsTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.LogInformation("性能指标收集已停止");
        }

        public void TrackWorkflowExecution(Guid workflowId, TimeSpan executionTime, bool success)
        {
            var tracker = _workflowTrackers.GetOrAdd(workflowId, _ => new WorkflowPerformanceTracker());
            tracker.RecordExecution(executionTime, success);
        }

        public void StartWorkflowExecution(Guid workflowId)
        {
            var tracker = _workflowTrackers.GetOrAdd(workflowId, _ => new WorkflowPerformanceTracker());
            tracker.StartExecution();
        }

        public void EndWorkflowExecution(Guid workflowId)
        {
            if (_workflowTrackers.TryGetValue(workflowId, out var tracker))
            {
                tracker.EndExecution();
            }
        }

        private async Task<double> GetCpuUsageAsync()
        {
            try
            {
                if (_cpuCounter != null)
                {
                    return _cpuCounter.NextValue();
                }

                // 替代方案：使用Process类
                var startTime = DateTime.UtcNow;
                var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
                await Task.Delay(1000);
                var endTime = DateTime.UtcNow;
                var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;

                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

                return cpuUsageTotal * 100;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetMemoryUsageAsync()
        {
            try
            {
                return Process.GetCurrentProcess().WorkingSet64 / 1024 / 1024;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetAvailableMemoryAsync()
        {
            try
            {
                if (_memoryCounter != null)
                {
                    return (long)_memoryCounter.NextValue();
                }

                // 替代方案：使用GC信息
                var totalMemory = GC.GetTotalMemory(false);
                return Math.Max(0, 1024 - totalMemory / 1024 / 1024); // 假设1GB可用内存
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> GetDiskUsageAsync()
        {
            try
            {
                var drives = DriveInfo.GetDrives();
                var systemDrive = drives.FirstOrDefault(d => d.Name == Path.GetPathRoot(Environment.SystemDirectory));
                
                if (systemDrive != null)
                {
                    var usedSpace = systemDrive.TotalSize - systemDrive.AvailableFreeSpace;
                    return (double)usedSpace / systemDrive.TotalSize * 100;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetNetworkBytesReceivedAsync()
        {
            // 简化实现，实际应该使用NetworkInterface类
            return 0;
        }

        private async Task<long> GetNetworkBytesSentAsync()
        {
            // 简化实现，实际应该使用NetworkInterface类
            return 0;
        }

        private async Task<int> GetActiveConnectionsAsync()
        {
            // 简化实现，实际应该使用网络统计API
            return 0;
        }

        private async void CollectMetricsPeriodically(object state)
        {
            try
            {
                await CollectSystemMetricsAsync();
                await CollectApplicationMetricsAsync();

                // 清理过期的工作流跟踪器
                var expiredTrackers = _workflowTrackers
                    .Where(kvp => kvp.Value.LastActivity < DateTime.UtcNow.AddHours(-1))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var expiredId in expiredTrackers)
                {
                    _workflowTrackers.TryRemove(expiredId, out _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期收集指标失败");
            }
        }

        public void Dispose()
        {
            _metricsTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
        }
    }

    // 性能指标数据模型
    public class SystemMetrics
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public long AvailableMemoryMB { get; set; }
        public double DiskUsagePercent { get; set; }
        public long NetworkBytesReceived { get; set; }
        public long NetworkBytesSent { get; set; }
        public int ActiveConnections { get; set; }
    }

    public class ApplicationMetrics
    {
        public DateTime Timestamp { get; set; }
        public int ProcessId { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public long WorkingSetMB { get; set; }
        public long PrivateMemoryMB { get; set; }
        public long GCMemoryMB { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
        public TimeSpan TotalProcessorTime { get; set; }
        public TimeSpan UserProcessorTime { get; set; }
        public int ActiveWorkflows { get; set; }
        public int TotalExecutions { get; set; }
        public double AverageExecutionTime { get; set; }
    }

    public class WorkflowMetrics
    {
        public Guid WorkflowId { get; set; }
        public DateTime Timestamp { get; set; }
        public int ExecutionCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public TimeSpan MinExecutionTime { get; set; }
        public TimeSpan MaxExecutionTime { get; set; }
        public DateTime? LastExecutionTime { get; set; }
        public double ThroughputPerMinute { get; set; }
        public double ErrorRate { get; set; }
        public int ActiveExecutions { get; set; }
    }

    // 工作流性能跟踪器
    public class WorkflowPerformanceTracker
    {
        private readonly object _lock = new object();
        private readonly List<ExecutionRecord> _executions = new List<ExecutionRecord>();
        private int _activeExecutions = 0;

        public int ExecutionCount { get; private set; }
        public int SuccessCount { get; private set; }
        public int FailureCount { get; private set; }
        public TimeSpan AverageExecutionTime { get; private set; }
        public TimeSpan MinExecutionTime { get; private set; } = TimeSpan.MaxValue;
        public TimeSpan MaxExecutionTime { get; private set; }
        public DateTime? LastExecutionTime { get; private set; }
        public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
        public int ActiveExecutions => _activeExecutions;

        public void RecordExecution(TimeSpan executionTime, bool success)
        {
            lock (_lock)
            {
                ExecutionCount++;
                LastExecutionTime = DateTime.UtcNow;
                LastActivity = DateTime.UtcNow;

                if (success)
                    SuccessCount++;
                else
                    FailureCount++;

                // 更新执行时间统计
                if (executionTime < MinExecutionTime)
                    MinExecutionTime = executionTime;

                if (executionTime > MaxExecutionTime)
                    MaxExecutionTime = executionTime;

                // 记录执行历史（保留最近100条）
                _executions.Add(new ExecutionRecord
                {
                    Timestamp = DateTime.UtcNow,
                    Duration = executionTime,
                    Success = success
                });

                if (_executions.Count > 100)
                {
                    _executions.RemoveAt(0);
                }

                // 计算平均执行时间
                AverageExecutionTime = TimeSpan.FromMilliseconds(
                    _executions.Average(e => e.Duration.TotalMilliseconds));
            }
        }

        public void StartExecution()
        {
            Interlocked.Increment(ref _activeExecutions);
            LastActivity = DateTime.UtcNow;
        }

        public void EndExecution()
        {
            Interlocked.Decrement(ref _activeExecutions);
            LastActivity = DateTime.UtcNow;
        }

        public double GetThroughputPerMinute()
        {
            lock (_lock)
            {
                var oneMinuteAgo = DateTime.UtcNow.AddMinutes(-1);
                var recentExecutions = _executions.Count(e => e.Timestamp >= oneMinuteAgo);
                return recentExecutions;
            }
        }

        public double GetErrorRate()
        {
            return ExecutionCount > 0 ? (double)FailureCount / ExecutionCount * 100 : 0;
        }

        private class ExecutionRecord
        {
            public DateTime Timestamp { get; set; }
            public TimeSpan Duration { get; set; }
            public bool Success { get; set; }
        }
    }

    // 指标历史记录
    public class MetricsHistory
    {
        private readonly Queue<SystemMetrics> _systemMetrics = new Queue<SystemMetrics>();
        private readonly Queue<ApplicationMetrics> _applicationMetrics = new Queue<ApplicationMetrics>();
        private readonly object _lock = new object();
        private const int MaxHistorySize = 1440; // 24小时的分钟数

        public void AddSystemMetrics(SystemMetrics metrics)
        {
            lock (_lock)
            {
                _systemMetrics.Enqueue(metrics);
                while (_systemMetrics.Count > MaxHistorySize)
                {
                    _systemMetrics.Dequeue();
                }
            }
        }

        public void AddApplicationMetrics(ApplicationMetrics metrics)
        {
            lock (_lock)
            {
                _applicationMetrics.Enqueue(metrics);
                while (_applicationMetrics.Count > MaxHistorySize)
                {
                    _applicationMetrics.Dequeue();
                }
            }
        }

        public IEnumerable<SystemMetrics> GetSystemMetricsHistory(TimeSpan duration)
        {
            lock (_lock)
            {
                var cutoff = DateTime.UtcNow - duration;
                return _systemMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            }
        }

        public IEnumerable<ApplicationMetrics> GetApplicationMetricsHistory(TimeSpan duration)
        {
            lock (_lock)
            {
                var cutoff = DateTime.UtcNow - duration;
                return _applicationMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            }
        }
    }
}
```

### 11.2 缓存优化策略

**多层缓存系统**:
```csharp
// Performance/CacheService.cs
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace FlowCustomV1.Engine.Performance
{
    public interface ICacheService
    {
        Task<T> GetAsync<T>(string key, CancellationToken cancellationToken = default);
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
        Task RemoveAsync(string key, CancellationToken cancellationToken = default);
        Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
        Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default);
        CacheStatistics GetStatistics();
    }

    public class MultiLevelCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<MultiLevelCacheService> _logger;
        private readonly CacheOptions _options;
        private readonly CacheStatistics _statistics;
        private readonly ConcurrentDictionary<string, HashSet<string>> _taggedKeys;

        public MultiLevelCacheService(
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            ILogger<MultiLevelCacheService> logger,
            IOptions<CacheOptions> options)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
            _options = options.Value;
            _statistics = new CacheStatistics();
            _taggedKeys = new ConcurrentDictionary<string, HashSet<string>>();
        }

        public async Task<T> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
                return default(T);

            try
            {
                // L1缓存：内存缓存
                if (_memoryCache.TryGetValue(key, out T memoryValue))
                {
                    _statistics.RecordHit(CacheLevel.Memory);
                    _logger.LogDebug("内存缓存命中: {Key}", key);
                    return memoryValue;
                }

                // L2缓存：分布式缓存
                var distributedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
                if (!string.IsNullOrEmpty(distributedValue))
                {
                    var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue);
                    
                    // 回填到内存缓存
                    var memoryExpiration = TimeSpan.FromMinutes(_options.MemoryCacheExpirationMinutes);
                    _memoryCache.Set(key, deserializedValue, memoryExpiration);
                    
                    _statistics.RecordHit(CacheLevel.Distributed);
                    _logger.LogDebug("分布式缓存命中: {Key}", key);
                    return deserializedValue;
                }

                _statistics.RecordMiss();
                _logger.LogDebug("缓存未命中: {Key}", key);
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存失败: {Key}", key);
                _statistics.RecordError();
                return default(T);
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key) || value == null)
                return;

            try
            {
                var actualExpiration = expiration ?? TimeSpan.FromMinutes(_options.DefaultExpirationMinutes);
                
                // 设置内存缓存
                var memoryExpiration = TimeSpan.FromMinutes(Math.Min(actualExpiration.TotalMinutes, _options.MemoryCacheExpirationMinutes));
                _memoryCache.Set(key, value, memoryExpiration);

                // 设置分布式缓存
                var serializedValue = JsonSerializer.Serialize(value);
                var distributedOptions = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = actualExpiration
                };
                
                await _distributedCache.SetStringAsync(key, serializedValue, distributedOptions, cancellationToken);

                _statistics.RecordSet();
                _logger.LogDebug("缓存设置成功: {Key}, 过期时间: {Expiration}", key, actualExpiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置缓存失败: {Key}", key);
                _statistics.RecordError();
            }
        }

        public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
                return;

            try
            {
                // 从内存缓存移除
                _memoryCache.Remove(key);

                // 从分布式缓存移除
                await _distributedCache.RemoveAsync(key, cancellationToken);

                _statistics.RecordRemove();
                _logger.LogDebug("缓存移除成功: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除缓存失败: {Key}", key);
                _statistics.RecordError();
            }
        }

        public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
        {
            try
            {
                // 注意：这是一个简化实现
                // 实际生产环境中，分布式缓存的模式删除需要特殊支持
                _logger.LogWarning("模式删除功能需要分布式缓存支持，当前仅清理内存缓存");
                
                // 清理内存缓存中匹配的键
                if (_memoryCache is MemoryCache mc)
                {
                    var field = typeof(MemoryCache).GetField("_coherentState", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var coherentState = field?.GetValue(mc);
                    var entriesCollection = coherentState?.GetType()
                        .GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var entries = (IDictionary)entriesCollection?.GetValue(coherentState);

                    if (entries != null)
                    {
                        var keysToRemove = new List<object>();
                        foreach (DictionaryEntry entry in entries)
                        {
                            if (entry.Key.ToString().Contains(pattern))
                            {
                                keysToRemove.Add(entry.Key);
                            }
                        }

                        foreach (var key in keysToRemove)
                        {
                            _memoryCache.Remove(key);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模式删除缓存失败: {Pattern}", pattern);
                _statistics.RecordError();
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            // 先尝试获取缓存
            var cachedValue = await GetAsync<T>(key, cancellationToken);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            // 使用分布式锁防止缓存击穿
            var lockKey = $"lock:{key}";
            var lockExpiration = TimeSpan.FromMinutes(5);
            
            try
            {
                // 简化的分布式锁实现
                var lockValue = Guid.NewGuid().ToString();
                var lockOptions = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = lockExpiration
                };

                // 尝试获取锁
                var existingLock = await _distributedCache.GetStringAsync(lockKey, cancellationToken);
                if (existingLock == null)
                {
                    await _distributedCache.SetStringAsync(lockKey, lockValue, lockOptions, cancellationToken);
                    
                    try
                    {
                        // 再次检查缓存（双重检查）
                        cachedValue = await GetAsync<T>(key, cancellationToken);
                        if (cachedValue != null)
                        {
                            return cachedValue;
                        }

                        // 执行工厂方法
                        var value = await factory();
                        if (value != null)
                        {
                            await SetAsync(key, value, expiration, cancellationToken);
                        }

                        return value;
                    }
                    finally
                    {
                        // 释放锁
                        await _distributedCache.RemoveAsync(lockKey, cancellationToken);
                    }
                }
                else
                {
                    // 等待锁释放后重试
                    await Task.Delay(100, cancellationToken);
                    return await GetAsync<T>(key, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetOrSet操作失败: {Key}", key);
                
                // 降级：直接执行工厂方法
                return await factory();
            }
        }

        public async Task InvalidateTagAsync(string tag, CancellationToken cancellationToken = default)
        {
            try
            {
                if (_taggedKeys.TryGetValue(tag, out var keys))
                {
                    var tasks = keys.Select(key => RemoveAsync(key, cancellationToken));
                    await Task.WhenAll(tasks);
                    
                    _taggedKeys.TryRemove(tag, out _);
                    _logger.LogInformation("标签缓存失效完成: {Tag}, 影响键数量: {Count}", tag, keys.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "标签缓存失效失败: {Tag}", tag);
                _statistics.RecordError();
            }
        }

        public CacheStatistics GetStatistics()
        {
            return _statistics.Clone();
        }

        // 带标签的缓存设置
        public async Task SetWithTagsAsync<T>(string key, T value, string[] tags, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            await SetAsync(key, value, expiration, cancellationToken);

            // 记录标签关联
            foreach (var tag in tags)
            {
                _taggedKeys.AddOrUpdate(tag, 
                    new HashSet<string> { key },
                    (_, existing) => 
                    {
                        existing.Add(key);
                        return existing;
                    });
            }
        }
    }

    // 缓存配置选项
    public class CacheOptions
    {
        public int DefaultExpirationMinutes { get; set; } = 60;
        public int MemoryCacheExpirationMinutes { get; set; } = 15;
        public bool EnableDistributedCache { get; set; } = true;
        public bool EnableCompression { get; set; } = true;
        public int MaxMemoryCacheSizeMB { get; set; } = 100;
    }

    // 缓存统计
    public class CacheStatistics
    {
        private long _memoryHits = 0;
        private long _distributedHits = 0;
        private long _misses = 0;
        private long _sets = 0;
        private long _removes = 0;
        private long _errors = 0;

        public long MemoryHits => _memoryHits;
        public long DistributedHits => _distributedHits;
        public long TotalHits => _memoryHits + _distributedHits;
        public long Misses => _misses;
        public long Sets => _sets;
        public long Removes => _removes;
        public long Errors => _errors;
        public long TotalRequests => TotalHits + _misses;
        public double HitRate => TotalRequests > 0 ? (double)TotalHits / TotalRequests * 100 : 0;
        public double MemoryHitRate => TotalRequests > 0 ? (double)_memoryHits / TotalRequests * 100 : 0;

        public void RecordHit(CacheLevel level)
        {
            if (level == CacheLevel.Memory)
                Interlocked.Increment(ref _memoryHits);
            else
                Interlocked.Increment(ref _distributedHits);
        }

        public void RecordMiss() => Interlocked.Increment(ref _misses);
        public void RecordSet() => Interlocked.Increment(ref _sets);
        public void RecordRemove() => Interlocked.Increment(ref _removes);
        public void RecordError() => Interlocked.Increment(ref _errors);

        public CacheStatistics Clone()
        {
            return new CacheStatistics
            {
                _memoryHits = _memoryHits,
                _distributedHits = _distributedHits,
                _misses = _misses,
                _sets = _sets,
                _removes = _removes,
                _errors = _errors
            };
        }
    }

    public enum CacheLevel
    {
        Memory,
        Distributed
    }
}
```

## 📊 12. 部署运维规范

### 12.1 Docker容器化部署

**多阶段构建Dockerfile**:
```dockerfile
# Dockerfile
# 构建阶段
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.PluginHost/FlowCustomV1.PluginHost.csproj", "src/FlowCustomV1.PluginHost/"]
COPY ["src/FlowCustomV1.Data/FlowCustomV1.Data.csproj", "src/FlowCustomV1.Data/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制源代码
COPY . .

# 构建应用
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

# 发布阶段
FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 前端构建阶段
FROM node:18-alpine AS frontend-build
WORKDIR /frontend

# 复制前端文件
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

# 运行时阶段
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r flowcustom && useradd -r -g flowcustom flowcustom

# 复制应用文件
COPY --from=publish /app/publish .
COPY --from=frontend-build /frontend/dist ./wwwroot

# 创建必要的目录
RUN mkdir -p /app/logs /app/data /app/plugins && \
    chown -R flowcustom:flowcustom /app

# 设置环境变量
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 切换到非root用户
USER flowcustom

# 暴露端口
EXPOSE 8080

# 启动应用
ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll"]
```

**Docker Compose配置**:
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主应用服务
  flowcustom-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: flowcustom-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=postgres
```

### 12.2 部署自动化

**CI/CD流水线**:
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up .NET
        uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.0.x'
      - name: Build
        run: dotnet build --configuration Release
      - name: Test
        run: dotnet test --configuration Release

  publish:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up .NET
        uses: actions/setup-dotnet@v2
        with:
          dotnet-version: '8.0.x'
      - name: Publish
        run: dotnet publish --configuration Release --output publish
      - name: Deploy
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'flowcustom-api'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
```

### 12.3 监控与日志

**监控系统**:
```csharp
// Monitoring/MonitoringService.cs
using System.Diagnostics;
using System.Runtime.GC;

namespace FlowCustomV1.Engine.Monitoring
{
    public interface IMonitoringService
    {
        Task<SystemMetrics> CollectSystemMetricsAsync();
        Task<ApplicationMetrics> CollectApplicationMetricsAsync();
        Task<WorkflowMetrics> CollectWorkflowMetricsAsync(Guid workflowId);
        void StartMetricsCollection();
        void StopMetricsCollection();
    }

    public class MonitoringService : IMonitoringService, IDisposable
    {
        private readonly ILogger<MonitoringService> _logger;
        private readonly Timer _metricsTimer;
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private readonly ConcurrentDictionary<Guid, WorkflowPerformanceTracker> _workflowTrackers;
        private readonly MetricsHistory _metricsHistory;

        public MonitoringService(ILogger<MonitoringService> logger)
        {
            _logger = logger;
            _workflowTrackers = new ConcurrentDictionary<Guid, WorkflowPerformanceTracker>();
            _metricsHistory = new MetricsHistory();

            // 初始化性能计数器
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法初始化性能计数器，将使用替代方案");
            }

            _metricsTimer = new Timer(CollectMetricsPeriodically, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task<SystemMetrics> CollectSystemMetricsAsync()
        {
            try
            {
                var metrics = new SystemMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsagePercent = await GetCpuUsageAsync(),
                    MemoryUsageMB = await GetMemoryUsageAsync(),
                    AvailableMemoryMB = await GetAvailableMemoryAsync(),
                    DiskUsagePercent = await GetDiskUsageAsync(),
                    NetworkBytesReceived = await GetNetworkBytesReceivedAsync(),
                    NetworkBytesSent = await GetNetworkBytesSentAsync(),
                    ActiveConnections = await GetActiveConnectionsAsync()
                };

                _metricsHistory.AddSystemMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集系统指标失败");
                return new SystemMetrics { Timestamp = DateTime.UtcNow };
            }
        }

        public async Task<ApplicationMetrics> CollectApplicationMetricsAsync()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var gcInfo = GC.GetTotalMemory(false);

                var metrics = new ApplicationMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    ProcessId = process.Id,
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount,
                    WorkingSetMB = process.WorkingSet64 / 1024 / 1024,
                    PrivateMemoryMB = process.PrivateMemorySize64 / 1024 / 1024,
                    GCMemoryMB = gcInfo / 1024 / 1024,
                    Gen0Collections = GC.CollectionCount(0),
                    Gen1Collections = GC.CollectionCount(1),
                    Gen2Collections = GC.CollectionCount(2),
                    TotalProcessorTime = process.TotalProcessorTime,
                    UserProcessorTime = process.UserProcessorTime,
                    ActiveWorkflows = _workflowTrackers.Count,
                    TotalExecutions = _workflowTrackers.Values.Sum(t => t.ExecutionCount),
                    AverageExecutionTime = _workflowTrackers.Values.Any() 
                        ? _workflowTrackers.Values.Average(t => t.AverageExecutionTime.TotalMilliseconds)
                        : 0
                };

                _metricsHistory.AddApplicationMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集应用指标失败");
                return new ApplicationMetrics { Timestamp = DateTime.UtcNow };
            }
        }

        public async Task<WorkflowMetrics> CollectWorkflowMetricsAsync(Guid workflowId)
        {
            try
            {
                if (!_workflowTrackers.TryGetValue(workflowId, out var tracker))
                {
                    return new WorkflowMetrics
                    {
                        WorkflowId = workflowId,
                        Timestamp = DateTime.UtcNow
                    };
                }

                var metrics = new WorkflowMetrics
                {
                    WorkflowId = workflowId,
                    Timestamp = DateTime.UtcNow,
                    ExecutionCount = tracker.ExecutionCount,
                    SuccessCount = tracker.SuccessCount,
                    FailureCount = tracker.FailureCount,
                    AverageExecutionTime = tracker.AverageExecutionTime,
                    MinExecutionTime = tracker.MinExecutionTime,
                    MaxExecutionTime = tracker.MaxExecutionTime,
                    LastExecutionTime = tracker.LastExecutionTime,
                    ThroughputPerMinute = tracker.GetThroughputPerMinute(),
                    ErrorRate = tracker.GetErrorRate(),
                    ActiveExecutions = tracker.ActiveExecutions
                };

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集工作流指标失败: {WorkflowId}", workflowId);
                return new WorkflowMetrics
                {
                    WorkflowId = workflowId,
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        public void StartMetricsCollection()
        {
            _metricsTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(30));
            _logger.LogInformation("性能指标收集已启动");
        }

        public void StopMetricsCollection()
        {
            _metricsTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.LogInformation("性能指标收集已停止");
        }

        public void TrackWorkflowExecution(Guid workflowId, TimeSpan executionTime, bool success)
        {
            var tracker = _workflowTrackers.GetOrAdd(workflowId, _ => new WorkflowPerformanceTracker());
            tracker.RecordExecution(executionTime, success);
        }

        public void StartWorkflowExecution(Guid workflowId)
        {
            var tracker = _workflowTrackers.GetOrAdd(workflowId, _ => new WorkflowPerformanceTracker());
            tracker.StartExecution();
        }

        public void EndWorkflowExecution(Guid workflowId)
        {
            if (_workflowTrackers.TryGetValue(workflowId, out var tracker))
            {
                tracker.EndExecution();
            }
        }

        private async Task<double> GetCpuUsageAsync()
        {
            try
            {
                if (_cpuCounter != null)
                {
                    return _cpuCounter.NextValue();
                }

                // 替代方案：使用Process类
                var startTime = DateTime.UtcNow;
                var startCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;
                await Task.Delay(1000);
                var endTime = DateTime.UtcNow;
                var endCpuUsage = Process.GetCurrentProcess().TotalProcessorTime;

                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);

                return cpuUsageTotal * 100;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetMemoryUsageAsync()
        {
            try
            {
                return Process.GetCurrentProcess().WorkingSet64 / 1024 / 1024;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetAvailableMemoryAsync()
        {
            try
            {
                if (_memoryCounter != null)
                {
                    return (long)_memoryCounter.NextValue();
                }

                // 替代方案：使用GC信息
                var totalMemory = GC.GetTotalMemory(false);
                return Math.Max(0, 1024 - totalMemory / 1024 / 1024); // 假设1GB可用内存
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> GetDiskUsageAsync()
        {
            try
            {
                var drives = DriveInfo.GetDrives();
                var systemDrive = drives.FirstOrDefault(d => d.Name == Path.GetPathRoot(Environment.SystemDirectory));
                
                if (systemDrive != null)
                {
                    var usedSpace = systemDrive.TotalSize - systemDrive.AvailableFreeSpace;
                    return (double)usedSpace / systemDrive.TotalSize * 100;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<long> GetNetworkBytesReceivedAsync()
        {
            // 简化实现，实际应该使用NetworkInterface类
            return 0;
        }

        private async Task<long> GetNetworkBytesSentAsync()
        {
            // 简化实现，实际应该使用NetworkInterface类
            return 0;
        }

        private async Task<int> GetActiveConnectionsAsync()
        {
            // 简化实现，实际应该使用网络统计API
            return 0;
        }

        private async void CollectMetricsPeriodically(object state)
        {
            try
            {
                await CollectSystemMetricsAsync();
                await CollectApplicationMetricsAsync();

                // 清理过期的工作流跟踪器
                var expiredTrackers = _workflowTrackers
                    .Where(kvp => kvp.Value.LastActivity < DateTime.UtcNow.AddHours(-1))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var expiredId in expiredTrackers)
                {
                    _workflowTrackers.TryRemove(expiredId, out _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定期收集指标失败");
            }
        }

        public void Dispose()
        {
            _metricsTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
        }
    }

    // 性能指标数据模型
    public class SystemMetrics
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public long AvailableMemoryMB { get; set; }
        public double DiskUsagePercent { get; set; }
        public long NetworkBytesReceived { get; set; }
        public long NetworkBytesSent { get; set; }
        public int ActiveConnections { get; set; }
    }

    public class ApplicationMetrics
    {
        public DateTime Timestamp { get; set; }
        public int ProcessId { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public long WorkingSetMB { get; set; }
        public long PrivateMemoryMB { get; set; }
        public long GCMemoryMB { get; set; }
        public int Gen0Collections { get; set; }
        public int Gen1Collections { get; set; }
        public int Gen2Collections { get; set; }
        public TimeSpan TotalProcessorTime { get; set; }
        public TimeSpan UserProcessorTime { get; set; }
        public int ActiveWorkflows { get; set; }
        public int TotalExecutions { get; set; }
        public double AverageExecutionTime { get; set; }
    }

    public class WorkflowMetrics
    {
        public Guid WorkflowId { get; set; }
        public DateTime Timestamp { get; set; }
        public int ExecutionCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public TimeSpan MinExecutionTime { get; set; }
        public TimeSpan MaxExecutionTime { get; set; }
        public DateTime? LastExecutionTime { get; set; }
        public double ThroughputPerMinute { get; set; }
        public double ErrorRate { get; set; }
        public int ActiveExecutions { get; set; }
    }

    // 工作流性能跟踪器
    public class WorkflowPerformanceTracker
    {
        private readonly object _lock = new object();
        private readonly List<ExecutionRecord> _executions = new List<ExecutionRecord>();
        private int _activeExecutions = 0;

        public int ExecutionCount { get; private set; }
        public int SuccessCount { get; private set; }
        public int FailureCount { get; private set; }
        public TimeSpan AverageExecutionTime { get; private set; }
        public TimeSpan MinExecutionTime { get; private set; } = TimeSpan.MaxValue;
        public TimeSpan MaxExecutionTime { get; private set; }
        public DateTime? LastExecutionTime { get; private set; }
        public DateTime LastActivity { get; private set; } = DateTime.UtcNow;
        public int ActiveExecutions => _activeExecutions;

        public void RecordExecution(TimeSpan executionTime, bool success)
        {
            lock (_lock)
            {
                ExecutionCount++;
                LastExecutionTime = DateTime.UtcNow;
                LastActivity = DateTime.UtcNow;

                if (success)
                    SuccessCount++;
                else
                    FailureCount++;

                // 更新执行时间统计
                if (executionTime < MinExecutionTime)
                    MinExecutionTime = executionTime;

                if (executionTime > MaxExecutionTime)
                    MaxExecutionTime = executionTime;

                // 记录执行历史（保留最近100条）
                _executions.Add(new ExecutionRecord
                {
                    Timestamp = DateTime.UtcNow,
                    Duration = executionTime,
                    Success = success
                });

                if (_executions.Count > 100)
                {
                    _executions.RemoveAt(0);
                }

                // 计算平均执行时间
                AverageExecutionTime = TimeSpan.FromMilliseconds(
                    _executions.Average(e => e.Duration.TotalMilliseconds));
            }
        }

        public void StartExecution()
        {
            Interlocked.Increment(ref _activeExecutions);
            LastActivity = DateTime.UtcNow;
        }

        public void EndExecution()
        {
            Interlocked.Decrement(ref _activeExecutions);
            LastActivity = DateTime.UtcNow;
        }

        public double GetThroughputPerMinute()
        {
            lock (_lock)
            {
                var oneMinuteAgo = DateTime.UtcNow.AddMinutes(-1);
                var recentExecutions = _executions.Count(e => e.Timestamp >= oneMinuteAgo);
                return recentExecutions;
            }
        }

        public double GetErrorRate()
        {
            return ExecutionCount > 0 ? (double)FailureCount / ExecutionCount * 100 : 0;
        }

        private class ExecutionRecord
        {
            public DateTime Timestamp { get; set; }
            public TimeSpan Duration { get; set; }
            public bool Success { get; set; }
        }
    }

    // 指标历史记录
    public class MetricsHistory
    {
        private readonly Queue<SystemMetrics> _systemMetrics = new Queue<SystemMetrics>();
        private readonly Queue<ApplicationMetrics> _applicationMetrics = new Queue<ApplicationMetrics>();
        private readonly object _lock = new object();
        private const int MaxHistorySize = 1440; // 24小时的分钟数

        public void AddSystemMetrics(SystemMetrics metrics)
        {
            lock (_lock)
            {
                _systemMetrics.Enqueue(metrics);
                while (_systemMetrics.Count > MaxHistorySize)
                {
                    _systemMetrics.Dequeue();
                }
            }
        }

        public void AddApplicationMetrics(ApplicationMetrics metrics)
        {
            lock (_lock)
            {
                _applicationMetrics.Enqueue(metrics);
                while (_applicationMetrics.Count > MaxHistorySize)
                {
                    _applicationMetrics.Dequeue();
                }
            }
        }

        public IEnumerable<SystemMetrics> GetSystemMetricsHistory(TimeSpan duration)
        {
            lock (_lock)
            {
                var cutoff = DateTime.UtcNow - duration;
                return _systemMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            }
        }

        public IEnumerable<ApplicationMetrics> GetApplicationMetricsHistory(TimeSpan duration)
        {
            lock (_lock)
            {
                var cutoff = DateTime.UtcNow - duration;
                return _applicationMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            }
        }
    }
}
```

### 12.4 安全性与合规性

**安全策略**:
```csharp
// Security/SecurityPolicy.cs
using System.Security.Cryptography;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using System.Diagnostics;
using System.Numerics;
using System;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Http.Connections.Features;
using Microsoft.AspNetCore.Http.Connections
    }
  }

  private initializeValidationRules(): void {
    // 初始化连接验证规则
    this.validationRules = [
      new NoSelfConnectionRule(),
      new SingleInputConnectionRule(),
      new RequiredConnectionRule(),
      new DataFlowValidationRule()
    ];
  }
}
```

#### 2.1.5 属性配置模块

**模块描述**: 提供统一的节点属性配置界面，支持多种参数类型的编辑和验证，包括文本、数字、选择、JSON等。

**功能职责**:
- 动态配置界面生成
- 参数验证和类型转换
- 配置数据持久化
- 实时预览和反馈

**输入接口**:
- 节点配置模式定义
- 当前配置数据
- 用户输入事件
- 验证规则

**输出接口**:
- 配置更新事件

**对应代码**:
```typescript
// NodeConfigModal.tsx
import React, { useState, useEffect } from 'react';
import { Node } from 'reactflow';
import { useStore } from 'zustand';
import { useUIStore } from '../stores/uiStore';
import { useWorkflowStore } from '../stores/workflowStore';
import { NodeManager } from '../managers/NodeManager';
import { ConnectionManager } from '../managers/ConnectionManager';
import { NodeTypeDefinition } from '../models/NodeTypeDefinition';
import { WorkflowNode } from '../models/WorkflowNode';
import { ValidationResult } from '../models/ValidationResult';
import { RotateCcw, Save } from 'lucide-react';

interface NodeConfigModalProps {
  node: Node;
  onClose: () => void;
  readOnly?: boolean;
}

const NodeConfigModal: React.FC<NodeConfigModalProps> = ({ node, onClose, readOnly = false }) => {
  const { id, type, position, data } = node;
  const { nodeTypes } = useStore(useWorkflowStore);
  const { setNodeConfig } = useStore(useWorkflowStore);
  const { setNodePosition } = useStore(useWorkflowStore);
  const { setNodeStatus } = useStore(useWorkflowStore);
  const { setNodeValidationResult } = useStore(useWorkflowStore);

  const [config, setConfig] = useState(data.config || {});
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(readOnly);

  const nodeType = nodeTypes[type];

  const handleConfigChange = (newConfig: any) => {
    setConfig(newConfig);
    setIsDirty(true);
  };

  const validateConfig = async () => {
    if (!nodeType) return;

    const validation = await nodeManager.validateNode({
      ...node,
      configuration: config
    });

    setValidationResult(validation);
    return validation;
  };

  const saveConfig = async () => {
    setIsSaving(true);

    const validation = await validateConfig();
    if (!validation.isValid) {
      setIsSaving(false);
      return;
    }

    setNodeConfig(id, config);
    setIsDirty(false);
    setIsSaving(false);
  };

  const handleSave = async () => {
    await saveConfig();
    onClose();
  };

  const handleCancel = () => {
    if (isDirty) {
      if (window.confirm('您有未保存的更改，确定要放弃吗？')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  const handleToggleReadOnly = () => {
    setIsReadOnly(!isReadOnly);
  };

  useEffect(() => {
    setConfig(data.config || {});
    setIsDirty(false);
  }, [data.config]);

  if (!nodeType) {
    return (
      <div className="!p-4!">
        <div className="!text-red!">未知节点类型: {type}</div>
      </div>
    );
  }

  return (
    <div className="!p-4!">
      <div className="!flex! !justify-between! !items-center! !mb-4!">
        <div className="!font-bold!">{nodeType.displayName}</div>
        <div className="!flex! !space-x-2!">
          {!isReadOnly && (
            <button
              className="!btn! !btn-primary! !!isSaving! && !btn-disabled! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! && !!loading! !!isSaving! &&
  const { setNodeError } = useStore(useWorkflowStore);
  const { setNodeOutput } = useStore(useWorkflowStore);
  const { setNodeVariables } = useStore(useWorkflowStore);
  const { setNodeExecutionStatus } = useStore(useWorkflowStore);
  const { setNodeExecutionResult } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore(useWorkflowStore);
  const { setNodeExecutionInputData } = useStore(useWorkflowStore);
  const { setNodeExecutionOutputData } = useStore(useWorkflowStore);
  const { setNodeExecutionError } = useStore(useWorkflowStore);
  const { setNodeExecutionDuration } = useStore
### 5.4 状态管理模块

**模块描述**: 基于Zustand的轻量级状态管理，负责全局状态的统一管理和组件间通信。

**功能职责**:
- 工作流状态管理
- UI状态控制
- 数据缓存管理
- 状态持久化

**对应代码**:
```typescript
// stores/workflowStore.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  category?: string;
  tags?: string[];
  status: 'draft' | 'active' | 'inactive';
  version: number;
  variables?: Record<string, any>;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface WorkflowNode {
  id: string;
  name: string;
  type: string;
  configuration: Record<string, any>;
  position: { x: number; y: number };
  workflowId: string;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourceEndpointId: string;
  targetEndpointId: string;
  workflowId: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  startedBy?: string;
  parameters?: Record<string, any>;
  variables?: Record<string, any>;
  nodeOutputs?: Record<string, any>;
  error?: string;
  nodeExecutions: NodeExecution[];
}

export interface NodeExecution {
  id: string;
  workflowExecutionId: string;
  nodeId: string;
  nodeType: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  error?: string;
  duration?: number;
}

interface WorkflowState {
  // 当前工作流
  currentWorkflow: WorkflowDefinition | null;
  
  // 工作流列表
  workflows: WorkflowDefinition[];
  
  // 执行记录
  executions: WorkflowExecution[];
  
  // 加载状态
  isLoading: boolean;
  
  // 修改状态
  isModified: boolean;
  
  // 错误信息
  error: string | null;
  
  // 操作方法
  setCurrentWorkflow: (workflow: WorkflowDefinition | null) => void;
  setWorkflows: (workflows: WorkflowDefinition[]) => void;
  addWorkflow: (workflow: WorkflowDefinition) => void;
  updateWorkflow: (workflow: WorkflowDefinition) => void;
  removeWorkflow: (workflowId: string) => void;
  
  setExecutions: (executions: WorkflowExecution[]) => void;
  addExecution: (execution: WorkflowExecution) => void;
  updateExecution: (execution: WorkflowExecution) => void;
  
  setIsLoading: (loading: boolean) => void;
  setIsModified: (modified: boolean) => void;
  setError: (error: string | null) => void;
  
  // 清理方法
  reset: () => void;
}

const initialState = {
  currentWorkflow: null,
  workflows: [],
  executions: [],
  isLoading: false,
  isModified: false,
  error: null,
};

export const useWorkflowStore = create<WorkflowState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        setCurrentWorkflow: (workflow) => {
          set({ currentWorkflow: workflow, isModified: false }, false, 'setCurrentWorkflow');
        },
        
        setWorkflows: (workflows) => {
          set({ workflows }, false, 'setWorkflows');
        },
        
        addWorkflow: (workflow) => {
          set(
            (state) => ({
              workflows: [...state.workflows, workflow],
            }),
            false,
            'addWorkflow'
          );
        },
        
        updateWorkflow: (workflow) => {
          set(
            (state) => ({
              workflows: state.workflows.map(w => 
                w.id === workflow.id ? workflow : w
              ),
              currentWorkflow: state.currentWorkflow?.id === workflow.id 
                ? workflow 
                : state.currentWorkflow,
            }),
            false,
            'updateWorkflow'
          );
        },
        
        removeWorkflow: (workflowId) => {
          set(
            (state) => ({
              workflows: state.workflows.filter(w => w.id !== workflowId),
              currentWorkflow: state.currentWorkflow?.id === workflowId 
                ? null 
                : state.currentWorkflow,
            }),
            false,
            'removeWorkflow'
          );
        },
        
        setExecutions: (executions) => {
          set({ executions }, false, 'setExecutions');
        },
        
        addExecution: (execution) => {
          set(
            (state) => ({
              executions: [execution, ...state.executions],
            }),
            false,
            'addExecution'
          );
        },
        
        updateExecution: (execution) => {
          set(
            (state) => ({
              executions: state.executions.map(e => 
                e.id === execution.id ? execution : e
              ),
            }),
            false,
            'updateExecution'
          );
        },
        
        setIsLoading: (isLoading) => {
          set({ isLoading }, false, 'setIsLoading');
        },
        
        setIsModified: (isModified) => {
          set({ isModified }, false, 'setIsModified');
        },
        
        setError: (error) => {
          set({ error }, false, 'setError');
        },
        
        reset: () => {
          set(initialState, false, 'reset');
        },
      }),
      {
        name: 'workflow-store',
        partialize: (state) => ({
          workflows: state.workflows,
          currentWorkflow: state.currentWorkflow,
        }),
      }
    ),
    {
      name: 'workflow-store',
    }
  )
);

// stores/uiStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface UIState {
  // 面板显示状态
  showNodePalette: boolean;
  showNodeConfig: boolean;
  showExecutionPanel: boolean;
  showLogPanel: boolean;
  
  // 选中状态
  selectedNodeId: string | null;
  selectedExecutionId: string | null;
  
  // 布局状态
  sidebarCollapsed: boolean;
  panelSizes: Record<string, number>;
  
  // 主题设置
  theme: 'light' | 'dark';
  
  // 通知状态
  notifications: Notification[];
  
  // 操作方法
  setShowNodePalette: (show: boolean) => void;
  setShowNodeConfig: (show: boolean) => void;
  setShowExecutionPanel: (show: boolean) => void;
  setShowLogPanel: (show: boolean) => void;
  
  setSelectedNodeId: (nodeId: string | null) => void;
  setSelectedExecutionId: (executionId: string | null) => void;
  
  setSidebarCollapsed: (collapsed: boolean) => void;
  setPanelSize: (panelId: string, size: number) => void;
  
  setTheme: (theme: 'light' | 'dark') => void;
  
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // 切换方法
  toggleNodePalette: () => void;
  toggleNodeConfig: () => void;
  toggleExecutionPanel: () => void;
  toggleLogPanel: () => void;
  toggleSidebar: () => void;
  toggleTheme: () => void;
}

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  duration?: number;
  timestamp: number;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      showNodePalette: true,
      showNodeConfig: false,
      showExecutionPanel: false,
      showLogPanel: false,
      
      selectedNodeId: null,
      selectedExecutionId: null,
      
      sidebarCollapsed: false,
      panelSizes: {
        nodePalette: 280,
        nodeConfig: 320,
        executionPanel: 400,
        logPanel: 300,
      },
      
      theme: 'light',
      notifications: [],
      
      // 面板显示控制
      setShowNodePalette: (show) => {
        set({ showNodePalette: show }, false, 'setShowNodePalette');
      },
      
      setShowNodeConfig: (show) => {
        set({ showNodeConfig: show }, false, 'setShowNodeConfig');
      },
      
      setShowExecutionPanel: (show) => {
        set({ showExecutionPanel: show }, false, 'setShowExecutionPanel');
      },
      
      setShowLogPanel: (show) => {
        set({ showLogPanel: show }, false, 'setShowLogPanel');
      },
      
      // 选中状态控制
      setSelectedNodeId: (nodeId) => {
        set({ selectedNodeId: nodeId }, false, 'setSelectedNodeId');
      },
      
      setSelectedExecutionId: (executionId) => {
        set({ selectedExecutionId: executionId }, false, 'setSelectedExecutionId');
      },
      
      // 布局控制
      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed }, false, 'setSidebarCollapsed');
      },
      
      setPanelSize: (panelId, size) => {
        set(
          (state) => ({
            panelSizes: { ...state.panelSizes, [panelId]: size },
          }),
          false,
          'setPanelSize'
        );
      },
      
      // 主题控制
      setTheme: (theme) => {
        set({ theme }, false, 'setTheme');
        document.documentElement.classList.toggle('dark', theme === 'dark');
      },
      
      // 通知管理
      addNotification: (notification) => {
        const id = `notification-${Date.now()}-${Math.random()}`;
        const newNotification: Notification = {
          ...notification,
          id,
          timestamp: Date.now(),
        };
        
        set(
          (state) => ({
            notifications: [...state.notifications, newNotification],
          }),
          false,
          'addNotification'
        );
        
        // 自动移除通知
        if (notification.duration !== 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration || 5000);
        }
      },
      
      removeNotification: (id) => {
        set(
          (state) => ({
            notifications: state.notifications.filter(n => n.id !== id),
          }),
          false,
          'removeNotification'
        );
      },
      
      clearNotifications: () => {
        set({ notifications: [] }, false, 'clearNotifications');
      },
      
      // 切换方法
      toggleNodePalette: () => {
        set(
          (state) => ({ showNodePalette: !state.showNodePalette }),
          false,
          'toggleNodePalette'
        );
      },
      
      toggleNodeConfig: () => {
        set(
          (state) => ({ showNodeConfig: !state.showNodeConfig }),
          false,
          'toggleNodeConfig'
        );
      },
      
      toggleExecutionPanel: () => {
        set(
          (state) => ({ showExecutionPanel: !state.showExecutionPanel }),
          false,
          'toggleExecutionPanel'
        );
      },
      
      toggleLogPanel: () => {
        set(
          (state) => ({ showLogPanel: !state.showLogPanel }),
          false,
          'toggleLogPanel'
        );
      },
      
      toggleSidebar: () => {
        set(
          (state) => ({ sidebarCollapsed: !state.sidebarCollapsed }),
          false,
          'toggleSidebar'
        );
      },
      
      toggleTheme: () => {
        set(
          (state) => {
            const newTheme = state.theme === 'light' ? 'dark' : 'light';
            document.documentElement.classList.toggle('dark', newTheme === 'dark');
            return { theme: newTheme };
          },
          false,
          'toggleTheme'
        );
      },
    }),
    {
      name: 'ui-store',
    }
  )
);
```

### 5.5 API通信模块

**模块描述**: 统一的API客户端，负责与后端服务的HTTP通信和数据交换。

**功能职责**:
- HTTP请求封装
- 响应数据处理
- 错误处理和重试
- 请求缓存管理

**对应代码**:
```typescript
// services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  timestamp: string;
}

// 分页结果接口
export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// 过滤器接口
export interface WorkflowFilter {
  name?: string;
  category?: string;
  status?: string;
  tags?: string[];
  createdBy?: string;
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface ExecutionFilter {
  workflowId?: string;
  status?: string;
  startedBy?: string;
  startedAfter?: string;
  startedBefore?: string;
  pageNumber?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

// API客户端基类
class ApiClient {
  private instance: AxiosInstance;
  private requestQueue: Map<string, Promise<any>> = new Map();

  constructor(baseURL: string = 'http://localhost:5279/api') {
    this.instance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加请求时间戳
        config.metadata = { startTime: Date.now() };
        
        // 添加认证头（如果需要）
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 计算请求耗时
        const duration = Date.now() - response.config.metadata?.startTime;
        console.debug(`API请求耗时: ${response.config.url} - ${duration}ms`);

        return response;
      },
      (error) => {
        // 统一错误处理
        this.handleError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleError(error: any) {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          toast.error(data.message || '请求参数错误');
          break;
        case 401:
          toast.error('未授权访问');
          // 可以在这里处理登录跳转
          break;
        case 403:
          toast.error('权限不足');
          break;
        case 404:
          toast.error('资源不存在');
          break;
        case 500:
          toast.error('服务器内部错误');
          break;
        default:
          toast.error(data.message || '请求失败');
      }
    } else if (error.request) {
      // 网络错误
      toast.error('网络连接失败');
    } else {
      // 其他错误
      toast.error('请求失败');
    }
  }

  // 防重复请求
  private async deduplicateRequest<T>(
    key: string,
    requestFn: () => Promise<AxiosResponse<T>>
  ): Promise<AxiosResponse<T>> {
    if (this.requestQueue.has(key)) {
      return this.requestQueue.get(key);
    }

    const promise = requestFn().finally(() => {
      this.requestQueue.delete(key);
    });

    this.requestQueue.set(key, promise);
    return promise;
  }

  // GET请求
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const key = `GET:${url}:${JSON.stringify(config?.params || {})}`;
    
    const response = await this.deduplicateRequest(key, () =>
      this.instance.get<ApiResponse<T>>(url, config)
    );
    
    return response.data;
  }

  // POST请求
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // PUT请求
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  // DELETE请求
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }
}

// 创建API客户端实例
const apiClient = new ApiClient();

// 工作流API
export const workflowApi = {
  // 获取工作流列表
  async getWorkflows(filter?: WorkflowFilter): Promise<ApiResponse<PagedResult<any>>> {
    return apiClient.get('/workflows', { params: filter });
  },

  // 获取单个工作流
  async getWorkflow(id: string): Promise<ApiResponse<any>> {
    return apiClient.get(`/workflows/${id}`);
  },

  // 创建工作流
  async createWorkflow(workflow: any): Promise<ApiResponse<any>> {
    return apiClient.post('/workflows', workflow);
  },

  // 更新工作流
  async updateWorkflow(id: string, workflow: any): Promise<ApiResponse<any>> {
    return apiClient.put(`/workflows/${id}`, workflow);
  },

  // 删除工作流
  async deleteWorkflow(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`/workflows/${id}`);
  },

  // 执行工作流
  async executeWorkflow(id: string, parameters?: Record<string, any>): Promise<ApiResponse<any>> {
    return apiClient.post(`/workflows/${id}/execute`, { parameters });
  },

  // 停止工作流执行
  async stopExecution(executionId: string): Promise<ApiResponse<void>> {
    return apiClient.post(`/executions/${executionId}/stop`);
  },

  // 获取执行记录
  async getExecutions(filter?: ExecutionFilter): Promise<ApiResponse<PagedResult<any>>> {
    return apiClient.get('/executions', { params: filter });
  },

  // 获取单个执行记录
  async getExecution(id: string): Promise<ApiResponse<any>> {
    return apiClient.get(`/executions/${id}`);
  },
};

// 节点类型API
export const nodeTypeApi = {
  // 获取所有节点类型
  async getNodeTypes(): Promise<ApiResponse<any[]>> {
    return apiClient.get('/node-types');
  },

  // 获取单个节点类型
  async getNodeType(type: string): Promise<ApiResponse<any>> {
    return apiClient.get(`/node-types/${type}`);
  },

  // 获取节点类型分类
  async getNodeCategories(): Promise<ApiResponse<string[]>> {
    return apiClient.get('/node-types/categories');
  },
};

// 系统API
export const systemApi = {
  // 获取系统状态
  async getSystemStatus(): Promise<ApiResponse<any>> {
    return apiClient.get('/system/status');
  },

  // 获取系统配置
  async getSystemConfig(): Promise<ApiResponse<Record<string, any>>> {
    return apiClient.get('/system/config');
  },

  // 更新系统配置
  async updateSystemConfig(config: Record<string, any>): Promise<ApiResponse<void>> {
    return apiClient.put('/system/config', config);
  },

  // 获取系统日志
  async getSystemLogs(filter?: {
    level?: string;
    startTime?: string;
    endTime?: string;
    pageNumber?: number;
    pageSize?: number;
  }): Promise<ApiResponse<PagedResult<any>>> {
    return apiClient.get('/system/logs', { params: filter });
  },
};

export default apiClient;
```

## 📊 6. 工作流执行引擎软件

**软件描述**: 高性能的工作流执行引擎，支持异步并发执行、状态管理和错误处理。

### 6.1 功能模块分解
```mermaid
graph TB
    subgraph "工作流执行引擎软件"
        M1[执行调度模块]
        M2[节点执行模块]
        M3[状态管理模块]
        M4[错误处理模块]
        M5[性能监控模块]
        M6[资源管理模块]
    end
    
    M1 --> M2
    M1 --> M3
    M2 --> M4
    M3 --> M5
    M4 --> M5
    M5 --> M6
```

### 6.2 执行调度模块

**模块描述**: 工作流执行的核心调度器，负责节点执行顺序、并发控制和资源分配。

**功能职责**:
- 执行计划生成
- 节点依赖解析
- 并发控制管理
- 资源调度分配

**对应代码**:
```csharp
// Services/WorkflowEngine.cs
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Engine.Services
{
    public class WorkflowEngine : IWorkflowEngine
    {
        private readonly INodeExecutorFactory _executorFactory;
        private readonly IExecutionModeSelector _modeSelector;
        private readonly ILogger<WorkflowEngine> _logger;
        private readonly ConcurrentDictionary<Guid, WorkflowExecutionContext> _activeExecutions;
        private readonly SemaphoreSlim _concurrencyLimiter;

        public WorkflowEngine(
            INodeExecutorFactory executorFactory,
            IExecutionModeSelector modeSelector,
            ILogger<WorkflowEngine> logger)
        {
            _executorFactory = executorFactory;
            _modeSelector = modeSelector;
            _logger = logger;
            _activeExecutions = new ConcurrentDictionary<Guid, WorkflowExecutionContext>();
            _concurrencyLimiter = new SemaphoreSlim(Environment.ProcessorCount * 2);
        }

        public async Task<WorkflowExecutionResult> ExecuteAsync(
            WorkflowDefinition workflow,
            Dictionary<string, object> parameters = null,
            CancellationToken cancellationToken = default)
        {
            var executionId = Guid.NewGuid();
            var context = new WorkflowExecutionContext
            {
                ExecutionId = executionId,
                WorkflowId = workflow.Id,
                Parameters = parameters ?? new Dictionary<string, object>(),
                Variables = new Dictionary<string, object>(workflow.Variables ?? new Dictionary<string, object>()),
                NodeOutputs = new Dictionary<string, object>(),
                StartedAt = DateTime.UtcNow,
                Status = ExecutionStatus.Initializing
            };

            _activeExecutions[executionId] = context;

            try
            {
                _logger.LogInformation("开始执行工作流: {WorkflowId} - {ExecutionId}", workflow.Id, executionId);

                // 构建执行计划
                var executionPlan = await BuildExecutionPlanAsync(workflow, cancellationToken);
                context.ExecutionPlan = executionPlan;
                context.Status = ExecutionStatus.Running;

                // 执行工作流
                var result = await ExecuteWorkflowAsync(context, workflow, cancellationToken);

                _logger.LogInformation("工作流执行完成: {WorkflowId} - {ExecutionId} - {Status}", 
                    workflow.Id, executionId, result.Status);

                return result;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("工作流执行被取消: {WorkflowId} - {ExecutionId}", workflow.Id, executionId);
                context.Status = ExecutionStatus.Cancelled;
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "工作流执行失败: {WorkflowId} - {ExecutionId}", workflow.Id, executionId);
                context.Status = ExecutionStatus.Failed;
                context.Error = ex.Message;
                throw;
            }
            finally
            {
                context.CompletedAt = DateTime.UtcNow;
                _activeExecutions.TryRemove(executionId, out _);
            }
        }

        private async Task<ExecutionPlan> BuildExecutionPlanAsync(
            WorkflowDefinition workflow,
            CancellationToken cancellationToken)
        {
            var plan = new ExecutionPlan();
            var nodeMap = workflow.Nodes.ToDictionary(n => n.Id, n => n);
            var connectionMap = workflow.Connections.ToLookup(c => c.TargetNodeId, c => c);

            // 构建依赖图
            var dependencyGraph = new Dictionary<Guid, List<Guid>>();
            var inDegree = new Dictionary<Guid, int>();

            foreach (var node in workflow.Nodes)
            {
                dependencyGraph[node.Id] = new List<Guid>();
                inDegree[node.Id] = 0;
            }

            foreach (var connection in workflow.Connections)
            {
                dependencyGraph[connection.SourceNodeId].Add(connection.TargetNodeId);
                inDegree[connection.TargetNodeId]++;
            }

            // 拓扑排序生成执行层级
            var queue = new Queue<Guid>();
            var levels = new Dictionary<Guid, int>();

            // 找到入度为0的节点（开始节点）
            foreach (var kvp in inDegree)
            {
                if (kvp.Value == 0)
                {
                    queue.Enqueue(kvp.Key);
                    levels[kvp.Key] = 0;
                }
            }

            var maxLevel = 0;
            while (queue.Count > 0)
            {
                var currentNode = queue.Dequeue();
                var currentLevel = levels[currentNode];
                maxLevel = Math.Max(maxLevel, currentLevel);

                foreach (var dependentNode in dependencyGraph[currentNode])
                {
                    inDegree[dependentNode]--;
                    if (inDegree[dependentNode] == 0)
                    {
                        queue.Enqueue(dependentNode);
                        levels[dependentNode] = currentLevel + 1;
                    }
                }
            }

            // 检查循环依赖
            if (levels.Count != workflow.Nodes.Count)
            {
                throw new InvalidOperationException("工作流存在循环依赖");
            }

            // 按层级组织执行计划
            for (int level = 0; level <= maxLevel; level++)
            {
                var nodesAtLevel = levels.Where(kvp => kvp.Value == level).Select(kvp => kvp.Key).ToList();
                if (nodesAtLevel.Any())
                {
                    plan.ExecutionLevels.Add(new ExecutionLevel
                    {
                        Level = level,
                        NodeIds = nodesAtLevel,
                        CanExecuteInParallel = level > 0 // 第一层通常是开始节点，后续层级可以并行
                    });
                }
            }

            _logger.LogDebug("执行计划构建完成: {WorkflowId} - {Levels}层级 - {TotalNodes}个节点", 
                workflow.Id, plan.ExecutionLevels.Count, workflow.Nodes.Count);

            return plan;
        }

        private async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
            WorkflowExecutionContext context,
            WorkflowDefinition workflow,
            CancellationToken cancellationToken)
        {
            var nodeMap = workflow.Nodes.ToDictionary(n => n.Id, n => n);
            var nodeExecutions = new ConcurrentDictionary<Guid, NodeExecutionResult>();

            try
            {
                // 按层级执行节点
                foreach (var level in context.ExecutionPlan.ExecutionLevels)
                {
                    if (level.CanExecuteInParallel && level.NodeIds.Count > 1)
                    {
                        // 并行执行
                        var tasks = level.NodeIds.Select(async nodeId =>
                        {
                            await _concurrencyLimiter.WaitAsync(cancellationToken);
                            try
                            {
                                return await ExecuteNodeAsync(nodeId, nodeMap[nodeId], context, cancellationToken);
                            }
                            finally
                            {
                                _concurrencyLimiter.Release();
                            }
                        });

                        var results = await Task.WhenAll(tasks);
                        
                        foreach (var result in results)
                        {
                            nodeExecutions[result.NodeId] = result;
                            if (result.Status == NodeExecutionStatus.Failed)
                            {
                                throw new NodeExecutionException($"节点执行失败: {result.NodeId} - {result.Error}");
                            }
                        }
                    }
                    else
                    {
                        // 串行执行
                        foreach (var nodeId in level.NodeIds)
                        {
                            var result = await ExecuteNodeAsync(nodeId, nodeMap[nodeId], context, cancellationToken);
                            nodeExecutions[nodeId] = result;
                            
                            if (result.Status == NodeExecutionStatus.Failed)
                            {
                                throw new NodeExecutionException($"节点执行失败: {nodeId} - {result.Error}");
                            }
                        }
                    }

                    _logger.LogDebug("执行层级完成: {WorkflowId} - Level {Level} - {NodeCount}个节点", 
                        workflow.Id, level.Level, level.NodeIds.Count);
                }

                return new WorkflowExecutionResult
                {
                    ExecutionId = context.ExecutionId,
                    WorkflowId = context.WorkflowId,
                    Status = ExecutionStatus.Completed,
                    StartedAt = context.StartedAt,
                    CompletedAt = DateTime.UtcNow,
                    NodeExecutions = nodeExecutions.Values.ToList(),
                    Variables = context.Variables,
                    NodeOutputs = context.NodeOutputs
                };
            }
            catch (Exception ex)
            {
                return new WorkflowExecutionResult
                {
                    ExecutionId = context.ExecutionId,
                    WorkflowId = context.WorkflowId,
                    Status = ExecutionStatus.Failed,
                    StartedAt = context.StartedAt,
                    CompletedAt = DateTime.UtcNow,
                    Error = ex.Message,
                    NodeExecutions = nodeExecutions.Values.ToList(),
                    Variables = context.Variables,
                    NodeOutputs = context.NodeOutputs
                };
            }
        }

        private async Task<NodeExecutionResult> ExecuteNodeAsync(
            Guid nodeId,
            WorkflowNode node,
            WorkflowExecutionContext context,
            CancellationToken cancellationToken)
        {
            var nodeContext = new NodeExecutionContext
            {
                NodeId = nodeId,
                NodeType = node.Type,
                Configuration = node.Configuration,
                WorkflowVariables = context.Variables,
                NodeOutputs = context.NodeOutputs,
                ExecutionId = context.ExecutionId
            };

            try
            {
                _logger.LogDebug("开始执行节点: {NodeId} - {NodeType}", nodeId, node.Type);

                var executor = await _executorFactory.CreateExecutorAsync(node.Type);
                var result = await executor.ExecuteAsync(nodeContext, cancellationToken);

                // 更新上下文
                if (result.OutputData != null)
                {
                    context.NodeOutputs[nodeId.ToString()] = result.OutputData;
                }

                if (result.Variables != null)
                {
                    foreach (var kvp in result.Variables)
                    {
                        context.Variables[kvp.Key] = kvp.Value;
                    }
                }

                _logger.LogDebug("节点执行完成: {NodeId} - {NodeType} - {Status}", 
                    nodeId, node.Type, result.Status);

                return new NodeExecutionResult
                {
                    NodeId = nodeId,
                    NodeType = node.Type,
                    Status = result.Status,
                    StartedAt = result.StartedAt,
                    CompletedAt = result.CompletedAt,
                    InputData = result.InputData,
                    OutputData = result.OutputData,
                    Error = result.Error,
                    Duration = result.Duration
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "节点执行异常: {NodeId} - {NodeType}", nodeId, node.Type);

                return new NodeExecutionResult
                {
                    NodeId = nodeId,
                    NodeType = node.Type,
                    Status = NodeExecutionStatus.Failed,
                    StartedAt = DateTime.UtcNow,
                    CompletedAt = DateTime.UtcNow,
                    Error = ex.Message,
                    Duration = 0
                };
            }
        }

        public async Task<bool> StopExecutionAsync(Guid executionId, CancellationToken cancellationToken = default)
        {
            if (_activeExecutions.TryGetValue(executionId, out var context))
            {
                context.CancellationTokenSource?.Cancel();
                context.Status = ExecutionStatus.Cancelled;
                
                _logger.LogInformation("工作流执行已停止: {ExecutionId}", executionId);
                return true;
            }

            return false;
        }

        public Task<WorkflowExecutionStatus> GetExecutionStatusAsync(Guid executionId, CancellationToken cancellationToken = default)
        {
            if (_activeExecutions.TryGetValue(executionId, out var context))
            {
                return Task.FromResult(new WorkflowExecutionStatus
                {
                    ExecutionId = executionId,
                    Status = context.Status,
                    StartedAt = context.StartedAt,
                    Progress = CalculateProgress(context),
                    CurrentNodes = GetCurrentExecutingNodes(context)
                });
            }

            return Task.FromResult<WorkflowExecutionStatus>(null);
        }

        private double CalculateProgress(WorkflowExecutionContext context)
        {
            if (context.ExecutionPlan == null || !context.ExecutionPlan.ExecutionLevels.Any())
                return 0.0;

            var totalNodes = context.ExecutionPlan.ExecutionLevels.Sum(l => l.NodeIds.Count);
            var completedNodes = context.NodeOutputs.Count;

            return totalNodes > 0 ? (double)completedNodes / totalNodes * 100 : 0.0;
        }

        private List<string> GetCurrentExecutingNodes(WorkflowExecutionContext context)
        {
            // 这里可以实现更复杂的逻辑来跟踪当前正在执行的节点
            return new List<string>();
        }

        public void Dispose()
        {
            _concurrencyLimiter?.Dispose();
            
            foreach (var context in _activeExecutions.Values)
            {
                context.CancellationTokenSource?.Cancel();
                context.CancellationTokenSource?.Dispose();
            }
            
            _activeExecutions.Clear();
        }
    }

    // 执行计划相关类
    public class ExecutionPlan
    {
        public List<ExecutionLevel> ExecutionLevels { get; set; } = new List<ExecutionLevel>();
    }

    public class ExecutionLevel
    {
        public int Level { get; set; }
        public List<Guid> NodeIds { get; set; } = new List<Guid>();
        public bool CanExecuteInParallel { get; set; }
    }

    // 执行上下文
    public class WorkflowExecutionContext
    {
        public Guid ExecutionId { get; set; }
        public Guid WorkflowId { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> Variables { get; set; } = new Dictionary<string, object>();
        public Dictionary<string, object> NodeOutputs { get; set; } = new Dictionary<string, object>();
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public ExecutionStatus Status { get; set; }
        public string Error { get; set; }
        public ExecutionPlan ExecutionPlan { get; set; }
        public CancellationTokenSource CancellationTokenSource { get; set; } = new CancellationTokenSource();
    }

    // 异常类
    public class NodeExecutionException : Exception
    {
        public NodeExecutionException(string message) : base(message) { }
        public NodeExecutionException(string message, Exception innerException) : base(message, innerException) { }
    }
}
```

## 📊 7. 系统集成与部署

### 7.1 系统集成架构
```mermaid
graph TB
    subgraph "部署架构"
        subgraph "前端层"
            A[React应用]
            B[Nginx反向代理]
        end
        
        subgraph "API层"
            C[.NET API服务]
            D[负载均衡器]
        end
        
        subgraph "数据层"
            E[SQLite/PostgreSQL]
            F[Redis缓存]
        end
        
        subgraph "监控层"
            G[日志收集]
            H[性能监控]
            I[健康检查]
        end
    end
    
    A --> B
    B --> D
    D --> C
    C --> E
    C --> F
    C --> G
    G --> H
    C --> I
```

### 7.2 Docker容器化部署

**Dockerfile配置**:
```dockerfile
# 后端Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.PluginHost/FlowCustomV1.PluginHost.csproj", "src/FlowCustomV1.PluginHost/"]
COPY ["src/FlowCustomV1.Data/FlowCustomV1.Data.csproj", "src/FlowCustomV1.Data/"]

RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"
COPY . .
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll"]
```

**Docker Compose配置**:
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 后端API服务
  api:
    build:
      context: .
      dockerfile: src/FlowCustomV1.Api/Dockerfile
    ports:
      - "5279:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Data Source=/data/flowcustom.db
      - Logging__LogLevel__Default=Information
    volumes:
      - ./data:/data
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:80"
    depends_on:
      - api
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    restart: unless-stopped

volumes:
  redis_data:
```

### 7.3 生产环境配置

**应用配置文件**:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=flowcustom.db"
  },
  "WorkflowEngine": {
    "MaxConcurrentExecutions": 10,
    "NodeExecutionTimeout": 300,
    "WorkflowExecutionTimeout": 3600,
    "EnablePerformanceMonitoring": true
  },
  "PluginSystem": {
    "PluginDirectory": "./plugins",
    "EnableHotReload": false,
    "MaxPluginLoadTime": 30
  },
  "Cache": {
    "DefaultExpiration": "00:30:00",
    "SlidingExpiration": "00:10:00",
    "MaxSize": 1000
  },
  "Security": {
    "EnableAuthentication": true,
    "JwtSecret": "your-secret-key-here",
    "JwtExpiration": "24:00:00"
  },
  "Monitoring": {
    "EnableHealthChecks": true,
    "EnableMetrics": true,
    "MetricsEndpoint": "/metrics"
  }
}
```

## 📊 8. 总结与展望

### 8.1 系统特点总结

**架构优势**:
1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **前后端分离**: 现代化的技术栈，支持独立部署和扩展
3. **插件化架构**: 灵活的节点扩展机制，支持多种加载方式
4. **高性能执行**: 异步并发执行引擎，支持大规模工作流处理
5. **完整的监控**: 全面的日志记录和性能监控体系

**技术亮点**:
1. **React 18 + TypeScript**: 现代化前端技术栈
2. **.NET 8**: 高性能后端框架
3. **ReactFlow**: 专业的流程图组件
4. **Entity Framework Core**: 强大的ORM框架
5. **Zustand**: 轻量级状态管理

### 8.2 后续发展规划

**Phase 2: 性能优化与功能完善**
- PostgreSQL集群支持
- 分布式执行引擎
- 高级监控告警
- 工作流模板系统
- 批量操作支持

**Phase 3: 企业级特性**
- 用户权限管理
- 多租户支持
- API网关集成
- 微服务架构
- 云原生部署

**长期愿景**:
- 构建完整的工作流生态系统
- 支持AI驱动的智能工作流
- 提供丰富的行业解决方案
- 建立开放的插件市场

本详细设计规范为FlowCustomV1项目提供了完整的技术实现指导，确保系统的可维护性、可扩展性和高性能。通过严格遵循本规范，可以构建出一个稳定、高效的工作流管理平台。
- 验证结果反馈
- 配置保存完成事件
- 参数变更通知

**活动图**:
```mermaid
flowchart TD
    A[打开配置面板] --> B[加载节点配置模式]
    B --> C[渲染配置表单]
    C --> D[等待用户输入]
    
    D --> E[用户修改参数]
    E --> F[实时验证输入]
    F --> G{验证通过?}
    
    G -->|否| H[显示错误提示]
    H --> D
    
    G -->|是| I[更新预览]
    I --> J{用户操作}
    
    J -->|继续编辑| D
    J -->|保存配置| K[执行最终验证]
    J -->|取消| L[恢复原始配置]
    
    K --> M{验证通过?}
    M -->|否| N[显示错误列表]
    N --> D
    
    M -->|是| O[保存配置数据]
    O --> P[更新节点状态]
    P --> Q[关闭配置面板]
    
    L --> Q
```

**对应代码**:
```typescript
// UnifiedNodeConfigPanel.tsx
export const UnifiedNodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  isOpen,
  onClose,
  onSave
}) => {
  const [config, setConfig] = useState<Record<string, any>>(node?.configuration || {});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  // 获取节点类型定义
  const nodeType = useNodeTypeDefinition(node?.type);

  // 处理配置变更
  const handleConfigChange = useCallback((field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setIsDirty(true);

    // 实时验证
    validateField(field, value);
  }, []);

  // 字段验证
  const validateField = useCallback(async (field: string, value: any) => {
    if (!nodeType?.configSchema?.properties?.[field]) return;

    const fieldSchema = nodeType.configSchema.properties[field];
    const validationResult = await validateConfigField(fieldSchema, value);

    setErrors(prev => ({
      ...prev,
      [field]: validationResult.error || ''
    }));
  }, [nodeType]);

  // 保存配置
  const handleSave = useCallback(async () => {
    if (!node || !nodeType) return;

    setIsValidating(true);
    
    try {
      // 执行完整验证
      const validationResult = await validateNodeConfiguration(nodeType.configSchema, config);
      
      if (!validationResult.isValid) {
        setErrors(validationResult.fieldErrors);
        return;
      }

      // 保存配置
      await onSave(node.id, config);
      setIsDirty(false);
      onClose();
    } catch (error) {
      console.error('Failed to save configuration:', error);
    } finally {
      setIsValidating(false);
    }
  }, [node, nodeType, config, onSave, onClose]);

  // 渲染配置字段
  const renderConfigField = useCallback((fieldName: string, fieldSchema: ConfigFieldSchema) => {
    const value = config[fieldName];
    const error = errors[fieldName];

    switch (fieldSchema.type) {
      case 'string':
        return (
          <StringConfigField
            key={fieldName}
            name={fieldName}
            schema={fieldSchema}
            value={value}
            error={error}
            onChange={(val) => handleConfigChange(fieldName, val)}
          />
        );

      case 'number':
        return (
          <NumberConfigField
            key={fieldName}
            name={fieldName}
            schema={fieldSchema}
            value={value}
            error={error}
            onChange={(val) => handleConfigChange(fieldName, val)}
          />
        );

      case 'boolean':
        return (
          <BooleanConfigField
            key={fieldName}
            name={fieldName}
            schema={fieldSchema}
            value={value}
            error={error}
            onChange={(val) => handleConfigChange(fieldName, val)}
          />
        );

      case 'select':
        return (
          <SelectConfigField
            key={fieldName}
            name={fieldName}
            schema={fieldSchema}
            value={value}
            error={error}
            onChange={(val) => handleConfigChange(fieldName, val)}
          />
        );

      case 'json':
        return (
          <JsonConfigField
            key={fieldName}
            name={fieldName}
            schema={fieldSchema}
            value={value}
            error={error}
            onChange={(val) => handleConfigChange(fieldName, val)}
          />
        );

      default:
        return (
          <div key={fieldName} className="text-red-500">
            Unsupported field type: {fieldSchema.type}
          </div>
        );
    }
  }, [config, errors, handleConfigChange]);

  if (!isOpen || !node || !nodeType) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <div 
              className="w-8 h-8 rounded flex items-center justify-center text-white"
              style={{ backgroundColor: nodeType.color }}
            >
              <Icon name={nodeType.icon} size={16} />
            </div>
            <div>
              <h2 className="text-lg font-semibold">{nodeType.displayName}</h2>
              <p className="text-sm text-gray-500">{nodeType.description}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={20} />
          </button>
        </div>

        {/* 配置表单 */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {nodeType.configSchema?.properties && 
              Object.entries(nodeType.configSchema.properties).map(([fieldName, fieldSchema]) =>
                renderConfigField(fieldName, fieldSchema as ConfigFieldSchema)
              )
            }
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="flex items-center space-x-2">
            {isDirty && (
              <span className="text-sm text-orange-600 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                有未保存的更改
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              disabled={isValidating || Object.values(errors).some(error => error)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isValidating && <Loader2 size={16} className="mr-2 animate-spin" />}
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 配置字段组件
const StringConfigField: React.FC<ConfigFieldProps> = ({ name, schema, value, error, onChange }) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {schema.title || name}
        {schema.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {schema.description && (
        <p className="text-sm text-gray-500">{schema.description}</p>
      )}
      
      {schema.format === 'textarea' ? (
        <textarea
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={schema.placeholder}
          rows={schema.rows || 3}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      ) : (
        <input
          type={schema.format === 'password' ? 'password' : 'text'}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={schema.placeholder}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      )}
      
      {error && (
        <p className="text-sm text-red-500 flex items-center">
          <AlertCircle size={16} className="mr-1" />
          {error}
        </p>
      )}
    </div>
  );
};
```

### 2.2 执行监控器软件

**软件描述**: 实时监控工作流执行状态的可视化界面，提供执行进度跟踪、日志查看、性能监控和控制操作功能。

#### 2.2.1 功能模块分解
```mermaid
graph TB
    subgraph "执行监控器软件"
        M1[状态显示模块]
        M2[日志查看模块]
        M3[进度跟踪模块]
        M4[控制操作模块]
        M5[实时通信模块]
        M6[性能监控模块]
    end
    
    M5 --> M1
    M5 --> M2
    M5 --> M3
    M5 --> M6
    M1 --> M4
    M3 --> M4
```

#### 2.2.2 状态显示模块

**模块描述**: 实时显示工作流和节点的执行状态，包括运行状态、完成进度、错误信息等，支持多种视图模式。

**功能职责**:
- 工作流整体状态展示
- 节点执行状态可视化
- 状态变更动画效果
- 多视图模式切换

**输入接口**:
- 工作流执行状态数据
- 节点执行状态更新
- 实时状态事件流
- 用户视图切换指令

**输出接口**:
- 状态变更通知
- 用户交互事件
- 视图状态更新
- 错误状态警告

**活动图**:
```mermaid
flowchart TD
    A[启动状态监控] --> B[建立实时连接]
    B --> C[订阅状态事件]
    C --> D[初始化状态显示]
    D --> E[等待状态更新]
    
    E --> F[接收状态消息]
    F --> G[解析状态数据]
    G --> H{状态类型}
    
    H -->|工作流状态| I[更新工作流显示]
    H -->|节点状态| J[更新节点显示]
    H -->|错误状态| K[显示错误警告]
    
    I --> L[应用状态动画]
    J --> L
    K --> L
    
    L --> M[更新UI界面]
    M --> N{执行是否完成?}
    
    N -->|否| E
    N -->|是| O[显示最终状态]
    O --> P[保持状态显示]
    P --> Q[等待用户操作]
    
    Q --> R{用户操作}
    R -->|查看详情| S[显示详细信息]
    R -->|重新执行| T[触发重新执行]
    R -->|关闭监控| U[关闭连接]
    
    S --> Q
    T --> A
    U --> V[结束监控]
```

**对应代码**:
```typescript
// ExecutionMonitor.tsx
export const ExecutionMonitor: React.FC<ExecutionMonitorProps> = ({
  executionId,
  viewMode = 'detailed'
}) => {
  const [execution, setExecution] = useState<WorkflowExecution | null>(null);
  const [nodeStatuses, setNodeStatuses] = useState<Map<string, NodeExecutionStatus>>(new Map());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 建立实时连接
  useEffect(() => {
    const eventSource = new EventSource(`/api/executions/${executionId}/events`);
    
    eventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
    };

    eventSource.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleStatusUpdate(message);
      } catch (err) {
        console.error('Failed to parse status message:', err);
      }
    };

    eventSource.onerror = () => {
      setIsConnected(false);
      setError('连接中断，正在重连...');
    };

    return () => {
      eventSource.close();
    };
  }, [executionId]);

  // 处理状态更新
  const handleStatusUpdate = useCallback((message: StatusUpdateMessage) => {
    switch (message.type) {
      case 'ExecutionStatusUpdate':
        setExecution(prev => ({
          ...prev,
          ...message.data,
          updatedAt: new Date()
        }));
        break;

      case 'NodeStatusUpdate':
        setNodeStatuses(prev => {
          const newMap = new Map(prev);
          newMap.set(message.data.nodeId, {
            ...message.data.status,
            updatedAt: new Date()
          });
          return newMap;
        });
        break;

      case 'ExecutionCompleted':
        setExecution(prev => ({
          ...prev,
          status: message.data.status,
          completedAt: new Date(message.data.completedAt),
          result: message.data.result
        }));
        break;

      case 'ExecutionError':
        setExecution(prev => ({
          ...prev,
          status: ExecutionStatus.Failed,
          error: message.data.error,
          completedAt: new Date()
        }));
        break;
    }
  }, []);

  // 渲染执行状态
  const renderExecutionStatus = () => {
    if (!execution) {
      return (
        <div className="flex items-center justify-center h-32">
          <Loader2 className="animate-spin mr-2" />
          <span>加载执行信息...</span>
        </div>
      );
    }

    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">执行状态</h2>
          <div className="flex items-center space-x-2">
            <StatusIndicator status={execution.status} />
            <span className="text-sm text-gray-500">
              {isConnected ? '实时连接' : '连接中断'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {execution.id.slice(0, 8)}
            </div>
            <div className="text-sm text-gray-500">执行ID</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatDuration(execution.startedAt, execution.completedAt)}
            </div>
            <div className="text-sm text-gray-500">执行时长</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {nodeStatuses.size}
            </div>
            <div className="text-sm text-gray-500">节点总数</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Array.from(nodeStatuses.values()).filter(s => s.status === 'completed').length}
            </div>
            <div className="text-sm text-gray-500">已完成</div>
          </div>
        </div>

        {execution.error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center mb-2">
              <AlertCircle className="text-red-500 mr-2" size={20} />
              <span className="font-medium text-red-800">执行错误</span>
            </div>
            <p className="text-red-700">{execution.error}</p>
          </div>
        )}
      </div>
    );
  };

  // 渲染节点状态网格
  const renderNodeStatusGrid = () => {
    if (nodeStatuses.size === 0) {
      return (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium mb-4">节点状态</h3>
          <div className="text-center text-gray-500">
            暂无节点状态信息
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium mb-4">节点状态</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from(nodeStatuses.entries()).map(([nodeId, status]) => (
            <NodeStatusCard
              key={nodeId}
              nodeId={nodeId}
              status={status}
              onClick={() => handleNodeClick(nodeId)}
            />
          ))}
        </div>
      </div>
    );
  };

  const handleNodeClick = (nodeId: string) => {
    // 显示节点详细信息
    console.log('Node clicked:', nodeId);
  };

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="text-yellow-500 mr-2" size={20} />
            <span className="text-yellow-800">{error}</span>
          </div>
        </div>
      )}

      {renderExecutionStatus()}
      {renderNodeStatusGrid()}
    </div>
  );
};

// 状态指示器组件
const StatusIndicator: React.FC<{ status: ExecutionStatus }> = ({ status }) => {
  const getStatusConfig = (status: ExecutionStatus) => {
    switch (status) {
      case ExecutionStatus.Running:
        return { color: 'text-blue-500', icon: Play, label: '运行中' };
      case ExecutionStatus.Completed:
        return { color: 'text-green-500', icon: CheckCircle, label: '已完成' };
      case ExecutionStatus.Failed:
        return { color: 'text-red-500', icon: XCircle, label: '失败' };
      case ExecutionStatus.Cancelled:
        return { color: 'text-gray-500', icon: StopCircle, label: '已取消' };
      default:
        return { color: 'text-gray-400', icon: Clock, label: '未知' };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <div className={`flex items-center ${config.color}`}>
      <Icon size={20} className="mr-1" />
      <span className="text-sm font-medium">{config.label}</span>
    </div>
  );
};

// 节点状态卡片组件
const NodeStatusCard: React.FC<NodeStatusCardProps> = ({ nodeId, status, onClick }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'border-blue-500 bg-blue-50';
      case 'completed': return 'border-green-500 bg-green-50';
      case 'failed': return 'border-red-500 bg-red-50';
      case 'pending': return 'border-gray-300 bg-gray-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  return (
    <div
      className={`border-2 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow ${getStatusColor(status.status)}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="font-medium text-sm">{nodeId.slice(0, 8)}</span>
        <StatusIndicator status={status.status as ExecutionStatus} />
      </div>
      
      {status.startedAt && (
        <div className="text-xs text-gray-500">
          开始: {new Date(status.startedAt).toLocaleTimeString()}
        </div>
      )}
      
      {status.completedAt && (
        <div className="text-xs text-gray-500">
          完成: {new Date(status.completedAt).toLocaleTimeString()}
        </div>
      )}
      
      {status.error && (
        <div className="text-xs text-red-600 mt-1 truncate">
          错误: {status.error}
        </div>
      )}
    </div>
  );
};
```

## 🔧 3. 后端子系统详细设计

### 3.1 API网关软件

**软件描述**: 基于ASP.NET Core的RESTful API服务，提供统一的HTTP接口，处理请求路由、验证、响应和错误处理。

#### 3.1.1 功能模块分解
```mermaid
graph TB
    subgraph "API网关软件"
        M1[路由管理模块]
        M2[请求验证模块]
        M3[响应处理模块]
        M4[错误处理模块]
        M5[日志记录模块]
        M6[认证授权模块]
    end
    
    M1 --> M2
    M2 --> M6
    M6 --> M3
    M2 --> M4
    M3 --> M5
    M4 --> M5
```

#### 3.1.2 工作流控制器模块

**模块描述**: 处理工作流相关的HTTP请求，包括工作流的CRUD操作、执行控制和状态查询。

**功能职责**:
- 工作流定义管理API
- 工作流执行控制API
- 工作流状态查询API
- 请求参数验证和转换

**输入接口**:
- HTTP请求 (GET, POST, PUT, DELETE)
- 请求参数和Body数据
- 认证Token和用户信息
- 查询过滤条件

**输出接口**:
- HTTP响应 (JSON格式)
- 标准状态码
- 统一错误格式
- 分页和排序数据

**类图**:
```mermaid
classDiagram
    class WorkflowController {
        -IWorkflowEngine _workflowEngine
        -ILogger _logger
        -IMapper _mapper
        +GetWorkflowsAsync(filter) Task~ActionResult~PagedResult~WorkflowDto~~~
        +GetWorkflowAsync(id) Task~ActionResult~WorkflowDto~~
        +CreateWorkflowAsync(request) Task~ActionResult~WorkflowDto~~
        +UpdateWorkflowAsync(id, request) Task~ActionResult~WorkflowDto~~
        +DeleteWorkflowAsync(id) Task~ActionResult~
        +ExecuteWorkflowAsync(id, parameters) Task~ActionResult~ExecutionDto~~
        +GetExecutionsAsync(workflowId) Task~ActionResult~List~ExecutionDto~~~
        +CancelExecutionAsync(executionId) Task~ActionResult~
    }
    
    class IWorkflowEngine {
        <<interface>>
        +GetAllWorkflowsAsync(filter) Task~PagedResult~WorkflowDefinition~~
        +GetWorkflowByIdAsync(id) Task~WorkflowDefinition~
        +CreateWorkflowAsync(definition) Task~WorkflowDefinition~
        +UpdateWorkflowAsync(definition) Task~WorkflowDefinition~
        +DeleteWorkflowAsync(id) Task
        +ExecuteWorkflowAsync(id, parameters) Task~WorkflowExecution~
        +CancelExecutionAsync(executionId) Task
    }
    
    class WorkflowDto {
        +Id: Guid
        +Name: string
        +Description: string
        +Version: int
        +Status: WorkflowStatus
        +CreatedAt: DateTime
        +UpdatedAt: DateTime
        +CreatedBy: string
        +Nodes: List~NodeDto~
        +Connections: List~ConnectionDto~
        +Variables: Dictionary~string,object~
    }
    
    class CreateWorkflowRequest {
        +Name: string
        +Description: string
        +Nodes: List~NodeDto~
        +Connections: List~ConnectionDto~
        +Variables: Dictionary~string,object~
        +Validate() ValidationResult
    }
    
    WorkflowController --> IWorkflowEngine
    WorkflowController --> WorkflowDto
    WorkflowController --> CreateWorkflowRequest
```

**对应代码**:
```csharp
// WorkflowController.cs
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WorkflowController : ControllerBase
{
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ILogger<WorkflowController> _logger;
    private readonly IMapper _mapper;

    public WorkflowController(
        IWorkflowEngine workflowEngine, 
        ILogger<WorkflowController> logger,
        IMapper mapper)
    {
        _workflowEngine = workflowEngine;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// 获取工作流列表
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<WorkflowDto>>>> GetWorkflowsAsync(
        [FromQuery] WorkflowFilterRequest filter)
    {
        try
        {
            _logger.LogInformation("Getting workflows with filter: {@Filter}", filter);

            var workflows = await _workflowEngine.GetAllWorkflowsAsync(filter.ToFilter());
            var workflowDtos = _mapper.Map<PagedResult<WorkflowDto>>(workflows);

            return Ok(ApiResponse.Success(workflowDtos));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflows");
            return StatusCode(500, ApiResponse.Error("获取工作流列表失败"));
        }
    }

    /// <summary>
    /// 根据ID获取工作流
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> GetWorkflowAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Getting workflow {WorkflowId}", id);

            var workflow = await _workflowEngine.GetWorkflowByIdAsync(id);
            if (workflow == null)
            {
                return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
            }

            var workflowDto = _mapper.Map<WorkflowDto>(workflow);
            return Ok(ApiResponse.Success(workflowDto));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("获取工作流失败"));
        }
    }

    /// <summary>
    /// 创建新工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> CreateWorkflowAsync(
        [FromBody] CreateWorkflowRequest request)
    {
        try
        {
            // 验证请求
            var validationResult = request.Validate();
            if (!validationResult.IsValid)
            {
                return BadRequest(ApiResponse.Error("请求参数无效", validationResult.Errors));
            }

            _logger.LogInformation("Creating workflow: {WorkflowName}", request.Name);

            // 转换为领域模型
            var workflowDefinition = _mapper.Map<WorkflowDefinition>(request);
            workflowDefinition.CreatedBy = User.Identity?.Name ?? "System";

            // 创建工作流
            var createdWorkflow = await _workflowEngine.CreateWorkflowAsync(workflowDefinition);
            var workflowDto = _mapper.Map<WorkflowDto>(createdWorkflow);

            return CreatedAtAction(nameof(GetWorkflowAsync), new { id = workflowDto.Id }, workflowDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow");
            return StatusCode(500, ApiResponse.Error("创建工作流失败"));
        }
    }

    /// <summary>
    /// 更新现有工作流
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> UpdateWorkflowAsync(
        Guid id, [FromBody] UpdateWorkflowRequest request)
    {
        try
        {
            // 验证请求
            var validationResult = request.Validate();
            if (!validationResult.IsValid)
            {
                return BadRequest(ApiResponse.Error("请求参数无效", validationResult.Errors));
            }

            _logger.LogInformation("Updating workflow {WorkflowId}", id);

            // 转换为领域模型
            var workflowDefinition = _mapper.Map<WorkflowDefinition>(request);
            workflowDefinition.Id = id;
            workflowDefinition.UpdatedAt = DateTime.UtcNow;
            workflowDefinition.UpdatedBy = User.Identity?.Name ?? "System";

            // 更新工作流
            var updatedWorkflow = await _workflowEngine.UpdateWorkflowAsync(workflowDefinition);
            if (updatedWorkflow == null)
            {
                return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
            }

            var workflowDto = _mapper.Map<WorkflowDto>(updatedWorkflow);
            return Ok(ApiResponse.Success(workflowDto));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("更新工作流失败"));
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ApiResponse>> DeleteWorkflowAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Deleting workflow {WorkflowId}", id);

            var result = await _workflowEngine.DeleteWorkflowAsync(id);
            if (!result)
            {
                return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
            }

            return Ok(ApiResponse.Success("工作流已成功删除"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("删除工作流失败"));
        }
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    [HttpPost("{id:guid}/execute")]
    public async Task<ActionResult<ApiResponse<ExecutionDto>>> ExecuteWorkflowAsync(
        Guid id, [FromBody] ExecuteWorkflowRequest request)
    {
        try
        {
            _logger.LogInformation("Executing workflow {WorkflowId}", id);

            var execution = await _workflowEngine.ExecuteWorkflowAsync(id, request.Parameters);
            var executionDto = _mapper.Map<ExecutionDto>(execution);
            return Ok(ApiResponse.Success(executionDto));
        }
        catch (NotFoundException ex)
        {
            return NotFound(ApiResponse.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow {WorkflowId}", id);
## 📖 1. 项目概述

### 1.1 项目背景
FlowCustomV1工作流管理系统是一个基于.NET 8的分布式工作流引擎，旨在提供高性能、高可靠性和可扩展的工作流处理能力。系统支持多种节点类型和复杂的工作流结构，适用于各种业务场景。

### 1.2 项目目标
- 实现一个高性能、高可靠性的分布式工作流引擎
- 支持多种节点类型和复杂的工作流结构
- 提供灵活的工作流定义和执行方式
- 支持并发执行和事务管理
- 提供丰富的API接口和用户界面
- 支持多种数据存储和缓存方案
- 提供可扩展的插件和事件处理机制
- 确保系统的高可用性和容错性

### 1.3 项目范围
- 工作流定义和管理
- 工作流执行和调度
- 节点类型和插件扩展
- API接口和用户界面
- 数据存储和缓存
- 插件和事件处理
- 部署和运维
- 性能和安全设计

## 📖 2. 系统架构设计

### 2.1 系统层次结构
![]

### 2.2 功能模块分解
![]

### 2.3 接口定义
![]

### 2.4 处理过程
![]

### 2.5 代码实现
![]

## 📊 3. 详细设计

### 3.1 API接口设计

#### 3.1.1 API响应统一格式
```csharp
// ApiResponse.cs
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public T Data { get; set; }
    public string Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static ApiResponse<T> Success(T data, string message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        };
    }

    public static ApiResponse<T> Error(string message, List<string> errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}

public class ApiResponse : ApiResponse<object>
{
    public static ApiResponse Success(string message = null)
    {
        return new ApiResponse
        {
            Success = true,
            Message = message
        };
    }

    public new static ApiResponse Error(string message, List<string> errors = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            Errors = errors ?? new List<string>()
        };
    }
}
```

#### 3.1.2 API控制器设计
```csharp
// WorkflowsController.cs
[ApiController]
[Route("api/[controller]")]
public class WorkflowsController : ControllerBase
{
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IMapper _mapper;
    private readonly ILogger<WorkflowsController> _logger;

    public WorkflowsController(
        IWorkflowEngine workflowEngine,
        IMapper mapper,
        ILogger<WorkflowsController> logger)
    {
        _workflowEngine = workflowEngine;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有工作流
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<WorkflowDto>>>> GetAllWorkflowsAsync(
        [FromQuery] WorkflowFilterRequest filter)
    {
        try
        {
            var filterModel = _mapper.Map<WorkflowFilter>(filter);
            var workflows = await _workflowEngine.GetAllWorkflowsAsync(filterModel);
            var workflowDtos = _mapper.Map<PagedResult<WorkflowDto>>(workflows);
            return Ok(ApiResponse.Success(workflowDtos));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflows");
            return StatusCode(500, ApiResponse.Error("获取工作流列表失败"));
        }
    }

    /// <summary>
    /// 获取单个工作流
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> GetWorkflowAsync(Guid id)
    {
        try
        {
            var workflow = await _workflowEngine.GetWorkflowByIdAsync(id);
            if (workflow == null)
            {
                return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
            }

            var workflowDto = _mapper.Map<WorkflowDto>(workflow);
            return Ok(ApiResponse.Success(workflowDto));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("获取工作流失败"));
        }
    }

    /// <summary>
    /// 创建新工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> CreateWorkflowAsync(
        [FromBody] CreateWorkflowRequest request)
    {
        try
        {
            // 验证请求
            var validationResult = request.Validate();
            if (!validationResult.IsValid)
            {
                return BadRequest(ApiResponse.Error("请求参数无效", validationResult.Errors));
            }

            _logger.LogInformation("Creating new workflow");

            var workflowDefinition = _mapper.Map<WorkflowDefinition>(request);
            workflowDefinition.CreatedBy = User.Identity?.Name ?? "System";

            var createdWorkflow = await _workflowEngine.CreateWorkflowAsync(workflowDefinition);
            var workflowDto = _mapper.Map<WorkflowDto>(createdWorkflow);

            return CreatedAtAction(
                nameof(GetWorkflowAsync), 
                new { id = createdWorkflow.Id }, 
                ApiResponse.Success(workflowDto));
        }
        catch (ValidationException ex)
        {
            _logger.LogWarning(ex, "Validation failed for workflow creation");
            return BadRequest(ApiResponse.Error("工作流验证失败", ex.Errors));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating workflow");
            return StatusCode(500, ApiResponse.Error("创建工作流失败"));
        }
    }

    /// <summary>
    /// 更新工作流
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<ApiResponse<WorkflowDto>>> UpdateWorkflowAsync(
        Guid id, 
        [FromBody] UpdateWorkflowRequest request)
    {
        try
        {
            // 验证请求
            var validationResult = request.Validate();
            if (!validationResult.IsValid)
            {
                return BadRequest(ApiResponse.Error("请求参数无效", validationResult.Errors));
            }

            _logger.LogInformation("Updating workflow {WorkflowId}", id);

            // 检查工作流是否存在
            var existingWorkflow = await _workflowEngine.GetWorkflowByIdAsync(id);
            if (existingWorkflow == null)
            {
                return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
            }

            // 更新工作流
            var workflowDefinition = _mapper.Map<WorkflowDefinition>(request);
            workflowDefinition.Id = id;
            workflowDefinition.UpdatedBy = User.Identity?.Name ?? "System";

            var updatedWorkflow = await _workflowEngine.UpdateWorkflowAsync(workflowDefinition);
            var workflowDto = _mapper.Map<WorkflowDto>(updatedWorkflow);

            return Ok(ApiResponse.Success(workflowDto));
        }
        catch (ValidationException ex)
        {
            _logger.LogWarning(ex, "Validation failed for workflow update");
            return BadRequest(ApiResponse.Error("工作流验证失败", ex.Errors));
        }
        catch (ConcurrencyException ex)
        {
            _logger.LogWarning(ex, "Concurrency conflict updating workflow {WorkflowId}", id);
            return Conflict(ApiResponse.Error("工作流已被其他用户修改，请刷新后重试"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("更新工作流失败"));
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ApiResponse>> DeleteWorkflowAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Deleting workflow {WorkflowId}", id);

            // 检查是否有正在执行的实例
            var runningExecutions = await _workflowEngine.GetRunningExecutionsAsync(id);
            if (runningExecutions.Any())
            {
                return BadRequest(ApiResponse.Error("无法删除正在执行的工作流"));
            }

            await _workflowEngine.DeleteWorkflowAsync(id);
            return Ok(ApiResponse.Success("工作流删除成功"));
        }
        catch (NotFoundException)
        {
            return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("删除工作流失败"));
        }
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    [HttpPost("{id:guid}/execute")]
    public async Task<ActionResult<ApiResponse<ExecutionDto>>> ExecuteWorkflowAsync(
        Guid id, 
        [FromBody] ExecuteWorkflowRequest request)
    {
        try
        {
            _logger.LogInformation("Executing workflow {WorkflowId} with parameters: {@Parameters}", 
                id, request.Parameters);

            var execution = await _workflowEngine.ExecuteWorkflowAsync(id, request.Parameters);
            var executionDto = _mapper.Map<ExecutionDto>(execution);

            return Ok(ApiResponse.Success(executionDto));
        }
        catch (NotFoundException)
        {
            return NotFound(ApiResponse.Error($"工作流 {id} 不存在"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot execute workflow {WorkflowId}", id);
            return BadRequest(ApiResponse.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("执行工作流失败"));
        }
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    [HttpGet("{id:guid}/executions")]
    public async Task<ActionResult<ApiResponse<PagedResult<ExecutionDto>>>> GetExecutionsAsync(
        Guid id,
        [FromQuery] ExecutionFilterRequest filter)
    {
        try
        {
            _logger.LogInformation("Getting executions for workflow {WorkflowId}", id);

            var executions = await _workflowEngine.GetExecutionsAsync(id, filter.ToFilter());
            var executionDtos = _mapper.Map<PagedResult<ExecutionDto>>(executions);

            return Ok(ApiResponse.Success(executionDtos));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting executions for workflow {WorkflowId}", id);
            return StatusCode(500, ApiResponse.Error("获取执行历史失败"));
        }
    }

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    [HttpPost("executions/{executionId:guid}/cancel")]
    public async Task<ActionResult<ApiResponse>> CancelExecutionAsync(Guid executionId)
    {
        try
        {
            _logger.LogInformation("Cancelling execution {ExecutionId}", executionId);

            await _workflowEngine.CancelExecutionAsync(executionId);
            return Ok(ApiResponse.Success("执行已取消"));
        }
        catch (NotFoundException)
        {
            return NotFound(ApiResponse.Error($"执行 {executionId} 不存在"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse.Error(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling execution {ExecutionId}", executionId);
            return StatusCode(500, ApiResponse.Error("取消执行失败"));
        }
    }
}
```

### 3.2 业务逻辑软件

**软件描述**: 工作流引擎的核心业务逻辑实现，负责工作流的解析、调度、执行和状态管理。

#### 3.2.1 功能模块分解
```mermaid
graph TB
    subgraph "业务逻辑软件"
        M1[工作流引擎模块]
        M2[节点执行器模块]
        M3[调度管理模块]
        M4[状态管理模块]
        M5[插件管理模块]
        M6[事件处理模块]
    end
    
    M1 --> M2
    M1 --> M3
    M1 --> M4
    M2 --> M5
    M3 --> M6
    M4 --> M6
```

#### 3.2.2 工作流引擎模块

**模块描述**: 工作流引擎的核心模块，负责工作流的整体执行控制、节点调度和状态协调。

**功能职责**:
- 工作流执行生命周期管理
- 节点依赖关系解析
- 执行上下文管理
- 异常处理和恢复

**输入接口**:
- 工作流定义数据
- 执行参数和配置
- 节点执行结果
- 外部控制指令

**输出接口**:
- 工作流执行结果
- 节点执行指令
- 状态变更事件
- 执行日志信息

**状态图**:
```mermaid
stateDiagram-v2
    [*] --> Initializing: 开始执行
    Initializing --> Ready: 初始化完成
    Ready --> Running: 开始执行节点
    Running --> Running: 节点执行中
    Running --> Paused: 暂停执行
    Running --> Completed: 所有节点完成
    Running --> Failed: 执行失败
    Running --> Cancelled: 用户取消
    
    Paused --> Running: 恢复执行
    Paused --> Cancelled: 取消执行
    
    Completed --> [*]
    Failed --> [*]
    Cancelled --> [*]
    
    note right of Running
        在此状态下：
        - 执行节点
        - 处理依赖
        - 更新状态
        - 记录日志
    end note
```

**对应代码**:
```csharp
// WorkflowEngine.cs
public class WorkflowEngine : IWorkflowEngine
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IExecutionRepository _executionRepository;
    private readonly INodeExecutorFactory _nodeExecutorFactory;
    private readonly IEventBus _eventBus;
    private readonly IDistributedCache _cache;

    public WorkflowEngine(
        IServiceProvider serviceProvider,
        ILogger<WorkflowEngine> logger,
        IWorkflowRepository workflowRepository,
        IExecutionRepository executionRepository,
        INodeExecutorFactory nodeExecutorFactory,
        IEventBus eventBus,
        IDistributedCache cache)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _workflowRepository = workflowRepository;
        _executionRepository = executionRepository;
        _nodeExecutorFactory = nodeExecutorFactory;
        _eventBus = eventBus;
        _cache = cache;
    }

    public async Task<WorkflowExecution> ExecuteWorkflowAsync(
        Guid workflowId, 
        Dictionary<string, object> parameters = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始执行工作流: {WorkflowId}", workflowId);

        try
        {
            // 获取工作流定义
            var workflow = await _workflowRepository.GetByIdAsync(workflowId);
            if (workflow == null)
            {
                throw new NotFoundException($"工作流 {workflowId} 不存在");
            }

            // 验证工作流状态
            if (workflow.Status != WorkflowStatus.Active)
            {
                throw new InvalidOperationException($"工作流状态为 {workflow.Status}，无法执行");
            }

            // 创建执行上下文
            var execution = new WorkflowExecution
            {
                Id = Guid.NewGuid(),
                WorkflowId = workflowId,
                WorkflowVersion = workflow.Version,
                Status = ExecutionStatus.Initializing,
                StartedAt = DateTime.UtcNow,
                Parameters = parameters ?? new Dictionary<string, object>(),
                NodeOutputs = new Dictionary<string, object>(),
                Variables = new Dictionary<string, object>(workflow.Variables ?? new Dictionary<string, object>())
            };

            // 保存执行记录
            await _executionRepository.CreateAsync(execution);

            // 发布执行开始事件
            await _eventBus.PublishAsync(new WorkflowExecutionStartedEvent
            {
                ExecutionId = execution.Id,
                WorkflowId = workflowId,
                StartedAt = execution.StartedAt,
                Parameters = parameters
            });

            // 异步执行工作流
            _ = Task.Run(async () => await ExecuteWorkflowInternalAsync(workflow, execution, cancellationToken));

            return execution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动工作流执行失败: {WorkflowId}", workflowId);
            throw;
        }
    }

    private async Task ExecuteWorkflowInternalAsync(
        WorkflowDefinition workflow, 
        WorkflowExecution execution,
        CancellationToken cancellationToken)
    {
        var context = new WorkflowExecutionContext
        {
            Workflow = workflow,
            Execution = execution,
            ServiceProvider = _serviceProvider,
            CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken),
            NodeOutputs = new ConcurrentDictionary<string, object>(),
            Variables = new ConcurrentDictionary<string, object>(execution.Variables)
        };

        try
        {
            // 更新状态为运行中
            await UpdateExecutionStatusAsync(execution.Id, ExecutionStatus.Running);

            // 构建执行图
            var executionGraph = BuildExecutionGraph(workflow);
            _logger.LogDebug("构建执行图完成，节点数: {NodeCount}", executionGraph.Nodes.Count);

            // 找到起始节点
            var startNodes = executionGraph.GetStartNodes();
            if (!startNodes.Any())
            {
                throw new InvalidOperationException("工作流中没有找到起始节点");
            }

            _logger.LogInformation("找到 {Count} 个起始节点", startNodes.Count());

            // 并行执行起始节点
            var startTasks = startNodes.Select(node => ExecuteNodeAsync(node, context, executionGraph));
            await Task.WhenAll(startTasks);

            // 检查执行结果
            if (context.CancellationTokenSource.Token.IsCancellationRequested)
            {
                await UpdateExecutionStatusAsync(execution.Id, ExecutionStatus.Cancelled);
                _logger.LogInformation("工作流执行已取消: {ExecutionId}", execution.Id);
            }
            else if (context.HasErrors)
            {
                await UpdateExecutionStatusAsync(execution.Id, ExecutionStatus.Failed, context.LastError);
                _logger.LogError("工作流执行失败: {ExecutionId}, 错误: {Error}", execution.Id, context.LastError);
            }
            else
            {
                await UpdateExecutionStatusAsync(execution.Id, ExecutionStatus.Completed);
                _logger.LogInformation("工作流执行完成: {ExecutionId}", execution.Id);
            }

            // 发布执行完成事件
            await _eventBus.PublishAsync(new WorkflowExecutionCompletedEvent
            {
                ExecutionId = execution.Id,
                WorkflowId = workflow.Id,
                Status = execution.Status,
                CompletedAt = DateTime.UtcNow,
                Duration = DateTime.UtcNow - execution.StartedAt,
                NodeOutputs = context.NodeOutputs.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作流执行异常: {ExecutionId}", execution.Id);
            await UpdateExecutionStatusAsync(execution.Id, ExecutionStatus.Failed, ex.Message);

            await _eventBus.PublishAsync(new WorkflowExecutionFailedEvent
            {
                ExecutionId = execution.Id,
                WorkflowId = workflow.Id,
                Error = ex.Message,
                FailedAt = DateTime.UtcNow
            });
        }
        finally
        {
            context.CancellationTokenSource?.Dispose();
        }
    }

    private async Task ExecuteNodeAsync(
        WorkflowNode node, 
        WorkflowExecutionContext context, 
        ExecutionGraph executionGraph)
    {
        if (context.CancellationTokenSource.Token.IsCancellationRequested)
        {
            return;
        }

        try
        {
            _logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", node.Id, node.Type);

            // 发布节点开始执行事件
            await _eventBus.PublishAsync(new NodeExecutionStartedEvent
            {
                ExecutionId = context.Execution.Id,
                NodeId = node.Id,
                NodeType = node.Type,
                StartedAt = DateTime.UtcNow
            });

            // 准备节点输入数据
            var inputData = await PrepareNodeInputDataAsync(node, context);

            // 创建节点执行上下文
            var nodeContext = new NodeExecutionContext
            {
                Node = node,
                WorkflowExecutionId = context.Execution.Id,
                InputData = inputData,
                Variables = context.Variables,
                ServiceProvider = context.ServiceProvider,
                Logger = _logger
            };

            // 获取节点执行器
            var executor = _nodeExecutorFactory.CreateExecutor(node.Type);
            if (executor == null)
            {
                throw new InvalidOperationException($"未找到节点类型 {node.Type} 的执行器");
            }

            // 执行节点
            var result = await executor.ExecuteAsync(nodeContext, context.CancellationTokenSource.Token);

            // 处理执行结果
            if (result.IsSuccess)
            {
                // 保存节点输出
                context.NodeOutputs.TryAdd(node.Id, result.OutputData);
                _logger.LogDebug("节点执行成功: {NodeId}", node.Id);

                // 发布节点执行成功事件
                await _eventBus.PublishAsync(new NodeExecutionCompletedEvent
                {
                    ExecutionId = context.Execution.Id,
                    NodeId = node.Id,
                    Status = NodeExecutionStatus.Completed,
                    OutputData = result.OutputData,
                    CompletedAt = DateTime.UtcNow
                });

                // 执行后续节点
                await ExecuteNextNodesAsync(node, context, executionGraph);
            }
            else
            {
                _logger.LogError("节点执行失败: {NodeId}, 错误: {Error}", node.Id, result.ErrorMessage);
                context.HasErrors = true;
                context.LastError = result.ErrorMessage;

                // 发布节点执行失败事件
                await _eventBus.PublishAsync(new NodeExecutionFailedEvent
                {
                    ExecutionId = context.Execution.Id,
                    NodeId = node.Id,
                    Error = result.ErrorMessage,
                    FailedAt = DateTime.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "节点执行异常: {NodeId}", node.Id);
            context.HasErrors = true;
            context.LastError = ex.Message;

            await _eventBus.PublishAsync(new NodeExecutionFailedEvent
            {
                ExecutionId = context.Execution.Id,
                NodeId = node.Id,
                Error = ex.Message,
                FailedAt = DateTime.UtcNow
            });
        }
    }

    private async Task<Dictionary<string, object>> PrepareNodeInputDataAsync(
        WorkflowNode node, 
        WorkflowExecutionContext context)
    {
        var inputData = new Dictionary<string, object>();

        // 添加工作流参数
        foreach (var param in context.Execution.Parameters)
        {
            inputData[param.Key] = param.Value;
        }

        // 添加工作流变量
        foreach (var variable in context.Variables)
        {
            inputData[variable.Key] = variable.Value;
        }

        // 处理输入连接
        var inputConnections = context.Workflow.Connections
            .Where(c => c.TargetNodeId == node.Id)
            .ToList();

        foreach (var connection in inputConnections)
        {
            if (context.NodeOutputs.TryGetValue(connection.SourceNodeId, out var sourceOutput))
            {
                // 根据连接的端点映射数据
                var sourceEndpoint = connection.SourceEndpointId;
                var targetEndpoint = connection.TargetEndpointId;

                if (sourceOutput is Dictionary<string, object> outputDict && 
                    outputDict.TryGetValue(sourceEndpoint, out var endpointValue))
                {
                    inputData[targetEndpoint] = endpointValue;
                }
                else
                {
                    // 如果没有指定端点，传递整个输出
                    inputData[targetEndpoint] = sourceOutput;
                }
            }
        }

        return inputData;
    }

    private async Task ExecuteNextNodesAsync(
        WorkflowNode currentNode, 
        WorkflowExecutionContext context, 
        ExecutionGraph executionGraph)
    {
        var nextNodes = executionGraph.GetNextNodes(currentNode.Id);
        var readyNodes = new List<WorkflowNode>();

        foreach (var nextNode in nextNodes)
        {
            // 检查节点的所有前置依赖是否都已完成
            var dependencies = executionGraph.GetPreviousNodes(nextNode.Id);
            var allDependenciesCompleted = dependencies.All(dep => 
                context.NodeOutputs.ContainsKey(dep.Id));

            if (allDependenciesCompleted)
            {
                readyNodes.Add(nextNode);
            }
        }

        // 并行执行准备好的节点
        if (readyNodes.Any())
        {
            var tasks = readyNodes.Select(node => ExecuteNodeAsync(node, context, executionGraph));
            await Task.WhenAll(tasks);
        }
    }

    private ExecutionGraph BuildExecutionGraph(WorkflowDefinition workflow)
    {
        var graph = new ExecutionGraph();

        // 添加所有节点
        foreach (var node in workflow.Nodes)
        {
            graph.AddNode(node);
        }

        // 添加连接关系
        foreach (var connection in workflow.Connections)
        {
            graph.AddEdge(connection.SourceNodeId, connection.TargetNodeId);
        }

        return graph;
    }

    private async Task UpdateExecutionStatusAsync(Guid executionId, ExecutionStatus status, string error = null)
    {
        var execution = await _executionRepository.GetByIdAsync(executionId);
        if (execution != null)
        {
            execution.Status = status;
            execution.Error = error;
            
            if (status == ExecutionStatus.Completed || status == ExecutionStatus.Failed || status == ExecutionStatus.Cancelled)
            {
                execution.CompletedAt = DateTime.UtcNow;
            }

            await _executionRepository.UpdateAsync(execution);

            // 发布状态更新事件
            await _eventBus.PublishAsync(new ExecutionStatusUpdatedEvent
            {
                ExecutionId = executionId,
                Status = status,
                Error = error,
                UpdatedAt = DateTime.UtcNow
            });
        }
    }

    public async Task CancelExecutionAsync(Guid executionId)
    {
        _logger.LogInformation("取消工作流执行: {ExecutionId}", executionId);

        var execution = await _executionRepository.GetByIdAsync(executionId);
        if (execution == null)
        {
            throw new NotFoundException($"执行 {executionId} 不存在");
        }

        if (execution.Status != ExecutionStatus.Running && execution.Status != ExecutionStatus.Initializing)
        {
            throw new InvalidOperationException($"无法取消状态为 {execution.Status} 的执行");
        }

        // 更新状态
        await UpdateExecutionStatusAsync(executionId, ExecutionStatus.Cancelled);

        // 发布取消事件
        await _eventBus.PublishAsync(new WorkflowExecutionCancelledEvent
        {
            ExecutionId = executionId,
            CancelledAt = DateTime.UtcNow
        });
    }
}

// 执行图类
public class ExecutionGraph
{
    private readonly Dictionary<string, WorkflowNode> _nodes = new();
    private readonly Dictionary<string, List<string>> _adjacencyList = new();
    private readonly Dictionary<string, List<string>> _reverseAdjacencyList = new();

    public IReadOnlyCollection<WorkflowNode> Nodes => _nodes.Values;

    public void AddNode(WorkflowNode node)
    {
        _nodes[node.Id] = node;
        _adjacencyList[node.Id] = new List<string>();
        _reverseAdjacencyList[node.Id] = new List<string>();
    }

    public void AddEdge(string fromNodeId, string toNodeId)
    {
        _adjacencyList[fromNodeId].Add(toNodeId);
        _reverseAdjacencyList[toNodeId].Add(fromNodeId);
    }

    public IEnumerable<WorkflowNode> GetStartNodes()
    {
        return _nodes.Values.Where(node => 
            node.Type == "start" || !_reverseAdjacencyList[node.Id].Any());
    }

    public IEnumerable<WorkflowNode> GetNextNodes(string nodeId)
    {
        return _adjacencyList[nodeId].Select(id => _nodes[id]);
    }

    public IEnumerable<WorkflowNode> GetPreviousNodes(string nodeId)
    {
        return _reverseAdjacencyList[nodeId].Select(id => _nodes[id]);
    }
}

// 工作流执行上下文
public class WorkflowExecutionContext
{
    public WorkflowDefinition Workflow { get; set; }
    public WorkflowExecution Execution { get; set; }
    public IServiceProvider ServiceProvider { get; set; }
    public CancellationTokenSource CancellationTokenSource { get; set; }
    public ConcurrentDictionary<string, object> NodeOutputs { get; set; }
    public ConcurrentDictionary<string, object> Variables { get; set; }
    public bool HasErrors { get; set; }
    public string LastError { get; set; }
}
```

#### 3.2.3 节点执行器模块

**模块描述**: 负责具体节点类型的执行逻辑，提供可扩展的节点执行器框架和内置节点实现。

**功能职责**:
- 节点执行器注册和管理
- 节点执行生命周期控制
- 输入输出数据处理
- 错误处理和重试机制

**输入接口**:
- 节点执行上下文
- 节点配置参数
- 输入数据
- 取消令牌

**输出接口**:
- 节点执行结果
- 输出数据
- 执行状态
- 错误信息

**类图**:
```mermaid
classDiagram
    class INodeExecutor {
        <<interface>>
        +ExecuteAsync(context, cancellationToken) Task~NodeExecutionResult~
        +ValidateConfiguration(config) ValidationResult
        +GetSupportedNodeType() string
    }
    
    class BaseNodeExecutor {
        <<abstract>>
        #Logger: ILogger
        +ExecuteAsync(context, cancellationToken) Task~NodeExecutionResult~
        #ExecuteCoreAsync(context, cancellationToken) Task~NodeExecutionResult~*
        #GetConfigurationValue~T~(config, key, defaultValue) T
        #GetInputValue~T~(inputData, key, defaultValue) T
        #ValidateRequiredInputs(context) ValidationResult
    }
    
    class StartNodeExecutor {
        +GetSupportedNodeType() string
        #ExecuteCoreAsync(context, cancellationToken) Task~NodeExecutionResult~
    }
    
    class EndNodeExecutor {
        +GetSupportedNodeType() string
        #ExecuteCoreAsync(context, cancellationToken) Task~NodeExecutionResult~
    }
    
    class LogNodeExecutor {
        +GetSupportedNodeType() string
        #ExecuteCoreAsync(context, cancellationToken) Task~NodeExecutionResult~
    }
    
    class LoopNodeExecutor {
        +GetSupportedNodeType() string
        #ExecuteCoreAsync(context, cancellationToken) Task~NodeExecutionResult~
        -ResolveArrayData(arrayPath, inputData) List~object~
    }
    
    class NodeExecutorFactory {
        -Dictionary~string,Type~ _executorTypes
        +RegisterExecutor~T~() void
        +CreateExecutor(nodeType) INodeExecutor
        +GetSupportedNodeTypes() List~string~
    }
    
    INodeExecutor <|-- BaseNodeExecutor
    BaseNodeExecutor <|-- StartNodeExecutor
    BaseNodeExecutor <|-- EndNodeExecutor
    BaseNodeExecutor <|-- LogNodeExecutor
    BaseNodeExecutor <|-- LoopNodeExecutor
    NodeExecutorFactory --> INodeExecutor
```

**对应代码**:
```csharp
// BaseNodeExecutor.cs
public abstract class BaseNodeExecutor : INodeExecutor
{
    protected ILogger Logger { get; }

    protected BaseNodeExecutor(ILogger logger)
    {
        Logger = logger;
    }

    public async Task<NodeExecutionResult> ExecuteAsync(
        NodeExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证必需输入
            var validationResult = ValidateRequiredInputs(context);
            if (!validationResult.IsValid)
            {
                return NodeExecutionResult.Failure($"输入验证失败: {string.Join(", ", validationResult.Errors)}");
            }

            // 记录开始执行
            Logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", context.Node.Id, context.Node.Type);

            // 执行核心逻辑
            var result = await ExecuteCoreAsync(context, cancellationToken);

            // 记录执行结果
            if (result.IsSuccess)
            {
                Logger.LogDebug("节点执行成功: {NodeId}", context.Node.Id);
            }
            else
            {
                Logger.LogWarning("节点执行失败: {NodeId}, 错误: {Error}", context.Node.Id, result.ErrorMessage);
            }

            return result;
        }
        catch (OperationCanceledException)
        {
            Logger.LogInformation("节点执行被取消: {NodeId}", context.Node.Id);
            return NodeExecutionResult.Failure("执行被取消");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "节点执行异常: {NodeId}", context.Node.Id);
            return NodeExecutionResult.Failure($"执行异常: {ex.Message}");
        }
    }

    protected abstract Task<NodeExecutionResult> ExecuteCoreAsync(
        NodeExecutionContext context,
        CancellationToken cancellationToken);

    public virtual ValidationResult ValidateConfiguration(Dictionary<string, object> configuration)
    {
        return ValidationResult.Success();
    }

    public abstract string GetSupportedNodeType();

    protected T GetConfigurationValue<T>(Dictionary<string, object> configuration, string key, T defaultValue = default)
    {
        if (configuration == null || !configuration.TryGetValue(key, out var value))
        {
            return defaultValue;
        }

        try
        {
            if (value is T directValue)
            {
                return directValue;
            }

            // 尝试类型转换
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "配置值类型转换失败: {Key} = {Value}", key, value);
            return defaultValue;
        }
    }

    protected T GetInputValue<T>(Dictionary<string, object> inputData, string key, T defaultValue = default)
    {
        if (inputData == null || !inputData.TryGetValue(key, out var value))
        {
            return defaultValue;
        }

        try
        {
            if (value is T directValue)
            {
                return directValue;
            }

            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "输入值类型转换失败: {Key} = {Value}", key, value);
            return defaultValue;
        }
    }

    protected virtual ValidationResult ValidateRequiredInputs(NodeExecutionContext context)
    {
        var result = new ValidationResult();
        
        // 子类可以重写此方法来实现特定的输入验证
        
        return result;
    }
}

// LoopNodeExecutor.cs - 循环节点执行器
public class LoopNodeExecutor : BaseNodeExecutor
{
    public LoopNodeExecutor(ILogger<LoopNodeExecutor> logger) : base(logger)
    {
    }

    public override string GetSupportedNodeType() => "loop";

    protected override async Task<NodeExecutionResult> ExecuteCoreAsync(
        NodeExecutionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var inputData = context.InputData ?? new Dictionary<string, object>();
            
            // 获取参数
            var loopType = GetConfigurationValue<string>(context.Node.Configuration, "loopType", "count");
            var count = GetConfigurationValue<int>(context.Node.Configuration, "count", 5);
            var arrayPath = GetConfigurationValue<string>(context.Node.Configuration, "arrayPath", "input.items");
            var maxIterations = GetConfigurationValue<int>(context.Node.Configuration, "maxIterations", 1000);
            var batchSize = GetConfigurationValue<int>(context.Node.Configuration, "batchSize", 1);

            Logger.LogInformation("循环节点开始执行: {NodeId}, 类型: {LoopType}", context.Node.Id, loopType);

            var results = new List<Dictionary<string, object>>();
            var iterationCount = 0;

            switch (loopType)
            {
                case "count":
                    for (int i = 0; i < count && iterationCount < maxIterations; i++)
                    {
                        var itemData = new Dictionary<string, object>(inputData)
                        {
                            ["index"] = i,
                            ["iteration"] = iterationCount,
                            ["isFirst"] = i == 0,
                            ["isLast"] = i == count - 1
                        };
                        
                        results.Add(itemData);
                        iterationCount++;

                        // 检查取消令牌
                        if (cancellationToken.IsCancellationRequested)
                        {
                            break;
                        }

                        // 批处理延迟
                        if (iterationCount % batchSize == 0)
                        {
                            await Task.Delay(1, cancellationToken);
                        }
                    }
                    break;

                case "array":
                    var arrayData = ResolveArrayData(arrayPath, inputData);
                    if (arrayData != null)
                    {
                        for (int i = 0; i < arrayData.Count && iterationCount < maxIterations; i++)
                        {
                            var itemData = new Dictionary<string, object>(inputData)
                            {
                                ["item"] = arrayData[i],
                                ["index"] = i,
                                ["iteration"] = iterationCount,
                                ["isFirst"] = i == 0,
                                ["isLast"] = i == arrayData.Count - 1,
                                ["totalCount"] = arrayData.Count
                            };
                            
                            results.Add(itemData);
                            iterationCount++;

                            // 检查取消令牌
                            if (cancellationToken.IsCancellationRequested)
                            {
                                break;
                            }

                            // 批处理延迟
                            if (iterationCount % batchSize == 0)
                            {
                                await Task.Delay(1, cancellationToken);
                            }
                        }
                    }
                    break;

                case "condition":
                    // 条件循环实现
                    var condition = GetConfigurationValue<string>(context.Node.Configuration, "condition", "");
                    var conditionEvaluator = new ConditionEvaluator();
                    
                    while (iterationCount < maxIterations)
                    {
                        var itemData = new Dictionary<string, object>(inputData)
                        {
                            ["iteration"] = iterationCount
                        };

                        // 评估条件
                        var shouldContinue = await conditionEvaluator.EvaluateAsync(condition, itemData);
                        if (!shouldContinue)
                        {
                            break;
                        }
                        
                        results.Add(itemData);
                        iterationCount++;

                        // 检查取消令牌
                        if (cancellationToken.IsCancellationRequested)
                        {
                            break;
                        }

                        await Task.Delay(1, cancellationToken);
                    }
                    break;

                default:
                    return NodeExecutionResult.Failure($"不支持的循环类型: {loopType}");
            }

            Logger.LogInformation("循环节点执行完成: {NodeId}, 迭代次数: {IterationCount}", context.Node.Id, iterationCount);

            var outputData = new Dictionary<string, object>
            {
                ["results"] = results,
                ["totalIterations"] = iterationCount,
                ["loopType"] = loopType,
                ["completedAt"] = DateTime.UtcNow,
                ["nodeId"] = context.Node.Id
            };

            // 添加原始输入数据
            foreach (var kvp in inputData)
            {
                if (!outputData.ContainsKey(kvp.Key))
                {
                    outputData[kvp.Key] = kvp.Value;
                }
            }

            return NodeExecutionResult.Success(outputData);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "循环节点执行失败: {NodeId}", context.Node.Id);
            return NodeExecutionResult.Failure($"循环节点执行失败: {ex.Message}");
        }
    }

    private List<object> ResolveArrayData(string arrayPath, Dictionary<string, object> inputData)
    {
        try
        {
            if (string.IsNullOrEmpty(arrayPath))
            {
                return null;
            }

            var pathParts = arrayPath.Split('.');
            object current = inputData;

            foreach (var part in pathParts)
            {
                if (current is Dictionary<string, object> dict)
                {
                    if (!dict.TryGetValue(part, out current))
                    {
                        return null;
                    }
                }
                else
                {
                    // 使用反射获取属性值
                    var property = current?.GetType().GetProperty(part);
                    if (property == null)
                    {
                        return null;
                    }
                    current = property.GetValue(current);
                }
            }

            // 转换为列表
            if (current is IEnumerable<object> enumerable)
            {
                return enumerable.ToList();
            }
            else if (current is IEnumerable nonGenericEnumerable)
            {
                return nonGenericEnumerable.Cast<object>().ToList();
            }

            return null;
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "解析数组路径失败: {ArrayPath}", arrayPath);
            return null;
        }
    }

    public override ValidationResult ValidateConfiguration(Dictionary<string, object> configuration)
    {
        var result = new ValidationResult();

        var loopType = GetConfigurationValue<string>(configuration, "loopType", "count");
        
        switch (loopType)
        {
            case "count":
                var count = GetConfigurationValue<int>(configuration, "count", 0);
                if (count <= 0)
                {
                    result.AddError("count 必须大于 0");
                }
                break;

            case "array":
                var arrayPath = GetConfigurationValue<string>(configuration, "arrayPath", "");
                if (string.IsNullOrEmpty(arrayPath))
                {
                    result.AddError("arrayPath 不能为空");
                }
                break;

            case "condition":
                var condition = GetConfigurationValue<string>(configuration, "condition", "");
                if (string.IsNullOrEmpty(condition))
                {
                    result.AddError("condition 不能为空");
                }
                break;

            default:
                result.AddError($"不支持的循环类型: {loopType}");
                break;
        }

        var maxIterations = GetConfigurationValue<int>(configuration, "maxIterations", 1000);
        if (maxIterations <= 0)
        {
            result.AddError("maxIterations 必须大于 0");
        }

        return result;
    }
}

// NodeExecutorFactory.cs - 节点执行器工厂
public class NodeExecutorFactory : INodeExecutorFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<string, Type> _executorTypes = new();
    private readonly ILogger<NodeExecutorFactory> _logger;

    public NodeExecutorFactory(IServiceProvider serviceProvider, ILogger<NodeExecutorFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        RegisterBuiltinExecutors();
    }

    public void RegisterExecutor<T>() where T : class, INodeExecutor
    {
        var executor = ActivatorUtilities.CreateInstance<T>(_serviceProvider);
        var nodeType = executor.GetSupportedNodeType();
        
        _executorTypes[nodeType] = typeof(T);
        _logger.LogInformation("注册节点执行器: {NodeType} -> {ExecutorType}", nodeType, typeof(T).Name);
    }

    public INodeExecutor CreateExecutor(string nodeType)
    {
        if (!_executorTypes.TryGetValue(nodeType, out var executorType))
        {
            _logger.LogWarning("未找到节点类型 {NodeType} 的执行器", nodeType);
            return null;
        }

        try
        {
            return (INodeExecutor)ActivatorUtilities.CreateInstance(_serviceProvider, executorType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建节点执行器失败: {NodeType}", nodeType);
            return null;
        }
    }

    public List<string> GetSupportedNodeTypes()
    {
        return _executorTypes.Keys.ToList();
    }

    private void RegisterBuiltinExecutors()
    {
        RegisterExecutor<StartNodeExecutor>();
        RegisterExecutor<EndNodeExecutor>();
        RegisterExecutor<LogNodeExecutor>();
        RegisterExecutor<LoopNodeExecutor>();
        RegisterExecutor<ScriptExecutorNodeExecutor>();
        RegisterExecutor<HttpRequestNodeExecutor>();
        RegisterExecutor<DataTransformNodeExecutor>();
    }
}

// 条件评估器
public class ConditionEvaluator
{
    public async Task<bool> EvaluateAsync(string condition, Dictionary<string, object> context)
    {
        try
        {
            // 这里可以使用JavaScript引擎或表达式解析器
            // 简化实现，支持基本的比较操作
            
            if (string.IsNullOrEmpty(condition))
            {
                return false;
            }

            // 简单的条件解析示例
            if (condition.Contains("iteration") && condition.Contains("<"))
            {
                var parts = condition.Split('<');
                if (parts.Length == 2 && 
                    context.TryGetValue("iteration", out var iterationValue) &&
                    int.TryParse(parts[1].Trim(), out var maxValue))
                {
                    return Convert.ToInt32(iterationValue) < maxValue;
                }
            }

            // 默认返回false以避免无限循环
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }
}
```

## 📊 4. 数据访问软件

**软件描述**: 基于Entity Framework Core的数据访问层，提供工作流定义、执行记录和系统配置的持久化存储。

### 4.1 功能模块分解
```mermaid
graph TB
    subgraph "数据访问软件"
        M1[仓储模式模块]
        M2[数据上下文模块]
        M3[实体映射模块]
        M4[查询优化模块]
        M5[事务管理模块]
        M6[缓存集成模块]
    end
    
    M2 --> M1
    M3 --> M2
    M1 --> M4
    M1 --> M5
    M4 --> M6
```

### 4.2 工作流仓储模块

**模块描述**: 实现工作流定义的数据访问操作，包括CRUD操作、查询过滤和分页支持。

**功能职责**:
- 工作流定义的增删改查
- 复杂查询和过滤
- 分页和排序支持
- 数据验证和约束检查

**输入接口**:
- 工作流实体对象
- 查询过滤条件
- 分页参数
- 排序规则

**输出接口**:
- 工作流实体数据
- 分页结果集
- 操作执行结果
- 数据验证结果

**对应代码**:
```csharp
// WorkflowRepository.cs
public class WorkflowRepository : IWorkflowRepository
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<WorkflowRepository> _logger;
    private readonly IMemoryCache _cache;

    public WorkflowRepository(
        FlowCustomDbContext context, 
        ILogger<WorkflowRepository> logger,
        IMemoryCache cache)
    {
        _context = context;
        _logger = logger;
        _cache = cache;
    }

    public async Task<PagedResult<WorkflowDefinition>> GetAllAsync(
        WorkflowFilter filter = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Workflows.AsQueryable();

            // 应用过滤条件
            if (filter != null)
            {
                if (!string.IsNullOrEmpty(filter.Name))
                {
                    query = query.Where(w => w.Name.Contains(filter.Name));
                }

                if (!string.IsNullOrEmpty(filter.Category))
                {
                    query = query.Where(w => w.Category == filter.Category);
                }

                if (filter.Status.HasValue)
                {
                    query = query.Where(w => w.Status == filter.Status.Value);
                }

                if (!string.IsNullOrEmpty(filter.CreatedBy))
                {
                    query = query.Where(w => w.CreatedBy == filter.CreatedBy);
                }

                if (filter.CreatedAfter.HasValue)
                {
                    query = query.Where(w => w.CreatedAt >= filter.CreatedAfter.Value);
                }

                if (filter.CreatedBefore.HasValue)
                {
                    query = query.Where(w => w.CreatedAt <= filter.CreatedBefore.Value);
                }

                if (filter.Tags != null && filter.Tags.Any())
                {
                    query = query.Where(w => filter.Tags.All(tag => w.Tags.Contains(tag)));
                }
            }

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 应用排序
            var sortField = filter?.SortBy ?? "CreatedAt";
            var sortDirection = filter?.SortDirection ?? "desc";

            query = sortField.ToLower() switch
            {
                "name" => sortDirection == "asc" ? query.OrderBy(w => w.Name) : query.OrderByDescending(w => w.Name),
                "status" => sortDirection == "asc" ? query.OrderBy(w => w.Status) : query.OrderByDescending(w => w.Status),
                "createdat" => sortDirection == "asc" ? query.OrderBy(w => w.CreatedAt) : query.OrderByDescending(w => w.CreatedAt),
                "updatedat" => sortDirection == "asc" ? query.OrderBy(w => w.UpdatedAt) : query.OrderByDescending(w => w.UpdatedAt),
                _ => query.OrderByDescending(w => w.CreatedAt)
            };

            // 应用分页
            var pageNumber = filter?.PageNumber ?? 1;
            var pageSize = filter?.PageSize ?? 20;
            
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(w => w.Nodes)
                .Include(w => w.Connections)
                .ToListAsync(cancellationToken);

            return new PagedResult<WorkflowDefinition>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流列表失败");
            throw;
        }
    }

    public async Task<WorkflowDefinition> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            // 先尝试从缓存获取
            var cacheKey = $"workflow:{id}";
            if (_cache.TryGetValue(cacheKey, out WorkflowDefinition cachedWorkflow))
            {
                return cachedWorkflow;
            }

            var workflow = await _context.Workflows
                .Include(w => w.Nodes)
                .Include(w => w.Connections)
                .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);

            // 缓存结果
            if (workflow != null)
            {
                _cache.Set(cacheKey, workflow, TimeSpan.FromMinutes(10));
            }

            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流失败: {WorkflowId}", id);
            throw;
        }
    }

    public async Task<WorkflowDefinition> CreateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证工作流
            await ValidateWorkflowAsync(workflow);

            // 设置创建时间
            workflow.CreatedAt = DateTime.UtcNow;
            workflow.UpdatedAt = DateTime.UtcNow;
            workflow.Version = 1;

            // 为节点和连接生成ID
            foreach (var node in workflow.Nodes)
            {
                if (node.Id == Guid.Empty)
                {
                    node.Id = Guid.NewGuid();
                }
                node.WorkflowId = workflow.Id;
                node.CreatedAt = DateTime.UtcNow;
            }

            foreach (var connection in workflow.Connections)
            {
                if (connection.Id == Guid.Empty)
                {
                    connection.Id = Guid.NewGuid();
                }
                connection.WorkflowId = workflow.Id;
                connection.CreatedAt = DateTime.UtcNow;
            }

            _context.Workflows.Add(workflow);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("工作流创建成功: {WorkflowId} - {WorkflowName}", workflow.Id, workflow.Name);

            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建工作流失败: {WorkflowName}", workflow?.Name);
            throw;
        }
    }

    public async Task<WorkflowDefinition> UpdateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证工作流
            await ValidateWorkflowAsync(workflow);

            var existingWorkflow = await _context.Workflows
                .Include(w => w.Nodes)
                .Include(w => w.Connections)
                .FirstOrDefaultAsync(w => w.Id == workflow.Id, cancellationToken);

            if (existingWorkflow == null)
            {
                throw new NotFoundException($"工作流 {workflow.Id} 不存在");
            }

            // 检查并发冲突
            if (existingWorkflow.Version != workflow.Version)
            {
                throw new ConcurrencyException("工作流已被其他用户修改");
            }

            // 更新基本信息
            existingWorkflow.Name = workflow.Name;
            existingWorkflow.Description = workflow.Description;
            existingWorkflow.Category = workflow.Category;
            existingWorkflow.Tags = workflow.Tags;
            existingWorkflow.Variables = workflow.Variables;
            existingWorkflow.Status = workflow.Status;
            existingWorkflow.UpdatedAt = DateTime.UtcNow;
            existingWorkflow.UpdatedBy = workflow.UpdatedBy;
            existingWorkflow.Version++;

            // 更新节点
            await UpdateNodesAsync(existingWorkflow, workflow.Nodes, cancellationToken);

            // 更新连接
            await UpdateConnectionsAsync(existingWorkflow, workflow.Connections, cancellationToken);

            await _context.SaveChangesAsync(cancellationToken);

            // 清除缓存
            var cacheKey = $"workflow:{workflow.Id}";
            _cache.Remove(cacheKey);

            _logger.LogInformation("工作流更新成功: {WorkflowId} - {WorkflowName}", workflow.Id, workflow.Name);

            return existingWorkflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新工作流失败: {WorkflowId}", workflow?.Id);
            throw;
        }
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _context.Workflows
                .Include(w => w.Nodes)
                .Include(w => w.Connections)
                .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);

            if (workflow == null)
            {
                throw new NotFoundException($"工作流 {id} 不存在");
            }

            // 检查是否有正在执行的实例
            var hasRunningExecutions = await _context.WorkflowExecutions
                .AnyAsync(e => e.WorkflowId == id && 
                         (e.Status == ExecutionStatus.Running || e.Status == ExecutionStatus.Initializing), 
                         cancellationToken);

            if (hasRunningExecutions)
            {
                throw new InvalidOperationException("无法删除有正在执行实例的工作流");
            }

            _context.Workflows.Remove(workflow);
            await _context.SaveChangesAsync(cancellationToken);

            // 清除缓存
            var cacheKey = $"workflow:{id}";
            _cache.Remove(cacheKey);

            _logger.LogInformation("工作流删除成功: {WorkflowId}", id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流失败: {WorkflowId}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Workflows.AnyAsync(w => w.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _context.Workflows.Where(w => w.Name == name);
        
        if (excludeId.HasValue)
        {
            query = query.Where(w => w.Id != excludeId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    private async Task ValidateWorkflowAsync(WorkflowDefinition workflow)
    {
        var errors = new List<string>();

        // 验证基本信息
        if (string.IsNullOrEmpty(workflow.Name))
        {
            errors.Add("工作流名称不能为空");
        }

        if (workflow.Name?.Length > 100)
        {
            errors.Add("工作流名称长度不能超过100个字符");
        }

        // 检查名称唯一性
        if (await ExistsByNameAsync(workflow.Name, workflow.Id))
        {
            errors.Add($"工作流名称 '{workflow.Name}' 已存在");
        }

        // 验证节点
        if (workflow.Nodes == null || !workflow.Nodes.Any())
        {
            errors.Add("工作流必须包含至少一个节点");
        }
        else
        {
            var nodeIds = workflow.Nodes.Select(n => n.Id).ToList();
            var duplicateIds = nodeIds.GroupBy(id => id).Where(g => g.Count() > 1).Select(g => g.Key);
            
            if (duplicateIds.Any())
            {
                errors.Add($"存在重复的节点ID: {string.Join(", ", duplicateIds)}");
            }

            // 验证必须有开始节点
            if (!workflow.Nodes.Any(n => n.Type == "start"))
            {
                errors.Add("工作流必须包含至少一个开始节点");
            }
        }

        // 验证连接
        if (workflow.Connections != null)
        {
            var nodeIds = workflow.Nodes.Select(n => n.Id).ToHashSet();
            
            foreach (var connection in workflow.Connections)
            {
                if (!nodeIds.Contains(connection.SourceNodeId))
                {
                    errors.Add($"连接的源节点 {connection.SourceNodeId} 不存在");
                }

                if (!nodeIds.Contains(connection.TargetNodeId))
                {
                    errors.Add($"连接的目标节点 {connection.TargetNodeId} 不存在");
                }

                if (connection.SourceNodeId == connection.TargetNodeId)
                {
                    errors.Add("不能创建自连接");
                }
            }

            // 检查循环依赖
            if (HasCircularDependency(workflow))
            {
                errors.Add("工作流存在循环依赖");
            }
        }

        if (errors.Any())
        {
            throw new ValidationException("工作流验证失败", errors);
        }
    }

    private bool HasCircularDependency(WorkflowDefinition workflow)
    {
        var graph = new Dictionary<Guid, List<Guid>>();
        
        // 构建邻接表
        foreach (var node in workflow.Nodes)
        {
            graph[node.Id] = new List<Guid>();
        }

        foreach (var connection in workflow.Connections)
        {
            graph[connection.SourceNodeId].Add(connection.TargetNodeId);
        }

        // 使用DFS检测循环
        var visited = new HashSet<Guid>();
        var recursionStack = new HashSet<Guid>();

        foreach (var nodeId in graph.Keys)
        {
            if (HasCycleDFS(nodeId, graph, visited, recursionStack))
            {
                return true;
            }
        }

        return false;
    }

    private bool HasCycleDFS(
        Guid nodeId,
        Dictionary<Guid, List<Guid>> graph,
        HashSet<Guid> visited,
        HashSet<Guid> recursionStack)
    {
        if (recursionStack.Contains(nodeId))
        {
            return true; // 发现循环
        }

        if (visited.Contains(nodeId))
        {
            return false; // 已访问过，无循环
        }

        visited.Add(nodeId);
        recursionStack.Add(nodeId);

        foreach (var neighbor in graph[nodeId])
        {
            if (HasCycleDFS(neighbor, graph, visited, recursionStack))
            {
                return true;
            }
        }

        recursionStack.Remove(nodeId);
        return false;
    }

    private async Task UpdateNodesAsync(
        WorkflowDefinition existingWorkflow,
        List<WorkflowNode> newNodes,
        CancellationToken cancellationToken)
    {
        // 删除不存在的节点
        var newNodeIds = newNodes.Select(n => n.Id).ToHashSet();
        var nodesToRemove = existingWorkflow.Nodes.Where(n => !newNodeIds.Contains(n.Id)).ToList();
        
        foreach (var node in nodesToRemove)
        {
            _context.WorkflowNodes.Remove(node);
        }

        // 更新或添加节点
        foreach (var newNode in newNodes)
        {
            var existingNode = existingWorkflow.Nodes.FirstOrDefault(n => n.Id == newNode.Id);
            
            if (existingNode != null)
            {
                // 更新现有节点
                existingNode.Name = newNode.Name;
                existingNode.Type = newNode.Type;
                existingNode.Configuration = newNode.Configuration;
                existingNode.Position = newNode.Position;
                existingNode.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // 添加新节点
                newNode.WorkflowId = existingWorkflow.Id;
                newNode.CreatedAt = DateTime.UtcNow;
                newNode.UpdatedAt = DateTime.UtcNow;
                existingWorkflow.Nodes.Add(newNode);
            }
        }
    }

    private async Task UpdateConnectionsAsync(
        WorkflowDefinition existingWorkflow,
        List<WorkflowConnection> newConnections,
        CancellationToken cancellationToken)
    {
        // 删除不存在的连接
        var newConnectionIds = newConnections.Select(c => c.Id).ToHashSet();
        var connectionsToRemove = existingWorkflow.Connections.Where(c => !newConnectionIds.Contains(c.Id)).ToList();
        
        foreach (var connection in connectionsToRemove)
        {
            _context.WorkflowConnections.Remove(connection);
        }

        // 更新或添加连接
        foreach (var newConnection in newConnections)
        {
            var existingConnection = existingWorkflow.Connections.FirstOrDefault(c => c.Id == newConnection.Id);
            
            if (existingConnection != null)
            {
                // 更新现有连接
                existingConnection.SourceNodeId = newConnection.SourceNodeId;
                existingConnection.TargetNodeId = newConnection.TargetNodeId;
                existingConnection.SourceEndpointId = newConnection.SourceEndpointId;
                existingConnection.TargetEndpointId = newConnection.TargetEndpointId;
                existingConnection.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // 添加新连接
                newConnection.WorkflowId = existingWorkflow.Id;
                newConnection.CreatedAt = DateTime.UtcNow;
                newConnection.UpdatedAt = DateTime.UtcNow;
                existingWorkflow.Connections.Add(newConnection);
            }
        }
    }
}

// WorkflowExecutionRepository.cs
public class WorkflowExecutionRepository : IWorkflowExecutionRepository
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<WorkflowExecutionRepository> _logger;

    public WorkflowExecutionRepository(
        FlowCustomDbContext context,
        ILogger<WorkflowExecutionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PagedResult<WorkflowExecution>> GetAllAsync(
        ExecutionFilter filter = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowExecutions.AsQueryable();

            // 应用过滤条件
            if (filter != null)
            {
                if (filter.WorkflowId.HasValue)
                {
                    query = query.Where(e => e.WorkflowId == filter.WorkflowId.Value);
                }

                if (filter.Status.HasValue)
                {
                    query = query.Where(e => e.Status == filter.Status.Value);
                }

                if (filter.StartedAfter.HasValue)
                {
                    query = query.Where(e => e.StartedAt >= filter.StartedAfter.Value);
                }

                if (filter.StartedBefore.HasValue)
                {
                    query = query.Where(e => e.StartedAt <= filter.StartedBefore.Value);
                }

                if (!string.IsNullOrEmpty(filter.StartedBy))
                {
                    query = query.Where(e => e.StartedBy == filter.StartedBy);
                }
            }

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 应用排序
            var sortField = filter?.SortBy ?? "StartedAt";
            var sortDirection = filter?.SortDirection ?? "desc";

            query = sortField.ToLower() switch
            {
                "startedat" => sortDirection == "asc" ? query.OrderBy(e => e.StartedAt) : query.OrderByDescending(e => e.StartedAt),
                "completedat" => sortDirection == "asc" ? query.OrderBy(e => e.CompletedAt) : query.OrderByDescending(e => e.CompletedAt),
                "status" => sortDirection == "asc" ? query.OrderBy(e => e.Status) : query.OrderByDescending(e => e.Status),
                _ => query.OrderByDescending(e => e.StartedAt)
            };

            // 应用分页
            var pageNumber = filter?.PageNumber ?? 1;
            var pageSize = filter?.PageSize ?? 20;

            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(e => e.NodeExecutions)
                .ToListAsync(cancellationToken);

            return new PagedResult<WorkflowExecution>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流执行列表失败");
            throw;
        }
    }

    public async Task<WorkflowExecution> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowExecutions
                .Include(e => e.NodeExecutions)
                .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流执行失败: {ExecutionId}", id);
            throw;
        }
    }

    public async Task<WorkflowExecution> CreateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            execution.CreatedAt = DateTime.UtcNow;
            execution.UpdatedAt = DateTime.UtcNow;

            _context.WorkflowExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("工作流执行记录创建成功: {ExecutionId}", execution.Id);
            return execution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建工作流执行记录失败: {ExecutionId}", execution?.Id);
            throw;
        }
    }

    public async Task<WorkflowExecution> UpdateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            execution.UpdatedAt = DateTime.UtcNow;
            
            _context.WorkflowExecutions.Update(execution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("工作流执行记录更新成功: {ExecutionId}", execution.Id);
            return execution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新工作流执行记录失败: {ExecutionId}", execution?.Id);
            throw;
        }
    }

    public async Task<List<WorkflowExecution>> GetRunningExecutionsAsync(Guid workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowExecutions
                .Where(e => e.WorkflowId == workflowId && 
                           (e.Status == ExecutionStatus.Running || e.Status == ExecutionStatus.Initializing))
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取正在运行的执行记录失败: {WorkflowId}", workflowId);
            throw;
        }
    }

    public async Task<List<WorkflowExecution>> GetExecutionsByStatusAsync(
        ExecutionStatus status, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowExecutions
                .Where(e => e.Status == status)
                .OrderBy(e => e.StartedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按状态获取执行记录失败: {Status}", status);
            throw;
        }
    }
}
```

### 4.3 数据上下文模块

**模块描述**: Entity Framework Core数据上下文，负责数据库连接管理、实体配置和数据库迁移。

**功能职责**:
- 数据库连接配置
- 实体关系映射
- 数据库迁移管理
- 查询性能优化

**对应代码**:
```csharp
// FlowCustomDbContext.cs
public class FlowCustomDbContext : DbContext
{
    public FlowCustomDbContext(DbContextOptions<FlowCustomDbContext> options) : base(options)
    {
    }

    // 工作流相关实体
    public DbSet<WorkflowDefinition> Workflows { get; set; }
    public DbSet<WorkflowNode> WorkflowNodes { get; set; }
    public DbSet<WorkflowConnection> WorkflowConnections { get; set; }
    public DbSet<WorkflowExecution> WorkflowExecutions { get; set; }
    public DbSet<NodeExecution> NodeExecutions { get; set; }

    // 系统配置实体
    public DbSet<SystemConfiguration> SystemConfigurations { get; set; }
    public DbSet<NodeTypeDefinition> NodeTypeDefinitions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置工作流定义
        modelBuilder.Entity<WorkflowDefinition>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);
            entity.Property(e => e.UpdatedBy).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();
            
            // JSON列配置
            entity.Property(e => e.Variables)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.Name).IsUnique();
            entity.HasIndex(e => e.Category);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);

            // 关系配置
            entity.HasMany(e => e.Nodes)
                .WithOne(n => n.Workflow)
                .HasForeignKey(n => n.WorkflowId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Connections)
                .WithOne(c => c.Workflow)
                .HasForeignKey(c => c.WorkflowId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Executions)
                .WithOne(ex => ex.Workflow)
                .HasForeignKey(ex => ex.WorkflowId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // 配置工作流节点
        modelBuilder.Entity<WorkflowNode>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
            
            // JSON列配置
            entity.Property(e => e.Configuration)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.Position)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<NodePosition>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.Type);
        });

        // 配置工作流连接
        modelBuilder.Entity<WorkflowConnection>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SourceEndpointId).HasMaxLength(50);
            entity.Property(e => e.TargetEndpointId).HasMaxLength(50);

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.SourceNodeId);
            entity.HasIndex(e => e.TargetNodeId);

            // 外键约束
            entity.HasOne<WorkflowNode>()
                .WithMany()
                .HasForeignKey(e => e.SourceNodeId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne<WorkflowNode>()
                .WithMany()
                .HasForeignKey(e => e.TargetNodeId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // 配置工作流执行
        modelBuilder.Entity<WorkflowExecution>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.StartedBy).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Error).HasMaxLength(1000);
            
            // JSON列配置
            entity.Property(e => e.Parameters)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.Variables)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.NodeOutputs)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.StartedAt);
            entity.HasIndex(e => e.CompletedAt);

            // 关系配置
            entity.HasMany(e => e.NodeExecutions)
                .WithOne(ne => ne.WorkflowExecution)
                .HasForeignKey(ne => ne.WorkflowExecutionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置节点执行
        modelBuilder.Entity<NodeExecution>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.NodeId).IsRequired();
            entity.Property(e => e.NodeType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Error).HasMaxLength(1000);
            
            // JSON列配置
            entity.Property(e => e.InputData)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.OutputData)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowExecutionId);
            entity.HasIndex(e => e.NodeId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.StartedAt);
        });

        // 配置节点类型定义
        modelBuilder.Entity<NodeTypeDefinition>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Icon).HasMaxLength(100);
            entity.Property(e => e.Color).HasMaxLength(20);
            
            // JSON列配置
            entity.Property(e => e.InputEndpoints)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<EndpointDefinition>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.OutputEndpoints)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<EndpointDefinition>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.ConfigurationSchema)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.Type).IsUnique();
            entity.HasIndex(e => e.Category);
        });

        // 配置系统配置
        modelBuilder.Entity<SystemConfiguration>(entity =>
        {
            entity.HasKey(e => e.Key);
            entity.Property(e => e.Key).HasMaxLength(100);
            entity.Property(e => e.Value).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.DataType).HasMaxLength(20);

            // 索引
            entity.HasIndex(e => e.Category);
        });
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // 开发环境默认配置
            optionsBuilder.UseSqlite("Data Source=flowcustom.db");
        }

        // 性能优化配置
        optionsBuilder.EnableSensitiveDataLogging(false);
        optionsBuilder.EnableServiceProviderCaching();
        optionsBuilder.EnableDetailedErrors(false);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // 自动设置审计字段
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;
            
            if (entry.State == EntityState.Added)
            {
                entity.CreatedAt = DateTime.UtcNow;
            }
            
            entity.UpdatedAt = DateTime.UtcNow;
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}

// 数据库初始化服务
public class DatabaseInitializer
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<DatabaseInitializer> _logger;

    public DatabaseInitializer(FlowCustomDbContext context, ILogger<DatabaseInitializer> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // 确保数据库已创建
            await _context.Database.EnsureCreatedAsync();
            
            // 运行待处理的迁移
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("应用数据库迁移: {Migrations}", string.Join(", ", pendingMigrations));
                await _context.Database.MigrateAsync();
            }

            // 初始化种子数据
            await SeedDataAsync();

            _logger.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    private async Task SeedDataAsync()
    {
        // 初始化节点类型定义
        if (!await _context.NodeTypeDefinitions.AnyAsync())
        {
            var nodeTypes = GetBuiltinNodeTypes();
            _context.NodeTypeDefinitions.AddRange(nodeTypes);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("初始化节点类型定义完成，共 {Count} 个类型", nodeTypes.Count);
        }

        // 初始化系统配置
        if (!await _context.SystemConfigurations.AnyAsync())
        {
            var configurations = GetDefaultSystemConfigurations();
            _context.SystemConfigurations.AddRange(configurations);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("初始化系统配置完成，共 {Count} 个配置项", configurations.Count);
        }
    }

    private List<NodeTypeDefinition> GetBuiltinNodeTypes()
    {
        return new List<NodeTypeDefinition>
        {
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "start",
                Name = "开始",
                Description = "工作流开始节点",
                Category = "system",
                Icon = "play-circle",
                Color = "#22c55e",
                InputEndpoints = new List<EndpointDefinition>(),
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "any" }
                },
                ConfigurationSchema = new Dictionary<string, object>()
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "end",
                Name = "结束",
                Description = "工作流结束节点",
                Category = "system",
                Icon = "stop-circle",
                Color = "#ef4444",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>(),
                ConfigurationSchema = new Dictionary<string, object>()
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "log",
                Name = "日志",
                Description = "记录日志信息",
                Category = "system",
                Icon = "file-text",
                Color = "#3b82f6",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "any" }
                },
                ConfigurationSchema = new Dictionary<string, object>
                {
                    ["message"] = new { type = "string", required = true, description = "日志消息" },
                    ["level"] = new { type = "string", enum = new[] { "info", "warn", "error" }, default = "info" }
                }
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "loop",
                Name = "循环",
                Description = "循环执行节点",
                Category = "logic",
                Icon = "repeat",
                Color = "#8b5cf6",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "array" }
                },
                ConfigurationSchema = new Dictionary<string, object>
                {
                    ["loopType"] = new { type = "string", enum = new[] { "count", "array", "condition" }, default = "count" },
                    ["count"] = new { type = "number", minimum = 1, maximum = 1000, default = 5 },
                    ["arrayPath"] = new { type = "string", description = "数组数据路径" },
                    ["condition"] = new { type = "string", description = "循环条件表达式" },
                    ["maxIterations"] = new { type = "number", minimum = 1, maximum = 10000, default = 1000 }
                }
            }
            // 可以继续添加更多内置节点类型...
        };
    }

    private List<SystemConfiguration> GetDefaultSystemConfigurations()
    {
        return new List<SystemConfiguration>
        {
            new SystemConfiguration
            {
                Key = "workflow.execution.timeout",
                Value = "3600",
                Description = "工作流执行超时时间（秒）",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "workflow.execution.maxConcurrency",
                Value = "10",
                Description = "最大并发执行数",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "node.execution.timeout",
                Value = "300",
                Description = "节点执行超时时间（秒）",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "system.log.level",
                Value = "Information",
                Description = "系统日志级别",
                Category = "logging",
                DataType = "string"
            },
            new SystemConfiguration
            {
                Key = "system.log.retention",
                Value = "30",
                Description = "日志保留天数",
                Category = "logging",
                DataType = "number"
            }
        };
    }
}
```

## 📊 5. 前端用户界面软件

**软件描述**: 基于React 18和TypeScript的现代化前端应用，提供直观的工作流设计和管理界面。

### 5.1 功能模块分解
```mermaid
graph TB
    subgraph "前端用户界面软件"
        M1[工作流设计器模块]
        M2[节点配置模块]
        M3[执行监控模块]
        M4[状态管理模块]
        M5[API通信模块]
        M6[UI组件库模块]
    end
    
    M1 --> M2
    M1 --> M4
    M3 --> M4
    M4 --> M5
    M2 --> M6
    M3 --> M6
```

### 5.2 工作流设计器模块

**模块描述**: 基于ReactFlow的可视化工作流设计器，支持节点拖拽、连接和实时编辑。

**功能职责**:
- 工作流画布管理
- 节点拖拽和定位
- 连接线绘制和验证
- 实时预览和保存

**输入接口**:
- 工作流定义数据
- 节点类型定义
- 用户交互事件
- 配置更新事件

**输出接口**:
- 工作流定义更新
- 节点配置请求
- 保存操作触发
- 状态变更通知

**组件层次图**:
```mermaid
graph TB
    subgraph "工作流设计器组件"
        A[WorkflowDesignerOptimized]
        B[CustomNode]
        C[CustomEdge]
        D[NodePalette]
        E[UnifiedNodeConfigPanel]
        F[WorkflowToolbar]
        G[MiniMap]
        H[Controls]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    
    B --> E
    D --> B
```

**对应代码**:
```typescript
// WorkflowDesignerOptimized.tsx
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Connection,
  NodeChange,
  EdgeChange,
  Background,
  Controls,
  MiniMap,
  Panel,
  useReactFlow,
  ReactFlowProvider
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { CustomNode } from './CustomNode';
import { NodePalette } from './NodePalette';
import { UnifiedNodeConfigPanel } from './UnifiedNodeConfigPanel';
import { WorkflowToolbar } from './WorkflowToolbar';
import { useWorkflowStore } from '../stores/workflowStore';
import { useUIStore } from '../stores/uiStore';
import { workflowApi } from '../services/api';
import { toast } from 'react-hot-toast';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowDesignerOptimizedProps {
  workflowId?: string;
  readOnly?: boolean;
}

const WorkflowDesignerContent: React.FC<WorkflowDesignerOptimizedProps> = ({
  workflowId,
  readOnly = false
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const { 
    currentWorkflow, 
    setCurrentWorkflow, 
    updateWorkflow,
    isModified,
    setIsModified 
  } = useWorkflowStore();
  
  const { 
    showNodeConfig, 
    setShowNodeConfig,
    showNodePalette,
    setShowNodePalette 
  } = useUIStore();

  const { getViewport, setViewport } = useReactFlow();

  // 加载工作流数据
  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const loadWorkflow = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const response = await workflowApi.getWorkflow(id);
      
      if (response.success && response.data) {
        const workflow = response.data;
        setCurrentWorkflow(workflow);
        
        // 转换节点数据
        const flowNodes = workflow.nodes?.map(node => ({
          id: node.id,
          type: 'custom',
          position: node.position || { x: 0, y: 0 },
          data: {
            id: node.id,
            name: node.name,
            type: node.type,
            configuration: node.configuration || {},
            onConfigClick: handleNodeConfigClick,
            onDelete: handleNodeDelete,
            readOnly
          }
        })) || [];

        // 转换连接数据
        const flowEdges = workflow.connections?.map(conn => ({
          id: conn.id,
          source: conn.sourceNodeId,
          target: conn.targetNodeId,
          sourceHandle: conn.sourceEndpointId,
          targetHandle: conn.targetEndpointId,
          type: 'smoothstep',
          animated: false
        })) || [];

        setNodes(flowNodes);
        setEdges(flowEdges);
        setIsModified(false);
      }
    } catch (error) {
      console.error('加载工作流失败:', error);
      toast.error('加载工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [setCurrentWorkflow, setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点变更
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChange(changes);
    
    // 检查是否有位置变更
    const hasPositionChange = changes.some(change => 
      change.type === 'position' && change.dragging === false
    );
    
    if (hasPositionChange) {
      setIsModified(true);
    }
  }, [onNodesChange, setIsModified]);

  // 处理连接变更
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    onEdgesChange(changes);
    setIsModified(true);
  }, [onEdgesChange, setIsModified]);

  // 处理新连接
  const onConnect = useCallback((connection: Connection) => {
    if (readOnly) return;

    const newEdge = {
      ...connection,
      id: `edge-${Date.now()}`,
      type: 'smoothstep',
      animated: false
    };

    setEdges(eds => addEdge(newEdge, eds));
    setIsModified(true);
  }, [setEdges, setIsModified, readOnly]);

  // 处理节点配置点击
  const handleNodeConfigClick = useCallback((nodeId: string) => {
    setSelectedNodeId(nodeId);
    setShowNodeConfig(true);
  }, [setShowNodeConfig]);

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    if (readOnly) return;

    setNodes(nds => nds.filter(node => node.id !== nodeId));
    setEdges(eds => eds.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setIsModified(true);
    toast.success('节点已删除');
  }, [setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string, position: { x: number; y: number }) => {
    if (readOnly) return;

    const newNodeId = `node-${Date.now()}`;
    const newNode = {
      id: newNodeId,
      type: 'custom',
      position,
      data: {
        id: newNodeId,
        name: `${nodeType} 节点`,
        type: nodeType,
        configuration: {},
        onConfigClick: handleNodeConfigClick,
        onDelete: handleNodeDelete,
        readOnly: false
      }
    };

    setNodes(nds => [...nds, newNode]);
    setIsModified(true);
    toast.success('节点已添加');
  }, [setNodes, setIsModified, handleNodeConfigClick, handleNodeDelete, readOnly]);

  // 处理节点配置更新
  const handleNodeConfigUpdate = useCallback((nodeId: string, config: any) => {
    setNodes(nds => nds.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            name: config.name || node.data.name,
            configuration: config.configuration || node.data.configuration
          }
        };
      }
      return node;
    }));
    setIsModified(true);
  }, [setNodes, setIsModified]);

  // 保存工作流
  const handleSave = useCallback(async () => {
    if (!currentWorkflow || readOnly) return;

    try {
      setIsLoading(true);

      // 构建工作流数据
      const workflowData = {
        ...currentWorkflow,
        nodes: nodes.map(node => ({
          id: node.id,
          name: node.data.name,
          type: node.data.type,
          configuration: node.data.configuration,
          position: node.position,
          workflowId: currentWorkflow.id
        })),
        connections: edges.map(edge => ({
          id: edge.id,
          sourceNodeId: edge.source,
          targetNodeId: edge.target,
          sourceEndpointId: edge.sourceHandle || 'output',
          targetEndpointId: edge.targetHandle || 'input',
          workflowId: currentWorkflow.id
        }))
      };

      let response;
      if (workflowId) {
        response = await workflowApi.updateWorkflow(workflowId, workflowData);
      } else {
        response = await workflowApi.createWorkflow(workflowData);
      }

      if (response.success) {
        setCurrentWorkflow(response.data);
        setIsModified(false);
        toast.success('工作流保存成功');
      } else {
        throw new Error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      toast.error('保存工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, nodes, edges, workflowId, setCurrentWorkflow, setIsModified, readOnly]);

  // 执行工作流
  const handleExecute = useCallback(async () => {
    if (!currentWorkflow || !workflowId) return;

    try {
      setIsLoading(true);
      const response = await workflowApi.executeWorkflow(workflowId, {});
      
      if (response.success) {
        toast.success('工作流执行已启动');
      } else {
        throw new Error(response.message || '执行失败');
      }
    } catch (error) {
      console.error('执行工作流失败:', error);
      toast.error('执行工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, workflowId]);

  // 获取选中的节点数据
  const selectedNode = useMemo(() => {
    if (!selectedNodeId) return null;
    return nodes.find(node => node.id === selectedNodeId);
  }, [selectedNodeId, nodes]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* 节点面板 */}
      {showNodePalette && !readOnly && (
        <div className="w-64 border-r border-gray-200 bg-white">
          <NodePalette onNodeAdd={handleNodeAdd} />
        </div>
      )}

      {/* 主设计区域 */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
          className="bg-gray-50"
          deleteKeyCode={readOnly ? null : ['Backspace', 'Delete']}
          multiSelectionKeyCode={readOnly ? null : ['Meta', 'Ctrl']}
          panOnDrag={!readOnly}
          zoomOnScroll={!readOnly}
          elementsSelectable={!readOnly}
          nodesConnectable={!readOnly}
          nodesDraggable={!readOnly}
        >
          <Background color="#e5e7eb" gap={20} />
          <Controls showInteractive={!readOnly} />
          <MiniMap 
            nodeColor="#3b82f6"
            maskColor="rgba(0, 0, 0, 0.1)"
            position="bottom-right"
          />
          
          {/* 工具栏 */}
          <Panel position="top-left">
            <WorkflowToolbar
              onSave={handleSave}
              onExecute={handleExecute}
              onToggleNodePalette={() => setShowNodePalette(!showNodePalette)}
              canSave={isModified && !readOnly}
              canExecute={!!workflowId && !readOnly}
              isLoading={isLoading}
              readOnly={readOnly}
            />
          </Panel>

          {/* 状态指示器 */}
          {isModified && !readOnly && (
            <Panel position="top-right">
              <div className="bg-yellow-100 border border-yellow-300 rounded px-3 py-1 text-sm text-yellow-800">
                未保存的更改
              </div>
            </Panel>
          )}
        </ReactFlow>
      </div>

      {/* 节点配置面板 */}
      {showNodeConfig && selectedNode && (
        <div className="w-80 border-l border-gray-200 bg-white">
          <UnifiedNodeConfigPanel
            node={selectedNode}
            onUpdate={handleNodeConfigUpdate}
            onClose={() => {
              setShowNodeConfig(false);
              setSelectedNodeId(null);
            }}
            readOnly={readOnly}
          />
        </div>
      )}
    </div>
  );
};

export const WorkflowDesignerOptimized: React.FC<WorkflowDesignerOptimizedProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowDesignerContent {...props} />
    </ReactFlowProvider>
  );
};
```

### 5.3 节点配置模块

**模块描述**: 统一的节点配置面板，支持动态表单生成和实时配置更新。

**功能职责**:
- 动态表单渲染
- 配置数据验证
- 实时预览更新
- 配置模板管理

**对应代码**:
```typescript
// UnifiedNodeConfigPanel.tsx
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { X, Save, RotateCcw, Eye, EyeOff } from 'lucide-react';
import { Node } from '@xyflow/react';
import { nodeTypeApi } from '../services/api';
import { toast } from 'react-hot-toast';

interface NodeConfigPanelProps {
  node: Node;
  onUpdate: (nodeId: string, config: any) => void;
  onClose: () => void;
  readOnly?: boolean;
}

interface NodeTypeDefinition {
  type: string;
  name: string;
  description: string;
  category: string;
  configurationSchema: Record<string, any>;
  inputEndpoints: Array<{
    id: string;
    name: string;
    type: string;
    required?: boolean;
  }>;
  outputEndpoints: Array<{
    id: string;
    name: string;
    type: string;
  }>;
}

export const UnifiedNodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onUpdate,
  onClose,
  readOnly = false
}) => {
  const [config, setConfig] = useState(node.data.configuration || {});
  const [nodeName, setNodeName] = useState(node.data.name || '');
  const [nodeTypeDefinition, setNodeTypeDefinition] = useState<NodeTypeDefinition | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 加载节点类型定义
  useEffect(() => {
    loadNodeTypeDefinition();
  }, [node.data.type]);

  // 监听配置变化
  useEffect(() => {
    const originalConfig = node.data.configuration || {};
    const originalName = node.data.name || '';
    
    const configChanged = JSON.stringify(config) !== JSON.stringify(originalConfig);
    const nameChanged = nodeName !== originalName;
    
    setHasChanges(configChanged || nameChanged);
  }, [config, nodeName, node.data.configuration, node.data.name]);

  const loadNodeTypeDefinition = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await nodeTypeApi.getNodeType(node.data.type);
      
      if (response.success && response.data) {
        setNodeTypeDefinition(response.data);
      }
    } catch (error) {
      console.error('加载节点类型定义失败:', error);
      toast.error('加载节点配置失败');
    } finally {
      setIsLoading(false);
    }
  }, [node.data.type]);

  // 验证配置
  const validateConfig = useCallback((configData: Record<string, any>) => {
    const newErrors: Record<string, string> = {};
    
    if (!nodeTypeDefinition?.configurationSchema) {
      return newErrors;
    }

    const schema = nodeTypeDefinition.configurationSchema;
    
    Object.entries(schema).forEach(([key, fieldSchema]: [string, any]) => {
      const value = configData[key];
      
      // 必填验证
      if (fieldSchema.required && (value === undefined || value === null || value === '')) {
        newErrors[key] = `${fieldSchema.description || key} 是必填项`;
        return;
      }

      // 类型验证
      if (value !== undefined && value !== null && value !== '') {
        switch (fieldSchema.type) {
          case 'number':
            if (isNaN(Number(value))) {
              newErrors[key] = '必须是数字';
            } else {
              const numValue = Number(value);
              if (fieldSchema.minimum !== undefined && numValue < fieldSchema.minimum) {
                newErrors[key] = `不能小于 ${fieldSchema.minimum}`;
              }
              if (fieldSchema.maximum !== undefined && numValue > fieldSchema.maximum) {
                newErrors[key] = `不能大于 ${fieldSchema.maximum}`;
              }
            }
            break;
          
          case 'string':
            if (typeof value !== 'string') {
              newErrors[key] = '必须是文本';
            } else {
              if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
                newErrors[key] = `长度不能少于 ${fieldSchema.minLength} 个字符`;
              }
              if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
                newErrors[key] = `长度不能超过 ${fieldSchema.maxLength} 个字符`;
              }
              if (fieldSchema.pattern) {
                const regex = new RegExp(fieldSchema.pattern);
                if (!regex.test(value)) {
                  newErrors[key] = fieldSchema.patternMessage || '格式不正确';
                }
              }
            }
            break;
          
          case 'boolean':
            if (typeof value !== 'boolean') {
              newErrors[key] = '必须是布尔值';
            }
            break;
        }

        // 枚举验证
        if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
          newErrors[key] = `必须是以下值之一: ${fieldSchema.enum.join(', ')}`;
        }
      }
    });

    return newErrors;
  }, [nodeTypeDefinition]);

  // 处理配置更新
  const handleConfigChange = useCallback((key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    
    // 实时验证
    const newErrors = validateConfig(newConfig);
    setErrors(newErrors);
  }, [config, validateConfig]);

  // 保存配置
  const handleSave = useCallback(() => {
    if (readOnly) return;

    const validationErrors = validateConfig(config);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      toast.error('请修正配置错误');
      return;
    }

    onUpdate(node.id, {
      name: nodeName,
      configuration: config
    });

    setHasChanges(false);
    toast.success('配置已保存');
  }, [config, nodeName, node.id, onUpdate, validateConfig, readOnly]);

  // 重置配置
  const handleReset = useCallback(() => {
    setConfig(node.data.configuration || {});
    setNodeName(node.data.name || '');
    setErrors({});
    setHasChanges(false);
  }, [node.data.configuration, node.data.name]);

  // 渲染配置字段
  const renderConfigField = useCallback((key: string, fieldSchema: any) => {
    const value = config[key] ?? fieldSchema.default ?? '';
    const error = errors[key];
    const isRequired = fieldSchema.required;

    const baseClasses = `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
      error ? 'border-red-300 bg-red-50' : 'border-gray-300'
    } ${readOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`;

    switch (fieldSchema.type) {
      case 'string':
        if (fieldSchema.enum) {
          return (
            <select
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={baseClasses}
              disabled={readOnly}
            >
              <option value="">请选择...</option>
              {fieldSchema.enum.map((option: string) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          );
        } else if (fieldSchema.multiline) {
          return (
            <textarea
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={`${baseClasses} h-24 resize-vertical`}
              placeholder={fieldSchema.placeholder}
              disabled={readOnly}
            />
          );
        } else {
          return (
            <input
              type="text"
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={baseClasses}
              placeholder={fieldSchema.placeholder}
              disabled={readOnly}
            />
          );
        }

      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => handleConfigChange(key, Number(e.target.value))}
            className={baseClasses}
            min={fieldSchema.minimum}
            max={fieldSchema.maximum}
            step={fieldSchema.step || 1}
            placeholder={fieldSchema.placeholder}
            disabled={readOnly}
          />
        );

      case 'boolean':
        return (
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={Boolean(value)}
              onChange={(e) => handleConfigChange(key, e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={readOnly}
            />
            <span className="text-sm text-gray-700">
              {fieldSchema.label || '启用'}
            </span>
          </label>
        );

      case 'array':
        return (
          <div className="space-y-2">
            {(Array.isArray(value) ? value : []).map((item: any, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newArray = [...(Array.isArray(value) ? value : [])];
                    newArray[index] = e.target.value;
                    handleConfigChange(key, newArray);
                  }}
                  className={baseClasses}
                  disabled={readOnly}
                />
                {!readOnly && (
                  <button
                    onClick={() => {
                      const newArray = [...(Array.isArray(value) ? value : [])];
                      newArray.splice(index, 1);
                      handleConfigChange(key, newArray);
                    }}
                    className="px-2 py-1 text-red-600 hover:bg-red-50 rounded"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
            {!readOnly && (
              <button
                onClick={() => {
                  const newArray = [...(Array.isArray(value) ? value : []), ''];
                  handleConfigChange(key, newArray);
                }}
                className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded border border-blue-300"
              >
                添加项目
              </button>
            )}
          </div>
        );

      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => handleConfigChange(key, e.target.value)}
            className={baseClasses}
            placeholder={fieldSchema.placeholder}
            disabled={readOnly}
          />
        );
    }
  }, [config, errors, handleConfigChange, readOnly]);

  // 配置字段分组
  const configFields = useMemo(() => {
    if (!nodeTypeDefinition?.configurationSchema) {
      return { basic: [], advanced: [] };
    }

    const schema = nodeTypeDefinition.configurationSchema;
    const basic: Array<[string, any]> = [];
    const advanced: Array<[string, any]> = [];

    Object.entries(schema).forEach(([key, fieldSchema]) => {
      if (fieldSchema.advanced) {
        advanced.push([key, fieldSchema]);
      } else {
        basic.push([key, fieldSchema]);
      }
    });

    return { basic, advanced };
  }, [nodeTypeDefinition]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">加载中...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-medium text-gray-900">节点配置</h3>
          <p className="text-sm text-gray-500">
            {nodeTypeDefinition?.name || node.data.type}
          </p>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* 基本信息 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">基本信息</h4>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              节点名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={nodeName}
              onChange={(e) => setNodeName(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100 cursor-not-allowed' : 'border-gray-300'
              }`}
              placeholder="请输入节点名称"
              disabled={readOnly}
            />
          </div>

          {nodeTypeDefinition?.description && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                节点描述
              </label>
              <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                {nodeTypeDefinition.description}
              </p>
            </div>
          )}
        </div>

        {/* 基础配置 */}
        {configFields.basic.length > 0 && (
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900">基础配置</h4>
            
            {configFields.basic.map(([key, fieldSchema]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {fieldSchema.description || key}
                  {fieldSchema.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderConfigField(key, fieldSchema)}
                {errors[key] && (
                  <p className="mt-1 text-sm text-red-600">{errors[key]}</p>
                )}
                {fieldSchema.help && (
                  <p className="mt-1 text-xs text-gray-500">{fieldSchema.help}</p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 高级配置 */}
        {configFields.advanced.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">高级配置</h4>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {showAdvanced ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                <span>{showAdvanced ? '隐藏' : '显示'}</span>
              </button>
            </div>
            
            {showAdvanced && configFields.advanced.map(([key, fieldSchema]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {fieldSchema.description || key}
                  {fieldSchema.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderConfigField(key, fieldSchema)}
                {errors[key] && (
                  <p className="mt-1 text-sm text-red-600">{errors[key]}</p>
                )}
                {fieldSchema.help && (
                  <p className="mt-1 text-xs text-gray-500">{fieldSchema.help}</p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部操作栏 */}
      <div className="flex items-center justify-end p-4 border-t border-gray-200">
        {!readOnly && (
          <button
            onClick={handleReset}
            className="mr-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 rounded"
          >
            重置
          </button>
        )}
        {!readOnly && (
          <button
            onClick={handleSave}
            className={`px-3 py-2 text-sm text-white hover:bg-blue-700 rounded ${
              !hasChanges || isLoading ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-600'
            }`}
            disabled={!hasChanges || isLoading}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>保存中...</span>
              </div>
            ) : (
              '保存'
            )}
          </button>
        )}
      </div>
    </div>
  );
};
```

### 5.4 执行监控模块

**模块描述**: 实时监控工作流执行状态和日志信息。

**功能职责**:
- 显示工作流执行列表
- 查看执行日志
- 监控执行进度
- 支持过滤和排序

**对应代码**:
```typescript
// ExecutionMonitor.tsx
import React, { useState, useEffect } from 'react';
import { workflowApi } from '../services/api';
import { toast } from 'react-hot-toast';
import { PagedResult } from '../models/PagedResult';
import { WorkflowExecution } from '../models/WorkflowExecution';
import { ExecutionFilter } from '../models/ExecutionFilter';
import { ExecutionStatus } from '../models/ExecutionStatus';
import { NodeExecution } from '../models/NodeExecution';
import { NodePosition } from '../models/NodePosition';
import { NodeTypeDefinition } from '../models/NodeTypeDefinition';
import { SystemConfiguration } from '../models/SystemConfiguration';
import { EndpointDefinition } from '../models/EndpointDefinition';
import { CustomNode } from './CustomNode';
import { NodePalette } from './NodePalette';
import { UnifiedNodeConfigPanel } from './UnifiedNodeConfigPanel';
import { WorkflowToolbar } from './WorkflowToolbar';
import { useWorkflowStore } from '../stores/workflowStore';
import { useUIStore } from '../stores/uiStore';
import { workflowApi } from '../services/api';
import { toast } from 'react-hot-toast';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowDesignerOptimizedProps {
  workflowId?: string;
  readOnly?: boolean;
}

const WorkflowDesignerContent: React.FC<WorkflowDesignerOptimizedProps> = ({
  workflowId,
  readOnly = false
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const { 
    currentWorkflow, 
    setCurrentWorkflow, 
    updateWorkflow,
    isModified,
    setIsModified 
  } = useWorkflowStore();
  
  const { 
    showNodeConfig, 
    setShowNodeConfig,
    showNodePalette,
    setShowNodePalette 
  } = useUIStore();

  const { getViewport, setViewport } = useReactFlow();

  // 加载工作流数据
  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const loadWorkflow = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const response = await workflowApi.getWorkflow(id);
      
      if (response.success && response.data) {
        const workflow = response.data;
        setCurrentWorkflow(workflow);
        
        // 转换节点数据
        const flowNodes = workflow.nodes?.map(node => ({
          id: node.id,
          type: 'custom',
          position: node.position || { x: 0, y: 0 },
          data: {
            id: node.id,
            name: node.name,
            type: node.type,
            configuration: node.configuration || {},
            onConfigClick: handleNodeConfigClick,
            onDelete: handleNodeDelete,
            readOnly
          }
        })) || [];

        // 转换连接数据
        const flowEdges = workflow.connections?.map(conn => ({
          id: conn.id,
          source: conn.sourceNodeId,
          target: conn.targetNodeId,
          sourceHandle: conn.sourceEndpointId,
          targetHandle: conn.targetEndpointId,
          type: 'smoothstep',
          animated: false
        })) || [];

        setNodes(flowNodes);
        setEdges(flowEdges);
        setIsModified(false);
      }
    } catch (error) {
      console.error('加载工作流失败:', error);
      toast.error('加载工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [setCurrentWorkflow, setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点变更
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChange(changes);
    
    // 检查是否有位置变更
    const hasPositionChange = changes.some(change => 
      change.type === 'position' && change.dragging === false
    );
    
    if (hasPositionChange) {
      setIsModified(true);
    }
  }, [onNodesChange, setIsModified]);

  // 处理连接变更
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    onEdgesChange(changes);
    setIsModified(true);
  }, [onEdgesChange, setIsModified]);

  // 处理新连接
  const onConnect = useCallback((connection: Connection) => {
    if (readOnly) return;

    const newEdge = {
      ...connection,
      id: `edge-${Date.now()}`,
      type: 'smoothstep',
      animated: false
    };

    setEdges(eds => addEdge(newEdge, eds));
    setIsModified(true);
  }, [setEdges, setIsModified, readOnly]);

  // 处理节点配置点击
  const handleNodeConfigClick = useCallback((nodeId: string) => {
    setSelectedNodeId(nodeId);
    setShowNodeConfig(true);
  }, [setShowNodeConfig]);

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    if (readOnly) return;

    setNodes(nds => nds.filter(node => node.id !== nodeId));
    setEdges(eds => eds.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setIsModified(true);
    toast.success('节点已删除');
  }, [setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string, position: { x: number; y: number }) => {
    if (readOnly) return;

    const newNodeId = `node-${Date.now()}`;
    const newNode = {
      id: newNodeId,
      type: 'custom',
      position,
      data: {
        id: newNodeId,
        name: `${nodeType} 节点`,
        type: nodeType,
        configuration: {},
        onConfigClick: handleNodeConfigClick,
        onDelete: handleNodeDelete,
        readOnly: false
      }
    };

    setNodes(nds => [...nds, newNode]);
    setIsModified(true);
    toast.success('节点已添加');
  }, [setNodes, setIsModified, handleNodeConfigClick, handleNodeDelete, readOnly]);

  // 处理节点配置更新
  const handleNodeConfigUpdate = useCallback((nodeId: string, config: any) => {
    setNodes(nds => nds.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            name: config.name || node.data.name,
            configuration: config.configuration || node.data.configuration
          }
        };
      }
      return node;
    }));
    setIsModified(true);
  }, [setNodes, setIsModified]);

  // 保存工作流
  const handleSave = useCallback(async () => {
    if (!currentWorkflow || readOnly) return;

    try {
      setIsLoading(true);

      // 构建工作流数据
      const workflowData = {
        ...currentWorkflow,
        nodes: nodes.map(node => ({
          id: node.id,
          name: node.data.name,
          type: node.data.type,
          configuration: node.data.configuration,
          position: node.position,
          workflowId: currentWorkflow.id
        })),
        connections: edges.map(edge => ({
          id: edge.id,
          sourceNodeId: edge.source,
          targetNodeId: edge.target,
          sourceEndpointId: edge.sourceHandle || 'output',
          targetEndpointId: edge.targetHandle || 'input',
          workflowId: currentWorkflow.id
        }))
      };

      let response;
      if (workflowId) {
        response = await workflowApi.updateWorkflow(workflowId, workflowData);
      } else {
        response = await workflowApi.createWorkflow(workflowData);
      }

      if (response.success) {
        setCurrentWorkflow(response.data);
        setIsModified(false);
        toast.success('工作流保存成功');
      } else {
        throw new Error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      toast.error('保存工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, nodes, edges, workflowId, setCurrentWorkflow, setIsModified, readOnly]);

  // 执行工作流
  const handleExecute = useCallback(async () => {
    if (!currentWorkflow || !workflowId) return;

    try {
      setIsLoading(true);
      const response = await workflowApi.executeWorkflow(workflowId, {});
      
      if (response.success) {
        toast.success('工作流执行已启动');
      } else {
        throw new Error(response.message || '执行失败');
      }
    } catch (error) {
      console.error('执行工作流失败:', error);
      toast.error('执行工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, workflowId]);

  // 获取选中的节点数据
  const selectedNode =
            entity.Property(e => e.Tags)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.Name).IsUnique();
            entity.HasIndex(e => e.Category);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.CreatedAt);

            // 关系配置
            entity.HasMany(e => e.Nodes)
                .WithOne(n => n.Workflow)
                .HasForeignKey(n => n.WorkflowId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Connections)
                .WithOne(c => c.Workflow)
                .HasForeignKey(c => c.WorkflowId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasMany(e => e.Executions)
                .WithOne(ex => ex.Workflow)
                .HasForeignKey(ex => ex.WorkflowId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // 配置工作流节点
        modelBuilder.Entity<WorkflowNode>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
            
            // JSON列配置
            entity.Property(e => e.Configuration)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.Position)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<NodePosition>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.Type);
        });

        // 配置工作流连接
        modelBuilder.Entity<WorkflowConnection>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SourceEndpointId).HasMaxLength(50);
            entity.Property(e => e.TargetEndpointId).HasMaxLength(50);

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.SourceNodeId);
            entity.HasIndex(e => e.TargetNodeId);

            // 外键约束
            entity.HasOne<WorkflowNode>()
                .WithMany()
                .HasForeignKey(e => e.SourceNodeId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne<WorkflowNode>()
                .WithMany()
                .HasForeignKey(e => e.TargetNodeId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // 配置工作流执行
        modelBuilder.Entity<WorkflowExecution>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.StartedBy).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Error).HasMaxLength(1000);
            
            // JSON列配置
            entity.Property(e => e.Parameters)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.Variables)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.NodeOutputs)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.StartedAt);
            entity.HasIndex(e => e.CompletedAt);

            // 关系配置
            entity.HasMany(e => e.NodeExecutions)
                .WithOne(ne => ne.WorkflowExecution)
                .HasForeignKey(ne => ne.WorkflowExecutionId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置节点执行
        modelBuilder.Entity<NodeExecution>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.NodeId).IsRequired();
            entity.Property(e => e.NodeType).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Error).HasMaxLength(1000);
            
            // JSON列配置
            entity.Property(e => e.InputData)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.OutputData)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.WorkflowExecutionId);
            entity.HasIndex(e => e.NodeId);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.StartedAt);
        });

        // 配置节点类型定义
        modelBuilder.Entity<NodeTypeDefinition>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Icon).HasMaxLength(100);
            entity.Property(e => e.Color).HasMaxLength(20);
            
            // JSON列配置
            entity.Property(e => e.InputEndpoints)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<EndpointDefinition>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.OutputEndpoints)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<List<EndpointDefinition>>(v, (JsonSerializerOptions)null));
            
            entity.Property(e => e.ConfigurationSchema)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions)null));

            // 索引
            entity.HasIndex(e => e.Type).IsUnique();
            entity.HasIndex(e => e.Category);
        });

        // 配置系统配置
        modelBuilder.Entity<SystemConfiguration>(entity =>
        {
            entity.HasKey(e => e.Key);
            entity.Property(e => e.Key).HasMaxLength(100);
            entity.Property(e => e.Value).IsRequired();
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Category).HasMaxLength(50);
            entity.Property(e => e.DataType).HasMaxLength(20);

            // 索引
            entity.HasIndex(e => e.Category);
        });
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // 开发环境默认配置
            optionsBuilder.UseSqlite("Data Source=flowcustom.db");
        }

        // 性能优化配置
        optionsBuilder.EnableSensitiveDataLogging(false);
        optionsBuilder.EnableServiceProviderCaching();
        optionsBuilder.EnableDetailedErrors(false);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // 自动设置审计字段
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;
            
            if (entry.State == EntityState.Added)
            {
                entity.CreatedAt = DateTime.UtcNow;
            }
            
            entity.UpdatedAt = DateTime.UtcNow;
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}

// 数据库初始化服务
public class DatabaseInitializer
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<DatabaseInitializer> _logger;

    public DatabaseInitializer(FlowCustomDbContext context, ILogger<DatabaseInitializer> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // 确保数据库已创建
            await _context.Database.EnsureCreatedAsync();
            
            // 运行待处理的迁移
            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("应用数据库迁移: {Migrations}", string.Join(", ", pendingMigrations));
                await _context.Database.MigrateAsync();
            }

            // 初始化种子数据
            await SeedDataAsync();

            _logger.LogInformation("数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库初始化失败");
            throw;
        }
    }

    private async Task SeedDataAsync()
    {
        // 初始化节点类型定义
        if (!await _context.NodeTypeDefinitions.AnyAsync())
        {
            var nodeTypes = GetBuiltinNodeTypes();
            _context.NodeTypeDefinitions.AddRange(nodeTypes);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("初始化节点类型定义完成，共 {Count} 个类型", nodeTypes.Count);
        }

        // 初始化系统配置
        if (!await _context.SystemConfigurations.AnyAsync())
        {
            var configurations = GetDefaultSystemConfigurations();
            _context.SystemConfigurations.AddRange(configurations);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("初始化系统配置完成，共 {Count} 个配置项", configurations.Count);
        }
    }

    private List<NodeTypeDefinition> GetBuiltinNodeTypes()
    {
        return new List<NodeTypeDefinition>
        {
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "start",
                Name = "开始",
                Description = "工作流开始节点",
                Category = "system",
                Icon = "play-circle",
                Color = "#22c55e",
                InputEndpoints = new List<EndpointDefinition>(),
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "any" }
                },
                ConfigurationSchema = new Dictionary<string, object>()
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "end",
                Name = "结束",
                Description = "工作流结束节点",
                Category = "system",
                Icon = "stop-circle",
                Color = "#ef4444",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>(),
                ConfigurationSchema = new Dictionary<string, object>()
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "log",
                Name = "日志",
                Description = "记录日志信息",
                Category = "system",
                Icon = "file-text",
                Color = "#3b82f6",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "any" }
                },
                ConfigurationSchema = new Dictionary<string, object>
                {
                    ["message"] = new { type = "string", required = true, description = "日志消息" },
                    ["level"] = new { type = "string", enum = new[] { "info", "warn", "error" }, default = "info" }
                }
            },
            new NodeTypeDefinition
            {
                Id = Guid.NewGuid(),
                Type = "loop",
                Name = "循环",
                Description = "循环执行节点",
                Category = "logic",
                Icon = "repeat",
                Color = "#8b5cf6",
                InputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "input", Name = "输入", Type = "any" }
                },
                OutputEndpoints = new List<EndpointDefinition>
                {
                    new EndpointDefinition { Id = "output", Name = "输出", Type = "array" }
                },
                ConfigurationSchema = new Dictionary<string, object>
                {
                    ["loopType"] = new { type = "string", enum = new[] { "count", "array", "condition" }, default = "count" },
                    ["count"] = new { type = "number", minimum = 1, maximum = 1000, default = 5 },
                    ["arrayPath"] = new { type = "string", description = "数组数据路径" },
                    ["condition"] = new { type = "string", description = "循环条件表达式" },
                    ["maxIterations"] = new { type = "number", minimum = 1, maximum = 10000, default = 1000 }
                }
            }
            // 可以继续添加更多内置节点类型...
        };
    }

    private List<SystemConfiguration> GetDefaultSystemConfigurations()
    {
        return new List<SystemConfiguration>
        {
            new SystemConfiguration
            {
                Key = "workflow.execution.timeout",
                Value = "3600",
                Description = "工作流执行超时时间（秒）",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "workflow.execution.maxConcurrency",
                Value = "10",
                Description = "最大并发执行数",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "node.execution.timeout",
                Value = "300",
                Description = "节点执行超时时间（秒）",
                Category = "execution",
                DataType = "number"
            },
            new SystemConfiguration
            {
                Key = "system.log.level",
                Value = "Information",
                Description = "系统日志级别",
                Category = "logging",
                DataType = "string"
            },
            new SystemConfiguration
            {
                Key = "system.log.retention",
                Value = "30",
                Description = "日志保留天数",
                Category = "logging",
                DataType = "number"
            }
        };
    }
}
```

## 📊 5. 前端用户界面软件

**软件描述**: 基于React 18和TypeScript的现代化前端应用，提供直观的工作流设计和管理界面。

### 5.1 功能模块分解
```mermaid
graph TB
    subgraph "前端用户界面软件"
        M1[工作流设计器模块]
        M2[节点配置模块]
        M3[执行监控模块]
        M4[状态管理模块]
        M5[API通信模块]
        M6[UI组件库模块]
    end
    
    M1 --> M2
    M1 --> M4
    M3 --> M4
    M4 --> M5
    M2 --> M6
    M3 --> M6
```

### 5.2 工作流设计器模块

**模块描述**: 基于ReactFlow的可视化工作流设计器，支持节点拖拽、连接和实时编辑。

**功能职责**:
- 工作流画布管理
- 节点拖拽和定位
- 连接线绘制和验证
- 实时预览和保存

**输入接口**:
- 工作流定义数据
- 节点类型定义
- 用户交互事件
- 配置更新事件

**输出接口**:
- 工作流定义更新
- 节点配置请求
- 保存操作触发
- 状态变更通知

**组件层次图**:
```mermaid
graph TB
    subgraph "工作流设计器组件"
        A[WorkflowDesignerOptimized]
        B[CustomNode]
        C[CustomEdge]
        D[NodePalette]
        E[UnifiedNodeConfigPanel]
        F[WorkflowToolbar]
        G[MiniMap]
        H[Controls]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    
    B --> E
    D --> B
```

**对应代码**:
```typescript
// WorkflowDesignerOptimized.tsx
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Connection,
  NodeChange,
  EdgeChange,
  Background,
  Controls,
  MiniMap,
  Panel,
  useReactFlow,
  ReactFlowProvider
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { CustomNode } from './CustomNode';
import { NodePalette } from './NodePalette';
import { UnifiedNodeConfigPanel } from './UnifiedNodeConfigPanel';
import { WorkflowToolbar } from './WorkflowToolbar';
import { useWorkflowStore } from '../stores/workflowStore';
import { useUIStore } from '../stores/uiStore';
import { workflowApi } from '../services/api';
import { toast } from 'react-hot-toast';

const nodeTypes = {
  custom: CustomNode,
};

interface WorkflowDesignerOptimizedProps {
  workflowId?: string;
  readOnly?: boolean;
}

const WorkflowDesignerContent: React.FC<WorkflowDesignerOptimizedProps> = ({
  workflowId,
  readOnly = false
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  const { 
    currentWorkflow, 
    setCurrentWorkflow, 
    updateWorkflow,
    isModified,
    setIsModified 
  } = useWorkflowStore();
  
  const { 
    showNodeConfig, 
    setShowNodeConfig,
    showNodePalette,
    setShowNodePalette 
  } = useUIStore();

  const { getViewport, setViewport } = useReactFlow();

  // 加载工作流数据
  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const loadWorkflow = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const response = await workflowApi.getWorkflow(id);
      
      if (response.success && response.data) {
        const workflow = response.data;
        setCurrentWorkflow(workflow);
        
        // 转换节点数据
        const flowNodes = workflow.nodes?.map(node => ({
          id: node.id,
          type: 'custom',
          position: node.position || { x: 0, y: 0 },
          data: {
            id: node.id,
            name: node.name,
            type: node.type,
            configuration: node.configuration || {},
            onConfigClick: handleNodeConfigClick,
            onDelete: handleNodeDelete,
            readOnly
          }
        })) || [];

        // 转换连接数据
        const flowEdges = workflow.connections?.map(conn => ({
          id: conn.id,
          source: conn.sourceNodeId,
          target: conn.targetNodeId,
          sourceHandle: conn.sourceEndpointId,
          targetHandle: conn.targetEndpointId,
          type: 'smoothstep',
          animated: false
        })) || [];

        setNodes(flowNodes);
        setEdges(flowEdges);
        setIsModified(false);
      }
    } catch (error) {
      console.error('加载工作流失败:', error);
      toast.error('加载工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [setCurrentWorkflow, setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点变更
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    onNodesChange(changes);
    
    // 检查是否有位置变更
    const hasPositionChange = changes.some(change => 
      change.type === 'position' && change.dragging === false
    );
    
    if (hasPositionChange) {
      setIsModified(true);
    }
  }, [onNodesChange, setIsModified]);

  // 处理连接变更
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    onEdgesChange(changes);
    setIsModified(true);
  }, [onEdgesChange, setIsModified]);

  // 处理新连接
  const onConnect = useCallback((connection: Connection) => {
    if (readOnly) return;

    const newEdge = {
      ...connection,
      id: `edge-${Date.now()}`,
      type: 'smoothstep',
      animated: false
    };

    setEdges(eds => addEdge(newEdge, eds));
    setIsModified(true);
  }, [setEdges, setIsModified, readOnly]);

  // 处理节点配置点击
  const handleNodeConfigClick = useCallback((nodeId: string) => {
    setSelectedNodeId(nodeId);
    setShowNodeConfig(true);
  }, [setShowNodeConfig]);

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    if (readOnly) return;

    setNodes(nds => nds.filter(node => node.id !== nodeId));
    setEdges(eds => eds.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setIsModified(true);
    toast.success('节点已删除');
  }, [setNodes, setEdges, setIsModified, readOnly]);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string, position: { x: number; y: number }) => {
    if (readOnly) return;

    const newNodeId = `node-${Date.now()}`;
    const newNode = {
      id: newNodeId,
      type: 'custom',
      position,
      data: {
        id: newNodeId,
        name: `${nodeType} 节点`,
        type: nodeType,
        configuration: {},
        onConfigClick: handleNodeConfigClick,
        onDelete: handleNodeDelete,
        readOnly: false
      }
    };

    setNodes(nds => [...nds, newNode]);
    setIsModified(true);
    toast.success('节点已添加');
  }, [setNodes, setIsModified, handleNodeConfigClick, handleNodeDelete, readOnly]);

  // 处理节点配置更新
  const handleNodeConfigUpdate = useCallback((nodeId: string, config: any) => {
    setNodes(nds => nds.map(node => {
      if (node.id === nodeId) {
        return {
          ...node,
          data: {
            ...node.data,
            name: config.name || node.data.name,
            configuration: config.configuration || node.data.configuration
          }
        };
      }
      return node;
    }));
    setIsModified(true);
  }, [setNodes, setIsModified]);

  // 保存工作流
  const handleSave = useCallback(async () => {
    if (!currentWorkflow || readOnly) return;

    try {
      setIsLoading(true);

      // 构建工作流数据
      const workflowData = {
        ...currentWorkflow,
        nodes: nodes.map(node => ({
          id: node.id,
          name: node.data.name,
          type: node.data.type,
          configuration: node.data.configuration,
          position: node.position,
          workflowId: currentWorkflow.id
        })),
        connections: edges.map(edge => ({
          id: edge.id,
          sourceNodeId: edge.source,
          targetNodeId: edge.target,
          sourceEndpointId: edge.sourceHandle || 'output',
          targetEndpointId: edge.targetHandle || 'input',
          workflowId: currentWorkflow.id
        }))
      };

      let response;
      if (workflowId) {
        response = await workflowApi.updateWorkflow(workflowId, workflowData);
      } else {
        response = await workflowApi.createWorkflow(workflowData);
      }

      if (response.success) {
        setCurrentWorkflow(response.data);
        setIsModified(false);
        toast.success('工作流保存成功');
      } else {
        throw new Error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      toast.error('保存工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, nodes, edges, workflowId, setCurrentWorkflow, setIsModified, readOnly]);

  // 执行工作流
  const handleExecute = useCallback(async () => {
    if (!currentWorkflow || !workflowId) return;

    try {
      setIsLoading(true);
      const response = await workflowApi.executeWorkflow(workflowId, {});
      
      if (response.success) {
        toast.success('工作流执行已启动');
      } else {
        throw new Error(response.message || '执行失败');
      }
    } catch (error) {
      console.error('执行工作流失败:', error);
      toast.error('执行工作流失败');
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkflow, workflowId]);

  // 获取选中的节点数据
  const selectedNode = useMemo(() => {
    if (!selectedNodeId) return null;
    return nodes.find(node => node.id === selectedNodeId);
  }, [selectedNodeId, nodes]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* 节点面板 */}
      {showNodePalette && !readOnly && (
        <div className="w-64 border-r border-gray-200 bg-white">
          <NodePalette onNodeAdd={handleNodeAdd} />
        </div>
      )}

      {/* 主设计区域 */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
          className="bg-gray-50"
          deleteKeyCode={readOnly ? null : ['Backspace', 'Delete']}
          multiSelectionKeyCode={readOnly ? null : ['Meta', 'Ctrl']}
          panOnDrag={!readOnly}
          zoomOnScroll={!readOnly}
          elementsSelectable={!readOnly}
          nodesConnectable={!readOnly}
          nodesDraggable={!readOnly}
        >
          <Background color="#e5e7eb" gap={20} />
          <Controls showInteractive={!readOnly} />
          <MiniMap 
            nodeColor="#3b82f6"
            maskColor="rgba(0, 0, 0, 0.1)"
            position="bottom-right"
          />
          
          {/* 工具栏 */}
          <Panel position="top-left">
            <WorkflowToolbar
              onSave={handleSave}
              onExecute={handleExecute}
              onToggleNodePalette={() => setShowNodePalette(!showNodePalette)}
              canSave={isModified && !readOnly}
              canExecute={!!workflowId && !readOnly}
              isLoading={isLoading}
              readOnly={readOnly}
            />
          </Panel>

          {/* 状态指示器 */}
          {isModified && !readOnly && (
            <Panel position="top-right">
              <div className="bg-yellow-100 border border-yellow-300 rounded px-3 py-1 text-sm text-yellow-800">
                未保存的更改
              </div>
            </Panel>
          )}
        </ReactFlow>
      </div>

      {/* 节点配置面板 */}
      {showNodeConfig && selectedNode && (
        <div className="w-80 border-l border-gray-200 bg-white">
          <UnifiedNodeConfigPanel
            node={selectedNode}
            onUpdate={handleNodeConfigUpdate}
            onClose={() => {
              setShowNodeConfig(false);
              setSelectedNodeId(null);
            }}
            readOnly={readOnly}
          />
        </div>
      )}
    </div>
  );
};

export const WorkflowDesignerOptimized: React.FC<WorkflowDesignerOptimizedProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowDesignerContent {...props} />
    </ReactFlowProvider>
  );
};
```

### 5.3 节点配置模块

**模块描述**: 统一的节点配置面板，支持动态表单生成和实时配置更新。

**功能职责**:
- 动态表单渲染
- 配置数据验证
- 实时预览更新
- 配置模板管理

**对应代码**:
```typescript
// UnifiedNodeConfigPanel.tsx
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { X, Save, RotateCcw, Eye, EyeOff } from 'lucide-react';
import { Node } from '@xyflow/react';
import { nodeTypeApi } from '../services/api';
import { toast } from 'react-hot-toast';

interface NodeConfigPanelProps {
  node: Node;
  onUpdate: (nodeId: string, config: any) => void;
  onClose: () => void;
  readOnly?: boolean;
}

interface NodeTypeDefinition {
  type: string;
  name: string;
  description: string;
  category: string;
  configurationSchema: Record<string, any>;
  inputEndpoints: Array<{
    id: string;
    name: string;
    type: string;
    required?: boolean;
  }>;
  outputEndpoints: Array<{
    id: string;
    name: string;
    type: string;
  }>;
}

export const UnifiedNodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onUpdate,
  onClose,
  readOnly = false
}) => {
  const [config, setConfig] = useState(node.data.configuration || {});
  const [nodeName, setNodeName] = useState(node.data.name || '');
  const [nodeTypeDefinition, setNodeTypeDefinition] = useState<NodeTypeDefinition | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 加载节点类型定义
  useEffect(() => {
    loadNodeTypeDefinition();
  }, [node.data.type]);

  // 监听配置变化
  useEffect(() => {
    const originalConfig = node.data.configuration || {};
    const originalName = node.data.name || '';
    
    const configChanged = JSON.stringify(config) !== JSON.stringify(originalConfig);
    const nameChanged = nodeName !== originalName;
    
    setHasChanges(configChanged || nameChanged);
  }, [config, nodeName, node.data.configuration, node.data.name]);

  const loadNodeTypeDefinition = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await nodeTypeApi.getNodeType(node.data.type);
      
      if (response.success && response.data) {
        setNodeTypeDefinition(response.data);
      }
    } catch (error) {
      console.error('加载节点类型定义失败:', error);
      toast.error('加载节点配置失败');
    } finally {
      setIsLoading(false);
    }
  }, [node.data.type]);

  // 验证配置
  const validateConfig = useCallback((configData: Record<string, any>) => {
    const newErrors: Record<string, string> = {};
    
    if (!nodeTypeDefinition?.configurationSchema) {
      return newErrors;
    }

    const schema = nodeTypeDefinition.configurationSchema;
    
    Object.entries(schema).forEach(([key, fieldSchema]: [string, any]) => {
      const value = configData[key];
      
      // 必填验证
      if (fieldSchema.required && (value === undefined || value === null || value === '')) {
        newErrors[key] = `${fieldSchema.description || key} 是必填项`;
        return;
      }

      // 类型验证
      if (value !== undefined && value !== null && value !== '') {
        switch (fieldSchema.type) {
          case 'number':
            if (isNaN(Number(value))) {
              newErrors[key] = '必须是数字';
            } else {
              const numValue = Number(value);
              if (fieldSchema.minimum !== undefined && numValue < fieldSchema.minimum) {
                newErrors[key] = `不能小于 ${fieldSchema.minimum}`;
              }
              if (fieldSchema.maximum !== undefined && numValue > fieldSchema.maximum) {
                newErrors[key] = `不能大于 ${fieldSchema.maximum}`;
              }
            }
            break;
          
          case 'string':
            if (typeof value !== 'string') {
              newErrors[key] = '必须是文本';
            } else {
              if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
                newErrors[key] = `长度不能少于 ${fieldSchema.minLength} 个字符`;
              }
              if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
                newErrors[key] = `长度不能超过 ${fieldSchema.maxLength} 个字符`;
              }
              if (fieldSchema.pattern) {
                const regex = new RegExp(fieldSchema.pattern);
                if (!regex.test(value)) {
                  newErrors[key] = fieldSchema.patternMessage || '格式不正确';
                }
              }
            }
            break;
          
          case 'boolean':
            if (typeof value !== 'boolean') {
              newErrors[key] = '必须是布尔值';
            }
            break;
        }

        // 枚举验证
        if (fieldSchema.enum && !fieldSchema.enum.includes(value)) {
          newErrors[key] = `必须是以下值之一: ${fieldSchema.enum.join(', ')}`;
        }
      }
    });

    return newErrors;
  }, [nodeTypeDefinition]);

  // 处理配置更新
  const handleConfigChange = useCallback((key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    
    // 实时验证
    const newErrors = validateConfig(newConfig);
    setErrors(newErrors);
  }, [config, validateConfig]);

  // 保存配置
  const handleSave = useCallback(() => {
    if (readOnly) return;

    const validationErrors = validateConfig(config);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      toast.error('请修正配置错误');
      return;
    }

    onUpdate(node.id, {
      name: nodeName,
      configuration: config
    });

    setHasChanges(false);
    toast.success('配置已保存');
  }, [config, nodeName, node.id, onUpdate, validateConfig, readOnly]);

  // 重置配置
  const handleReset = useCallback(() => {
    setConfig(node.data.configuration || {});
    setNodeName(node.data.name || '');
    setErrors({});
    setHasChanges(false);
  }, [node.data.configuration, node.data.name]);

  // 渲染配置字段
  const renderConfigField = useCallback((key: string, fieldSchema: any) => {
    const value = config[key] ?? fieldSchema.default ?? '';
    const error = errors[key];
    const isRequired = fieldSchema.required;

    const baseClasses = `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
      error ? 'border-red-300 bg-red-50' : 'border-gray-300'
    } ${readOnly ? 'bg-gray-100 cursor-not-allowed' : ''}`;

    switch (fieldSchema.type) {
      case 'string':
        if (fieldSchema.enum) {
          return (
            <select
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={baseClasses}
              disabled={readOnly}
            >
              <option value="">请选择...</option>
              {fieldSchema.enum.map((option: string) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          );
        } else if (fieldSchema.multiline) {
          return (
            <textarea
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={`${baseClasses} h-24 resize-vertical`}
              placeholder={fieldSchema.placeholder}
              disabled={readOnly}
            />
          );
        } else {
          return (
            <input
              type="text"
              value={value}
              onChange={(e) => handleConfigChange(key, e.target.value)}
              className={baseClasses}
              placeholder={fieldSchema.placeholder}
              disabled={readOnly}
            />
          );
        }

      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => handleConfigChange(key, Number(e.target.value))}
            className={baseClasses}
            min={fieldSchema.minimum}
            max={fieldSchema.maximum}
            step={fieldSchema.step || 1}
            placeholder={fieldSchema.placeholder}
            disabled={readOnly}
          />
        );

      case 'boolean':
        return (
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={Boolean(value)}
              onChange={(e) => handleConfigChange(key, e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={readOnly}
            />
            <span className="text-sm text-gray-700">
              {fieldSchema.label || '启用'}
            </span>
          </label>
        );

      case 'array':
        return (
          <div className="space-y-2">
            {(Array.isArray(value) ? value : []).map((item: any, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newArray = [...(Array.isArray(value) ? value : [])];
                    newArray[index] = e.target.value;
                    handleConfigChange(key, newArray);
                  }}
                  className={baseClasses}
                  disabled={readOnly}
                />
                {!readOnly && (
                  <button
                    onClick={() => {
                      const newArray = [...(Array.isArray(value) ? value : [])];
                      newArray.splice(index, 1);
                      handleConfigChange(key, newArray);
                    }}
                    className="px-2 py-1 text-red-600 hover:bg-red-50 rounded"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
            {!readOnly && (
              <button
                onClick={() => {
                  const newArray = [...(Array.isArray(value) ? value : []), ''];
                  handleConfigChange(key, newArray);
                }}
                className="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded border border-blue-300"
              >
                添加项目
              </button>
            )}
          </div>
        );

      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => handleConfigChange(key, e.target.value)}
            className={baseClasses}
            placeholder={fieldSchema.placeholder}
            disabled={readOnly}
          />
        );
    }
  }, [config, errors, handleConfigChange, readOnly]);

  // 配置字段分组
  const configFields = useMemo(() => {
    if (!nodeTypeDefinition?.configurationSchema) {
      return { basic: [], advanced: [] };
    }

    const schema = nodeTypeDefinition.configurationSchema;
    const basic: Array<[string, any]> = [];
    const advanced: Array<[string, any]> = [];

    Object.entries(schema).forEach(([key, fieldSchema]) => {
      if (fieldSchema.advanced) {
        advanced.push([key, fieldSchema]);
      } else {
        basic.push([key, fieldSchema]);
      }
    });

    return { basic, advanced };
  }, [nodeTypeDefinition]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">加载中...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-medium text-gray-900">节点配置</h3>
          <p className="text-sm text-gray-500">
            {nodeTypeDefinition?.name || node.data.type}
          </p>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* 基本信息 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">基本信息</h4>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              节点名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={nodeName}
              onChange={(e) => setNodeName(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100 cursor-not-allowed' : 'border-gray-300'
              }`}
              placeholder="请输入节点名称"
              disabled={readOnly}
            />
          </div>

          {nodeTypeDefinition?.description && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                节点描述
              </label>
              <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                {nodeTypeDefinition.description}
              </p>
            </div>
          )}
        </div>

        {/* 基础配置 */}
        {configFields.basic.length > 0 && (
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900">基础配置</h4>
            
            {configFields.basic.map(([key, fieldSchema]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {fieldSchema.description || key}
                  {fieldSchema.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderConfigField(key, fieldSchema)}
                {errors[key] && (
                  <p className="mt-1 text-sm text-red-600">{errors[key]}</p>
                )}
                {fieldSchema.help && (
                  <p className="mt-1 text-xs text-gray-500">{fieldSchema.help}</p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 高级配置 */}
        {configFields.advanced.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">高级配置</h4>
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800"
              >
                {showAdvanced ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                <span>{showAdvanced ? '隐藏' : '显示'}</span>
              </button>
            </div>
            
            {showAdvanced && configFields.advanced.map(([key, fieldSchema]) => (
              <div key={key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {fieldSchema.description || key}
                  {fieldSchema.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderConfigField(key, fieldSchema)}
                {errors[key] && (
                  <p className="mt-1 text-sm text-red-600">{errors[key]}</p>
                )}
                {fieldSchema.help && (
                  <p className="mt-1 text-xs text-gray-500">{fieldSchema.help}</p>
                )}
              </div>
            ))}
          </div>
        )}






