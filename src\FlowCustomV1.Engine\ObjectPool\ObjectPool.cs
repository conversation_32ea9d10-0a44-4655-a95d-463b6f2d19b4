using System;
using System.Collections.Concurrent;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 通用对象池实现
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    public class ObjectPool<T> : IObjectPool<T> where T : class
    {
        private readonly ConcurrentQueue<T> _objects = new();
        private readonly Func<T> _objectFactory;
        private readonly Action<T>? _resetAction;
        private readonly Action<T>? _destroyAction;
        private readonly int _maxSize;
        private volatile int _currentCount;
        private volatile int _totalCreated;
        private readonly ILogger<ObjectPool<T>>? _logger;
        private volatile bool _disposed;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="objectFactory">对象创建工厂</param>
        /// <param name="resetAction">对象重置操作（可选）</param>
        /// <param name="destroyAction">对象销毁操作（可选）</param>
        /// <param name="maxSize">池的最大容量</param>
        /// <param name="logger">日志记录器（可选）</param>
        public ObjectPool(
            Func<T> objectFactory,
            Action<T>? resetAction = null,
            Action<T>? destroyAction = null,
            int maxSize = 100,
            ILogger<ObjectPool<T>>? logger = null)
        {
            _objectFactory = objectFactory ?? throw new ArgumentNullException(nameof(objectFactory));
            _resetAction = resetAction;
            _destroyAction = destroyAction;
            _maxSize = maxSize > 0 ? maxSize : throw new ArgumentOutOfRangeException(nameof(maxSize), "最大容量必须大于0");
            _logger = logger;
        }

        /// <summary>
        /// 当前池中对象数量
        /// </summary>
        public int Count => _currentCount;

        /// <summary>
        /// 池的最大容量
        /// </summary>
        public int MaxSize => _maxSize;

        /// <summary>
        /// 已创建的对象总数
        /// </summary>
        public int TotalCreated => _totalCreated;

        /// <summary>
        /// 从对象池获取对象
        /// </summary>
        /// <returns>对象实例</returns>
        public T Get()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ObjectPool<T>));

            if (_objects.TryDequeue(out var item))
            {
                Interlocked.Decrement(ref _currentCount);
                _logger?.LogDebug("从对象池获取对象，当前池中数量: {Count}", _currentCount);
                return item;
            }

            // 池中没有可用对象，创建新对象
            var newItem = _objectFactory();
            Interlocked.Increment(ref _totalCreated);
            _logger?.LogDebug("创建新对象，总创建数量: {TotalCreated}", _totalCreated);
            return newItem;
        }

        /// <summary>
        /// 将对象返回到对象池
        /// </summary>
        /// <param name="item">要返回的对象</param>
        public void Return(T item)
        {
            if (_disposed || item == null)
                return;

            try
            {
                // 执行重置操作
                _resetAction?.Invoke(item);

                // 检查池是否已满
                if (_currentCount >= _maxSize)
                {
                    // 池已满，销毁对象
                    try
                    {
                        _destroyAction?.Invoke(item);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "销毁对象时发生异常");
                    }
                    _logger?.LogDebug("对象池已满，销毁对象");
                    return;
                }

                // 将对象放回池中
                _objects.Enqueue(item);
                Interlocked.Increment(ref _currentCount);
                _logger?.LogDebug("对象返回到池中，当前池中数量: {Count}", _currentCount);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "返回对象到池中时发生异常");
                // 如果重置失败，尝试销毁对象
                try
                {
                    _destroyAction?.Invoke(item);
                }
                catch (Exception destroyEx)
                {
                    _logger?.LogWarning(destroyEx, "销毁异常对象时发生异常");
                }
            }
        }

        /// <summary>
        /// 清空对象池
        /// </summary>
        public void Clear()
        {
            if (_disposed)
                return;

            var clearedCount = 0;
            while (_objects.TryDequeue(out var item))
            {
                try
                {
                    _destroyAction?.Invoke(item);
                    clearedCount++;
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "清空池时销毁对象发生异常");
                }
            }

            _currentCount = 0;
            _logger?.LogInformation("对象池已清空，销毁了 {Count} 个对象", clearedCount);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            Clear();
            _logger?.LogInformation("对象池已释放，总共创建了 {TotalCreated} 个对象", _totalCreated);
        }
    }
}
