# FlowCustomV1 本地集群调试启动脚本
# 启动1个Master节点和1个Worker节点

param(
    [switch]$Master,
    [switch]$Worker,
    [switch]$Stop,
    [switch]$Status
)

$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-ColorOutput Magenta "🚀 $message"
    Write-ColorOutput Magenta ("=" * 50)
}

# 检查基础设施服务
function Test-Infrastructure {
    Write-Info "检查基础设施服务状态..."
    
    $services = @(
        @{Name="MySQL"; Port=3306; Container="flowcustom-mysql-debug"},
        @{Name="Redis"; Port=6379; Container="flowcustom-redis-debug"},
        @{Name="NATS"; Port=4222; Container="flowcustom-nats-debug"}
    )
    
    foreach ($service in $services) {
        try {
            $status = docker inspect --format='{{.State.Status}}' $service.Container 2>$null
            if ($status -eq "running") {
                Write-Success "$($service.Name) 服务运行正常"
            } else {
                Write-Warning "$($service.Name) 服务状态: $status"
            }
        } catch {
            Write-Error "$($service.Name) 服务未运行"
            return $false
        }
    }
    return $true
}

# 启动Master节点
function Start-Master {
    Write-Header "启动Master节点"
    
    if (-not (Test-Infrastructure)) {
        Write-Error "基础设施服务未就绪，请先启动: docker-compose -f docker-compose.debug.yml up -d mysql redis nats"
        return
    }
    
    Write-Info "启动Master节点 (端口: 5279)..."
    Write-Info "配置文件: appsettings.Development.json"
    Write-Info "按 Ctrl+C 停止服务"
    Write-Info ""
    
    $env:ASPNETCORE_ENVIRONMENT = "Development"
    dotnet run --project src/FlowCustomV1.Cluster -- --urls "http://localhost:5279"
}

# 启动Worker节点
function Start-Worker {
    Write-Header "启动Worker节点"
    
    if (-not (Test-Infrastructure)) {
        Write-Error "基础设施服务未就绪，请先启动: docker-compose -f docker-compose.debug.yml up -d mysql redis nats"
        return
    }
    
    Write-Info "启动Worker节点..."
    Write-Info "配置文件: config/cluster/local-worker.json"
    Write-Info "按 Ctrl+C 停止服务"
    Write-Info ""
    
    $env:ASPNETCORE_ENVIRONMENT = "Production"
    dotnet run --project src/FlowCustomV1.Cluster -- --urls "http://localhost:8080" --configuration "config/cluster/local-worker.json"
}

# 显示状态
function Show-Status {
    Write-Header "FlowCustomV1 本地集群状态"
    
    Write-Info "基础设施服务状态:"
    Test-Infrastructure | Out-Null
    
    Write-Info ""
    Write-Info "集群节点端口:"
    Write-Info "• Master节点: http://localhost:5279"
    Write-Info "• Master API: http://localhost:5279/api/cluster/node"
    Write-Info "• Swagger UI: http://localhost:5279/swagger"
    Write-Info "• Worker节点: http://localhost:8080 (内部通信)"
    
    Write-Info ""
    Write-Info "基础设施访问:"
    Write-Info "• NATS监控: http://localhost:8222"
    Write-Info "• MySQL: localhost:3306 (flowcustom/flowcustom123)"
    Write-Info "• Redis: localhost:6379"
    
    Write-Info ""
    Write-Info "启动命令:"
    Write-Info "• 启动Master: .\scripts\start-local-cluster.ps1 -Master"
    Write-Info "• 启动Worker: .\scripts\start-local-cluster.ps1 -Worker"
    Write-Info "• 查看状态: .\scripts\start-local-cluster.ps1 -Status"
}

# 停止服务
function Stop-Services {
    Write-Header "停止本地集群服务"
    
    Write-Info "停止所有dotnet进程..."
    Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object {
        $_.ProcessName -eq "dotnet" -and $_.MainWindowTitle -like "*FlowCustomV1*"
    } | Stop-Process -Force
    
    Write-Success "本地集群服务已停止"
}

# 主逻辑
try {
    if ($Master) {
        Start-Master
    }
    elseif ($Worker) {
        Start-Worker
    }
    elseif ($Stop) {
        Stop-Services
    }
    else {
        Show-Status
    }
}
catch {
    Write-Error "Execution failed: $($_.Exception.Message)"
    exit 1
}
