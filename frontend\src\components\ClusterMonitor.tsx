import React, { useState, useEffect } from 'react';
import { Activity, Server, Users, Clock, AlertCircle, CheckCircle } from 'lucide-react';

interface NodeInfo {
  nodeId: string;
  displayName: string;
  mode: 'Master' | 'Worker' | 'Standalone';
  status: 'Healthy' | 'Warning' | 'Unhealthy' | 'Offline';
  lastHeartbeat: string;
  startTime: string;
  load: {
    activeTasks: number;
    maxConcurrency: number;
    cpuUsage: number;
    memoryUsage: number;
    loadScore: number;
  };
}

interface ClusterStatus {
  clusterName: string;
  currentNode: NodeInfo;
  nodes: NodeInfo[];
  activeTasks: number;
  queuedTasks: number;
  isHealthy: boolean;
  lastUpdated: string;
}

export const ClusterMonitor: React.FC = () => {
  const [clusterStatus, setClusterStatus] = useState<ClusterStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchClusterStatus = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/cluster/status`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      setClusterStatus(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取集群状态失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClusterStatus();
    const interval = setInterval(fetchClusterStatus, 5000); // 每5秒刷新
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'Warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'Unhealthy':
      case 'Offline':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Unhealthy':
      case 'Offline':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatUptime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diff = now.getTime() - start.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm border">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">加载集群状态...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm border">
        <div className="flex items-center text-red-600">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>集群状态获取失败: {error}</span>
        </div>
        <button
          onClick={fetchClusterStatus}
          className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  if (!clusterStatus) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm border">
        <div className="text-gray-600">暂无集群状态数据</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 集群概览 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Server className="w-5 h-5 mr-2" />
            集群状态监控
          </h2>
          <div className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(clusterStatus.isHealthy ? 'Healthy' : 'Unhealthy')}`}>
            {getStatusIcon(clusterStatus.isHealthy ? 'Healthy' : 'Unhealthy')}
            <span className="ml-1">{clusterStatus.isHealthy ? '健康' : '异常'}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm text-gray-600">集群节点</p>
                <p className="text-2xl font-bold text-blue-600">{clusterStatus.nodes.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Activity className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm text-gray-600">活跃任务</p>
                <p className="text-2xl font-bold text-green-600">{clusterStatus.activeTasks}</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div className="ml-3">
                <p className="text-sm text-gray-600">队列任务</p>
                <p className="text-2xl font-bold text-yellow-600">{clusterStatus.queuedTasks}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center">
              <Server className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm text-gray-600">集群名称</p>
                <p className="text-sm font-medium text-purple-600">{clusterStatus.clusterName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 节点列表 */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">节点详情</h3>
        <div className="space-y-4">
          {clusterStatus.nodes.map((node) => (
            <div key={node.nodeId} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  {getStatusIcon(node.status)}
                  <div className="ml-3">
                    <h4 className="font-medium text-gray-900">{node.displayName}</h4>
                    <p className="text-sm text-gray-600">{node.nodeId}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    node.mode === 'Master' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {node.mode}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs ${getStatusColor(node.status)}`}>
                    {node.status}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">活跃任务</p>
                  <p className="font-medium">{node.load.activeTasks}/{node.load.maxConcurrency}</p>
                </div>
                <div>
                  <p className="text-gray-600">负载评分</p>
                  <p className="font-medium">{(node.load.loadScore * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <p className="text-gray-600">运行时间</p>
                  <p className="font-medium">{formatUptime(node.startTime)}</p>
                </div>
                <div>
                  <p className="text-gray-600">最后心跳</p>
                  <p className="font-medium">{new Date(node.lastHeartbeat).toLocaleTimeString()}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 更新时间 */}
      <div className="text-center text-sm text-gray-500">
        最后更新: {new Date(clusterStatus.lastUpdated).toLocaleString()}
      </div>
    </div>
  );
};
