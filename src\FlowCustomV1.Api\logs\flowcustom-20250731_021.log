[2025-07-31 19:21:35.428 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 19:21:35.468 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 19:21:35.472 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 19:21:35.478 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 19:21:35.481 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 19:21:35.489 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 19:21:35.491 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 19:21:35.493 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 19:21:35.495 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 19:21:35.497 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 19:21:35.499 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 19:21:35.503 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 19:21:35.505 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 19:21:35.507 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 19:21:35.508 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 19:21:35.523 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 19:21:35.527 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 19:21:35.532 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 19:21:35.536 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 19:21:35.537 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 19:21:35.540 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 19:21:35.545 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 19:21:35.547 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 19:21:35.550 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 19:21:35.553 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 19:21:35.555 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 19:21:35.557 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,313,976 字节
[2025-07-31 19:21:35.561 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 19:21:35.566 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Worker", NodeId: worker-001
[2025-07-31 19:21:35.570 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Worker", 节点ID: worker-001
[2025-07-31 19:21:35.574 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 1/10)
[2025-07-31 19:21:36.285 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:21:36.289 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:21:36.292 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:21:36.295 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:21:36.298 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:21:36.539 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 19:21:36.542 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 19:21:36.587 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 2/10)
[2025-07-31 19:21:36.636 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 19:21:36.639 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 19:21:36.642 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 19:21:36.778 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:21:36.796 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:21:36.821 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 19:21:36.824 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 19:21:36.995 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 19:21:37.005 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 19:21:37.011 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 19:21:37.013 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 19:21:37.016 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 19:21:37.020 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 19:21:37.136 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 19:21:37.138 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 19:21:37.144 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 19:21:37.146 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 19:21:37.149 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 19:21:37.152 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 19:21:37.156 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 19:21:37.159 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 19:21:37.166 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 19:21:37.171 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 19:21:37.176 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 19:21:37.180 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 19:21:37.184 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 19:21:37.189 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 19:21:37.194 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 19:21:37.198 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 19:21:37.204 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 19:21:37.210 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 19:21:37.235 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 19:21:37.259 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 19:21:37.273 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 19:21:37.279 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 19:21:37.283 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 19:21:37.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 19:21:37.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 19:21:37.303 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 19:21:37.308 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 19:21:37.312 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 19:21:37.316 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 19:21:37.321 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 19:21:37.324 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 19:21:37.327 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 19:21:37.337 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 19:21:37.344 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 19:21:37.347 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 19:21:37.351 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 19:21:37.354 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:21:37.357 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:21:37.360 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 19:21:37.363 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 19:21:37.366 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 19:21:37.377 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 19:21:37.381 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 19:21:37.384 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 19:21:37.395 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:21:37.599 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:21:37.603 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.notification
[2025-07-31 19:21:37.603 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:21:37.606 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.heartbeat
[2025-07-31 19:21:37.609 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:21:37.612 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.events
[2025-07-31 19:21:37.615 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:21:37.618 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ NATS订阅初始化成功
[2025-07-31 19:21:37.622 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 19:21:37.627 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 19:21:37.636 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 19:21:37.639 +08:00 INF] Program: 后台初始化完成
[2025-07-31 19:21:37.659 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:21:37.660 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: worker-001
[2025-07-31 19:21:37.666 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 19:21:37.672 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 19:21:37.676 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 19:21:37.738 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 19:21:37.741 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 19:21:40.561 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 19:21:45.540 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 19:21:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:21:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:22:05.491 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 19:22:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:22:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:22:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:22:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:22:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:22:37.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:22:44.086 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔍 集群状态查询 - 节点字典大小: 1, 节点列表: [master-001]
[2025-07-31 19:22:44.104 +08:00 INF] Program: 请求完成: GET /api/cluster/health - 200 - 31ms
[2025-07-31 19:22:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:22:50.378 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:23:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:23:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:23:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:23:20.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:23:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:23:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:23:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:23:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:24:07.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:24:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:24:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:24:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:24:37.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:24:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:24:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:24:50.376 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:25:07.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:25:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:25:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:25:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:25:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:25:37.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:25:50.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:25:50.382 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:26:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:26:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:26:20.372 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:26:20.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:26:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:26:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:26:50.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:26:50.375 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:27:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:27:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:27:20.379 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:27:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:27:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:27:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:27:50.368 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:27:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:28:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:28:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:28:20.382 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:28:20.384 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:28:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:28:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:28:50.367 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:28:50.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:29:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:29:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:29:20.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:29:20.384 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:29:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:29:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:29:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:29:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:30:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:30:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:30:20.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:30:20.373 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:30:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:30:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:30:50.370 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:30:50.374 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:31:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:31:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:31:20.381 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:31:20.384 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:31:37.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:31:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:31:50.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:31:50.382 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:32:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:32:07.622 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:32:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:32:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:32:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:32:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:32:50.377 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:32:50.380 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:33:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:33:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:33:20.369 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:33:20.371 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master", 字典大小: 1
[2025-07-31 19:33:35.425 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 19:33:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:33:37.633 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 19:33:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:33:37.822 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:33:37.824 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:33:41.868 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:33:41.872 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 19:33:46.876 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 19:33:50.986 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:33:51.397 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 19:34:01.661 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 19:34:05.424 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 19:34:05.742 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 3/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:34:05.751 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 15000ms 后尝试重连
[2025-07-31 19:34:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:34:07.630 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 19:34:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:34:11.880 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:34:11.883 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 4/10)
[2025-07-31 19:34:13.939 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:34:13.941 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:34:13.943 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:34:13.946 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:34:20.753 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:34:20.764 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:34:20.765 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:34:20.767 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:34:20.768 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:34:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:34:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:35:07.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:35:07.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:35:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:35:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:36:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:36:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:36:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:36:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:37:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:37:07.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:37:37.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:37:37.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:38:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:38:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:38:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:38:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:38:43.997 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 19:38:43.999 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:38:44.006 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not start to connect nats server: nats://localhost:4222
 ---> NATS.Client.Core.Internal.SocketClosedException: Socket has been closed.
   at NATS.Client.Core.Internal.SocketReader.ReadAtLeastAsync(Int32 minimumSize)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at NATS.Client.Core.Internal.NatsReadProtocolProcessor.ReadLoopAsync()
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   --- End of inner exception stack trace ---
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:38:44.014 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 19:38:49.025 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 19:38:53.075 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:38:53.084 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 19:39:03.096 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 19:39:03.606 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:39:03.607 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:39:03.609 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:39:03.611 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:39:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:39:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:39:37.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:39:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:40:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:40:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:40:37.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:40:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:41:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:41:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:41:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:41:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:42:07.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:42:07.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:42:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:42:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:43:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:43:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:43:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:43:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:44:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:44:07.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:44:37.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:44:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:45:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:45:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:45:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:45:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:46:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:46:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:46:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:46:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:47:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:47:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:47:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:47:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:48:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:48:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:48:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:48:37.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:49:07.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:49:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:49:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:49:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:50:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:50:07.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:50:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:50:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:51:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:51:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:51:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:51:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:52:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:52:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:52:37.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:52:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:53:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:53:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:53:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:53:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:54:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:54:07.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:54:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:54:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:55:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:55:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:55:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:55:37.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:56:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:56:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:56:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:56:37.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:57:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:57:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:57:37.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:57:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:58:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:58:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:58:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:58:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:59:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:59:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:59:37.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:59:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:00:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:00:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:00:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:00:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:01:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:01:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:01:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:01:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:02:07.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:02:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:02:37.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:02:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:03:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:03:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:03:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:03:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:04:07.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:04:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:04:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:04:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:05:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:05:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:05:37.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:05:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:06:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:06:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:06:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:06:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:07:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:07:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:07:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:07:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:08:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:08:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:08:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:08:37.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:09:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:09:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:09:37.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:09:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:10:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:10:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:10:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:10:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:11:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:11:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:11:37.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:11:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:12:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:12:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:12:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:12:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:13:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:13:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:13:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:13:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:14:07.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:14:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:14:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:14:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:15:07.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:15:07.639 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:15:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:15:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:16:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:16:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:16:37.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:16:37.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:17:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:17:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:17:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:17:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:18:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:18:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:18:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:18:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:19:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:19:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:19:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:19:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:20:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:20:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:20:37.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:20:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:21:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:21:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:21:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:21:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:22:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:22:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:22:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:22:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:23:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:23:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:23:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:23:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:24:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:24:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:24:37.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:24:37.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:25:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:25:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:25:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:25:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:26:07.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:26:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:26:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:26:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:27:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:27:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:27:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:27:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:28:07.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:28:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:28:37.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:28:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:29:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:29:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:29:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:29:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:30:07.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:30:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:30:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:30:37.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:31:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:31:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:31:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:31:37.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:32:07.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:32:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:32:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:32:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:33:07.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:33:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:33:37.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:33:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:34:07.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:34:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:34:37.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:34:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:35:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:35:07.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:35:37.289 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:35:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:36:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:36:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:36:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:36:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:37:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:37:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:37:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:37:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:38:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:38:07.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:38:37.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:38:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:39:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:39:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:39:37.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:39:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:40:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:40:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:40:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:40:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:41:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:41:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:41:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:41:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:42:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:42:07.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:42:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:42:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:43:07.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:43:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:43:37.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:43:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:44:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:44:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:44:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:44:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:45:07.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:45:07.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:45:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:45:37.638 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:46:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:46:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:46:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:46:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:47:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:47:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:47:37.293 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:47:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:48:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:48:07.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:48:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:48:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:49:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:49:07.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:49:37.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:49:37.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:50:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:50:07.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:50:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:50:37.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:51:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:51:07.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:51:15.214 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 20:51:15.215 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 20:51:19.281 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 20:51:19.287 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 20:51:24.285 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 20:51:28.340 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 20:51:28.343 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 20:51:35.421 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 20:51:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:51:37.635 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 20:51:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:51:38.346 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 20:51:38.355 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 20:51:38.356 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 20:51:38.358 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 20:51:38.360 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 20:52:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:52:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:52:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:52:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:53:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:53:07.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:53:37.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:53:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:54:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:54:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:54:37.302 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:54:37.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:55:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:55:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:55:37.299 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:55:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:56:07.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:56:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:56:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:56:37.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:57:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:57:07.637 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:57:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:57:37.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:58:07.303 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:58:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:58:37.288 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:58:37.625 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:59:07.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:59:07.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 20:59:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 20:59:37.624 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:00:07.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:00:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:00:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:00:37.633 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:01:07.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:01:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:01:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:01:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:02:07.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:02:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:02:37.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:02:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:03:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:03:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:03:37.288 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:03:37.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:04:07.304 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:04:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:04:37.301 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:04:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:05:07.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:05:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:05:37.298 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:05:37.628 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:06:07.305 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:06:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:06:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:06:37.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:07:07.292 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:07:07.631 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:07:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:07:37.634 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:08:07.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:08:07.635 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:08:37.290 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:08:37.623 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:09:07.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:09:07.629 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:09:37.296 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:09:37.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:10:07.288 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:10:07.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:10:37.300 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:10:37.636 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:11:07.297 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:11:07.632 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:11:19.590 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:11:19.592 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 21:11:23.632 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:23.638 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 5000ms 后尝试重连
[2025-07-31 21:11:28.650 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 2/10)
[2025-07-31 21:11:32.721 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:32.725 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 10000ms 后尝试重连
[2025-07-31 21:11:35.426 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:11:37.295 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:11:37.628 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:11:37.630 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:11:42.740 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 3/10)
[2025-07-31 21:11:46.816 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 3/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:46.819 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 15000ms 后尝试重连
[2025-07-31 21:11:53.645 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:11:53.650 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 4/10)
[2025-07-31 21:11:57.720 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 4/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:57.729 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 20000ms 后尝试重连
[2025-07-31 21:12:05.432 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:12:07.294 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:12:07.624 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:12:07.626 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 21:12:17.729 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 5/10)
[2025-07-31 21:12:21.803 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 5/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:21.809 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 25000ms 后尝试重连
[2025-07-31 21:12:27.726 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: 🔍 检测到NATS连接断开，启动自动重连
[2025-07-31 21:12:27.731 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 6/10)
[2025-07-31 21:12:31.788 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 6/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:31.798 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ⏰ 将在 30000ms 后尝试重连
[2025-07-31 21:12:35.428 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 21:12:37.291 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 21:12:37.625 +08:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: cluster.heartbeat
[2025-07-31 21:12:37.627 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
