@echo off
echo ========================================
echo FlowCustomV1 Master-Worker Cluster
echo ========================================
echo.

echo Step 1: Check infrastructure services...
docker ps --filter "name=flowcustom-mysql-debug" --filter "name=flowcustom-redis-debug" --filter "name=flowcustom-nats-debug" --format "table {{.Names}}\t{{.Status}}"

echo.
echo Step 2: Start infrastructure if needed...
docker-compose -f docker-compose.debug.yml up -d mysql redis nats

echo.
echo Step 3: Wait for services to be ready...
timeout /t 10 /nobreak >nul

echo.
echo Step 4: Infrastructure is ready!
echo - MySQL: localhost:3306
echo - Redis: localhost:6379  
echo - NATS: localhost:4222 (WebSocket: 8081)
echo.

echo ========================================
echo Ready to start cluster nodes:
echo ========================================
echo 1. Master Node: .\start-master-simple.cmd
echo 2. Worker Node: .\start-worker-simple.cmd  
echo 3. Frontend:    cd frontend && npm run dev
echo.
echo API Endpoints:
echo - Master API: http://localhost:5279
echo - Swagger UI: http://localhost:5279/swagger
echo - Cluster Status: http://localhost:5279/api/cluster/status
echo - Worker Health: http://localhost:8080/api/cluster/health
echo.
pause
