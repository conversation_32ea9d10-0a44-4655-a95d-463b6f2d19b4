[2025-07-31 19:11:34.610 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 19:11:34.674 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 19:11:34.679 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 19:11:34.691 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 19:11:34.693 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 19:11:34.696 +08:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 19:11:34.699 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 19:11:34.701 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 19:11:34.703 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 19:11:34.706 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 19:11:34.708 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 19:11:34.713 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 19:11:34.715 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 19:11:34.718 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 19:11:34.720 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 19:11:34.743 +08:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 19:11:34.761 +08:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00%-50.00%-80.00%], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 19:11:34.763 +08:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 19:11:34.767 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 19:11:34.775 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 19:11:34.777 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: SQLite
[2025-07-31 19:11:34.780 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 19:11:34.785 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 19:11:34.788 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 19:11:34.790 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,315,032 字节
[2025-07-31 19:11:34.790 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已存在
[2025-07-31 19:11:34.793 +08:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 19:11:34.794 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 19:11:34.797 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: true, NodeMode: "Worker", NodeId: worker-001
[2025-07-31 19:11:34.800 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🚀 启动集群服务 - 节点模式: "Worker", 节点ID: worker-001
[2025-07-31 19:11:34.804 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 1/10)
[2025-07-31 19:11:35.301 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:11:35.305 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:11:35.309 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:11:35.312 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:11:35.316 +08:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 19:11:35.500 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 19:11:35.503 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 19:11:35.559 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 1 个
[2025-07-31 19:11:35.561 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 应用待处理的迁移...
[2025-07-31 19:11:35.562 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService:   - 应用迁移: 20250728134138_InitialMySqlMigration
[2025-07-31 19:11:35.658 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:11:35.671 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:11:35.688 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 备用方案执行成功
[2025-07-31 19:11:35.691 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 19:11:35.801 +08:00 WRN] FlowCustomV1.Api.Services.ClusterService: ⏳ NATS未连接，等待连接... (尝试 2/10)
[2025-07-31 19:11:35.860 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 19:11:35.866 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 19:11:35.870 +08:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: ParameterTemplates, TemplateInstances
[2025-07-31 19:11:35.872 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 19:11:35.875 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 19:11:35.877 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 19:11:35.947 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 基础数据检查完成
[2025-07-31 19:11:35.948 +08:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 19:11:35.950 +08:00 INF] Program: 获取插件管理器服务...
[2025-07-31 19:11:35.951 +08:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 19:11:35.953 +08:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 19:11:35.955 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 19:11:35.956 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 19:11:35.958 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 19:11:35.962 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 19:11:35.964 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 19:11:35.966 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 19:11:35.968 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 19:11:35.971 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 19:11:35.973 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 19:11:35.976 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 19:11:35.978 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 19:11:35.980 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 19:11:35.983 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 19:11:35.994 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 19:11:35.997 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 19:11:35.999 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 19:11:36.001 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 19:11:36.002 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 19:11:36.005 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 19:11:36.007 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 19:11:36.009 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 19:11:36.010 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 19:11:36.012 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 19:11:36.013 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 19:11:36.015 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 19:11:36.016 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 19:11:36.018 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 19:11:36.022 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 19:11:36.025 +08:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 19:11:36.027 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 19:11:36.028 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 19:11:36.030 +08:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:11:36.031 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 19:11:36.032 +08:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 19:11:36.033 +08:00 INF] Program: 插件管理器初始化完成
[2025-07-31 19:11:36.034 +08:00 INF] Program: 开始初始化协议模板...
[2025-07-31 19:11:36.038 +08:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 19:11:36.040 +08:00 INF] Program: 协议模板初始化完成
[2025-07-31 19:11:36.041 +08:00 INF] Program: 开始启动NATS服务...
[2025-07-31 19:11:36.044 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://localhost:4222 (尝试 1/10)
[2025-07-31 19:11:36.157 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 19:11:36.162 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 19:11:36.166 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 19:11:36.176 +08:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 19:11:36.182 +08:00 INF] Program: NATS服务启动完成
[2025-07-31 19:11:36.184 +08:00 INF] Program: 配置NATS日志发布...
[2025-07-31 19:11:36.193 +08:00 INF] Program: NATS日志发布配置完成
[2025-07-31 19:11:36.195 +08:00 INF] Program: 后台初始化完成
[2025-07-31 19:11:36.811 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.notification
[2025-07-31 19:11:36.813 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.heartbeat
[2025-07-31 19:11:36.815 +08:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: cluster.events
[2025-07-31 19:11:36.817 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ NATS订阅初始化成功
[2025-07-31 19:11:36.838 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:11:36.839 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 节点注册成功: worker-001
[2025-07-31 19:11:36.842 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: ✅ 集群服务启动成功
[2025-07-31 19:11:36.846 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 19:11:36.849 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 19:11:36.885 +08:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 19:11:36.887 +08:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 19:11:39.776 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 19:11:44.779 +08:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 19:11:53.784 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:11:53.796 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master"
[2025-07-31 19:12:04.703 +08:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 19:12:06.012 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:12:06.819 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:12:23.775 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:12:23.779 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master"
[2025-07-31 19:12:36.018 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:12:36.816 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
[2025-07-31 19:12:53.776 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 💓 收到心跳: master-001 <- cluster.heartbeat
[2025-07-31 19:12:53.779 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔄 更新节点信息: master-001, 模式: "Master"
[2025-07-31 19:13:06.017 +08:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 19:13:06.815 +08:00 INF] FlowCustomV1.Api.Services.ClusterService: 📡 发送心跳: worker-001 -> cluster.heartbeat
