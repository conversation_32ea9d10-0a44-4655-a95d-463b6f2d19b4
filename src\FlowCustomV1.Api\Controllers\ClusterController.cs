using FlowCustomV1.Api.Services;
using FlowCustomV1.Api.Configuration;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 集群管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ClusterController : ControllerBase
{
    private readonly ILogger<ClusterController> _logger;
    private readonly IClusterService _clusterService;
    private readonly ClusterConfiguration _clusterConfig;

    public ClusterController(
        ILogger<ClusterController> logger,
        IClusterService clusterService,
        IOptions<ClusterConfiguration> clusterConfig)
    {
        _logger = logger;
        _clusterService = clusterService;
        _clusterConfig = clusterConfig.Value;
    }

    /// <summary>
    /// 获取集群状态
    /// </summary>
    [HttpGet("status")]
    public async Task<ActionResult<ClusterStatus>> GetClusterStatus()
    {
        try
        {
            var status = await _clusterService.GetClusterStatusAsync();
            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取集群状态失败");
            return StatusCode(500, new { error = "获取集群状态失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取当前节点信息
    /// </summary>
    [HttpGet("node")]
    public ActionResult<object> GetCurrentNode()
    {
        try
        {
            return Ok(new
            {
                nodeId = _clusterConfig.NodeId,
                displayName = _clusterConfig.NodeDisplayName,
                clusterName = _clusterConfig.ClusterName,
                nodeMode = _clusterService.NodeMode.ToString(),
                isClusterEnabled = _clusterService.IsClusterEnabled,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取节点信息失败");
            return StatusCode(500, new { error = "获取节点信息失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 分发工作流任务（仅Master节点）
    /// </summary>
    [HttpPost("dispatch-task")]
    public async Task<ActionResult<object>> DispatchWorkflowTask([FromBody] WorkflowTaskRequest request)
    {
        if (_clusterService.NodeMode != NodeMode.Master)
        {
            return BadRequest(new { error = "只有Master节点可以分发任务" });
        }

        try
        {
            var taskId = await _clusterService.DispatchWorkflowTaskAsync(request);
            return Ok(new { taskId, message = "任务分发成功", timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分发工作流任务失败: {TaskId}", request.TaskId);
            return StatusCode(500, new { error = "任务分发失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 获取集群配置
    /// </summary>
    [HttpGet("config")]
    public ActionResult<object> GetClusterConfig()
    {
        try
        {
            return Ok(new
            {
                enableClusterMode = _clusterConfig.EnableClusterMode,
                nodeMode = _clusterConfig.NodeMode.ToString(),
                nodeId = _clusterConfig.NodeId,
                displayName = _clusterConfig.NodeDisplayName,
                clusterName = _clusterConfig.ClusterName,
                master = _clusterConfig.NodeMode == NodeMode.Master ? new
                {
                    apiPort = _clusterConfig.Master.ApiPort,
                    enableSwagger = _clusterConfig.Master.EnableSwagger,
                    enableCors = _clusterConfig.Master.EnableCors,
                    corsOrigins = _clusterConfig.Master.CorsOrigins
                } : null,
                worker = _clusterConfig.NodeMode == NodeMode.Worker ? new
                {
                    maxConcurrentExecutions = _clusterConfig.Worker.MaxConcurrentExecutions,
                    workerType = _clusterConfig.Worker.WorkerType,
                    supportedNodeTypes = _clusterConfig.Worker.SupportedNodeTypes,
                    healthCheckInterval = _clusterConfig.Worker.HealthCheckInterval,
                    taskTimeout = _clusterConfig.Worker.TaskTimeout
                } : null,
                communication = new
                {
                    connectionString = _clusterConfig.Communication.ConnectionString,
                    connectionName = _clusterConfig.Communication.ConnectionName,
                    heartbeatInterval = _clusterConfig.Communication.HeartbeatInterval,
                    connectTimeout = _clusterConfig.Communication.ConnectTimeout
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取集群配置失败");
            return StatusCode(500, new { error = "获取集群配置失败", message = ex.Message });
        }
    }

    /// <summary>
    /// 健康检查端点
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult<object>> HealthCheck()
    {
        try
        {
            var status = await _clusterService.GetClusterStatusAsync();
            var isHealthy = status.IsHealthy;

            return Ok(new
            {
                status = isHealthy ? "healthy" : "unhealthy",
                nodeId = _clusterConfig.NodeId,
                nodeMode = _clusterService.NodeMode.ToString(),
                clusterEnabled = _clusterService.IsClusterEnabled,
                activeNodes = status.Nodes.Count(n => n.Status == NodeHealthStatus.Healthy),
                totalNodes = status.Nodes.Count,
                activeTasks = status.ActiveTasks,
                queuedTasks = status.QueuedTasks,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查失败");
            return StatusCode(503, new 
            { 
                status = "unhealthy", 
                error = "健康检查失败", 
                message = ex.Message,
                timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取NATS主题信息
    /// </summary>
    [HttpGet("nats-subjects")]
    public ActionResult<object> GetNatsSubjects()
    {
        return Ok(new
        {
            workflowTasks = "workflow.tasks",
            workflowStatus = "workflow.status",
            nodeStatus = "node.status",
            clusterHeartbeat = "cluster.heartbeat",
            clusterEvents = "cluster.events",
            systemNotifications = "system.notifications"
        });
    }

    /// <summary>
    /// 测试工作流任务分发（开发调试用）
    /// </summary>
    [HttpPost("test/dispatch")]
    public async Task<ActionResult<object>> TestDispatchTask()
    {
        if (_clusterService.NodeMode != NodeMode.Master)
        {
            return BadRequest(new { error = "只有Master节点可以分发任务" });
        }

        try
        {
            var testRequest = new WorkflowTaskRequest
            {
                TaskId = Guid.NewGuid().ToString(),
                WorkflowId = "test-workflow-001",
                ExecutionId = Guid.NewGuid().ToString(),
                WorkflowDefinition = """
                {
                    "id": "test-workflow-001",
                    "name": "测试工作流",
                    "nodes": [
                        {
                            "id": "start",
                            "type": "start",
                            "name": "开始",
                            "position": { "x": 100, "y": 100 }
                        },
                        {
                            "id": "script1",
                            "type": "script",
                            "name": "脚本节点",
                            "position": { "x": 300, "y": 100 },
                            "config": {
                                "script": "console.log('Hello from cluster!');"
                            }
                        },
                        {
                            "id": "end",
                            "type": "end",
                            "name": "结束",
                            "position": { "x": 500, "y": 100 }
                        }
                    ],
                    "connections": [
                        { "source": "start", "target": "script1" },
                        { "source": "script1", "target": "end" }
                    ]
                }
                """,
                InputData = new Dictionary<string, object>
                {
                    ["testParam"] = "Hello Cluster!"
                },
                Priority = 1,
                TriggeredBy = "test-api"
            };

            var taskId = await _clusterService.DispatchWorkflowTaskAsync(testRequest);
            
            return Ok(new 
            { 
                taskId, 
                executionId = testRequest.ExecutionId,
                message = "测试任务分发成功", 
                timestamp = DateTime.UtcNow 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试任务分发失败");
            return StatusCode(500, new { error = "测试任务分发失败", message = ex.Message });
        }
    }
}
