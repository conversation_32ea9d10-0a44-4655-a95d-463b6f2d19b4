{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "FlowCustomV1.Cluster": "Debug"}}, "NodeConfiguration": {"NodeId": "test-master-1", "DisplayName": "测试Master节点1", "ClusterName": "flowcustom-test-cluster", "EnableMasterRole": true, "EnableWorkerRole": true, "Description": "测试用的Master+Worker混合节点", "Labels": {"environment": "test", "region": "local", "version": "v0.9.8"}, "Master": {"ApiPort": 5279, "EnableSwagger": true, "EnableCors": true, "CorsOrigins": ["*"], "TriggerManagement": {"EnableTimerTriggers": true, "EnableWebhookTriggers": true, "EnableManualTriggers": true, "MaxConcurrentTriggers": 50}, "Scheduler": {"LoadBalancingStrategy": "weighted_round_robin", "TaskTimeout": "00:30:00", "MaxRetryAttempts": 3, "RetryDelay": "00:00:05"}}, "Worker": {"MaxConcurrentExecutions": 8, "WorkerType": "test-general", "SupportedNodeTypes": ["script", "http", "timer"], "ResourceLimits": {"MaxCpuUsagePercent": 80.0, "MaxMemoryUsagePercent": 80.0, "MaxDiskUsageBytes": **********}, "HealthCheck": {"CheckInterval": "00:00:30", "UnhealthyThreshold": "00:02:00", "EnableDetailedHealthCheck": true}}, "Communication": {"NatsConnectionString": "nats://localhost:4222", "NatsOptions": {"Name": "FlowCustomV1-TestNode", "Verbose": false, "ConnectTimeout": "00:00:05", "MaxReconnectAttempts": -1, "ReconnectWait": "00:00:02"}, "HeartbeatInterval": "00:00:30", "RegistrationTimeout": "00:00:10"}}, "ConnectionStrings": {"NATS": "nats://localhost:4222", "DefaultConnection": "Server=localhost;Database=flowcustom_test;Uid=root;Pwd=password;", "Redis": "localhost:6379"}}