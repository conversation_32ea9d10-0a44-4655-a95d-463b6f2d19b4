[2025-07-31 01:31:50.936 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 01:31:51.076 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 01:31:51.079 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 01:31:51.088 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/debug
[2025-07-31 01:31:51.093 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/info
[2025-07-31 01:31:51.099 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/warn
[2025-07-31 01:31:51.106 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/error
[2025-07-31 01:31:51.112 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/trace
[2025-07-31 01:31:51.113 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 01:31:51.234 +00:00 ERR] Program: 后台初始化失败
MySqlConnector.MySqlException (0x80004005): Unable to connect to any of the specified MySQL hosts.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1063
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at FlowCustomV1.Data.Providers.MySqlProvider.ConfigureDbContext(DbContextOptionsBuilder builder, String connectionString) in /src/src/FlowCustomV1.Data/Providers/MySqlProvider.cs:line 17
   at Program.<>c__DisplayClass0_0.<<Main>$>b__20(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in /src/src/FlowCustomV1.Api/Program.cs:line 85
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__10>d.MoveNext() in /src/src/FlowCustomV1.Api/Program.cs:line 288
[2025-07-31 01:31:51.308 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 01:31:51.314 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 01:31:51.317 +00:00 WRN] Microsoft.AspNetCore.Hosting.Diagnostics: Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:80'.
[2025-07-31 01:46:01.234 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已停止
[2025-07-31 01:46:01.236 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 正在停止日志清理服务...
[2025-07-31 01:46:01.237 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-07-31 01:46:01.240 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-07-31 01:46:01.248 +00:00 INF] FlowCustomV1.Api.Services.NatsService: 正在停止NATS服务...
[2025-07-31 01:46:01.251 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS服务已停止
[2025-07-31 01:46:03.568 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 01:46:03.621 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 01:46:03.622 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 01:46:03.627 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/debug
[2025-07-31 01:46:03.630 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/info
[2025-07-31 01:46:03.632 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/warn
[2025-07-31 01:46:03.635 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/error
[2025-07-31 01:46:03.638 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/trace
[2025-07-31 01:46:03.639 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 01:46:03.746 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 01:46:03.749 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 01:46:03.750 +00:00 WRN] Microsoft.AspNetCore.Hosting.Diagnostics: Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:80'.
[2025-07-31 01:46:04.133 +00:00 INF] Program: 开始初始化数据库和插件管理器...
[2025-07-31 01:46:04.146 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🚀 开始数据库初始化流程...
[2025-07-31 01:46:04.147 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 使用数据库提供者: MySQL
[2025-07-31 01:46:04.148 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔗 连接字符串验证: 有效
[2025-07-31 01:46:04.148 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库是否存在...
[2025-07-31 01:46:04.182 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🆕 数据库不存在，正在创建...
[2025-07-31 01:46:04.191 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 数据库存在性检查失败，将在连接时处理: Access denied for user 'flowcustom'@'%' to database 'mysql'
MySqlConnector.MySqlException (0x80004005): Access denied for user 'flowcustom'@'%' to database 'mysql'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ServerSession.SwitchAuthenticationAsync(ConnectionSettings cs, String password, PayloadData payload, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 672
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 537
   at MySqlConnector.Core.ConnectionPool.ConnectSessionAsync(MySqlConnection connection, Action`4 logMessage, Int64 startingTimestamp, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ConnectionPool.cs:line 428
   at MySqlConnector.Core.ConnectionPool.ConnectSessionAsync(MySqlConnection connection, Action`4 logMessage, Int64 startingTimestamp, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ConnectionPool.cs:line 433
   at MySqlConnector.Core.ConnectionPool.GetSessionAsync(MySqlConnection connection, Int64 startingTimestamp, Int32 timeoutMilliseconds, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ConnectionPool.cs:line 111
   at MySqlConnector.Core.ConnectionPool.GetSessionAsync(MySqlConnection connection, Int64 startingTimestamp, Int32 timeoutMilliseconds, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ConnectionPool.cs:line 144
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 919
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at FlowCustomV1.Data.Providers.MySqlProvider.CreateDatabaseIfNotExistsAsync(String connectionString) in /src/src/FlowCustomV1.Data/Providers/MySqlProvider.cs:line 178
   at FlowCustomV1.Api.Services.DatabaseInitializationService.EnsureDatabaseExistsAsync(String connectionString) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 96
[2025-07-31 01:46:04.205 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 检查数据库连接...
[2025-07-31 01:46:04.808 +00:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConfigurationChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 01:46:04.809 +00:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.ConnectionChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 01:46:04.809 +00:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Metadata' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 01:46:04.810 +00:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.NodeChanges' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 01:46:04.810 +00:00 WRN] Microsoft.EntityFrameworkCore.Model.Validation: The property 'WorkflowVersion.Tags' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
[2025-07-31 01:46:05.056 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库连接正常
[2025-07-31 01:46:05.059 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔄 检查数据库迁移状态...
[2025-07-31 01:46:05.161 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 📊 迁移状态: 已应用 1 个，待应用 0 个
[2025-07-31 01:46:05.161 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 数据库已是最新版本
[2025-07-31 01:46:05.163 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔍 验证数据库表结构...
[2025-07-31 01:46:05.315 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 WorkflowDefinitions 不存在
[2025-07-31 01:46:05.319 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 WorkflowExecutions 不存在
[2025-07-31 01:46:05.321 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 WorkflowVersions 不存在
[2025-07-31 01:46:05.324 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 NodeExecutions 不存在
[2025-07-31 01:46:05.328 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ExecutionLogs 不存在
[2025-07-31 01:46:05.331 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 PluginDefinitions 不存在
[2025-07-31 01:46:05.334 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 ParameterTemplates 不存在
[2025-07-31 01:46:05.337 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateDefinitions 不存在
[2025-07-31 01:46:05.339 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateCategories 不存在
[2025-07-31 01:46:05.343 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateInstances 不存在
[2025-07-31 01:46:05.346 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 表 TemplateUsageStatistics 不存在
[2025-07-31 01:46:05.346 +00:00 WRN] FlowCustomV1.Api.Services.DatabaseInitializationService: ⚠️ 发现缺失表: WorkflowDefinitions, WorkflowExecutions, WorkflowVersions, NodeExecutions, ExecutionLogs, PluginDefinitions, ParameterTemplates, TemplateDefinitions, TemplateCategories, TemplateInstances, TemplateUsageStatistics
[2025-07-31 01:46:05.347 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 重新创建缺失的表...
[2025-07-31 01:46:05.357 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ✅ 表结构重建完成
[2025-07-31 01:46:05.358 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🔧 检查基础数据...
[2025-07-31 01:46:05.463 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: ℹ️ 检测到空数据库，基础数据将在后续流程中初始化
[2025-07-31 01:46:05.464 +00:00 INF] FlowCustomV1.Api.Services.DatabaseInitializationService: 🎉 数据库初始化完成
[2025-07-31 01:46:05.466 +00:00 INF] Program: 获取插件管理器服务...
[2025-07-31 01:46:05.468 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 01:46:05.469 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 01:46:05.470 +00:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 01:46:05.470 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 01:46:05.471 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 01:46:05.472 +00:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 01:46:05.473 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 01:46:05.473 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 01:46:05.473 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 01:46:05.474 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 01:46:05.474 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 01:46:05.477 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 01:46:05.477 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 01:46:05.478 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 01:46:05.478 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 01:46:05.478 +00:00 INF] Program: 插件管理器服务获取成功: UnifiedPluginManager
[2025-07-31 01:46:05.478 +00:00 INF] Program: 开始初始化插件管理器...
[2025-07-31 01:46:05.479 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始初始化统一插件管理器
[2025-07-31 01:46:05.480 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始初始化内置插件管理器
[2025-07-31 01:46:05.481 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册内置节点执行器
[2025-07-31 01:46:05.483 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StartNode - 开始节点
[2025-07-31 01:46:05.484 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: EndNode - 结束节点
[2025-07-31 01:46:05.485 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DelayNode - 延迟节点
[2025-07-31 01:46:05.486 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LogNode - 日志节点
[2025-07-31 01:46:05.487 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: TimerTriggerNode - 定时触发器
[2025-07-31 01:46:05.488 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ManualTriggerNode - 手动触发器
[2025-07-31 01:46:05.490 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: WebhookTrigger - Webhook触发器
[2025-07-31 01:46:05.491 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ConditionNode - 条件节点
[2025-07-31 01:46:05.492 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: LoopNode - 循环节点
[2025-07-31 01:46:05.494 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: SwitchNode - 分支节点
[2025-07-31 01:46:05.503 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: HttpRequestNode - HTTP请求
[2025-07-31 01:46:05.504 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataTransformNode - 数据转换
[2025-07-31 01:46:05.505 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: ScriptExecutorNode - 脚本执行器
[2025-07-31 01:46:05.506 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 开始注册数据管道节点
[2025-07-31 01:46:05.507 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner: 哈希数据分区器已初始化，默认分区数: 24
[2025-07-31 01:46:05.507 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已初始化，最大并发度: 48
[2025-07-31 01:46:05.509 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已初始化，最大并发度: 48
[2025-07-31 01:46:05.510 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: StreamProcessor - 流式处理器
[2025-07-31 01:46:05.511 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 StreamProcessor 节点
[2025-07-31 01:46:05.511 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: BatchProcessor - 批量处理器
[2025-07-31 01:46:05.512 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 BatchProcessor 节点
[2025-07-31 01:46:05.512 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册内置节点执行器: DataPartitioner - 数据分区器
[2025-07-31 01:46:05.513 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 成功注册 DataPartitioner 节点
[2025-07-31 01:46:05.513 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 数据管道节点注册完成
[2025-07-31 01:46:05.520 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置节点执行器注册完成，共注册 16 个执行器
[2025-07-31 01:46:05.523 +00:00 INF] FlowCustomV1.PluginHost.BuiltinPluginManager: 内置插件管理器初始化完成，共注册 16 个内置节点执行器
[2025-07-31 01:46:05.523 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 内置插件初始化完成
[2025-07-31 01:46:05.524 +00:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: 初始化JSON插件管理器
[2025-07-31 01:46:05.525 +00:00 INF] FlowCustomV1.PluginHost.JsonPluginManager: JSON插件管理器初始化完成
[2025-07-31 01:46:05.525 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: JSON插件管理器初始化完成
[2025-07-31 01:46:05.525 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 所有插件管理器初始化完成
[2025-07-31 01:46:05.526 +00:00 INF] Program: 插件管理器初始化完成
[2025-07-31 01:46:05.526 +00:00 INF] Program: 开始初始化协议模板...
[2025-07-31 01:46:05.529 +00:00 INF] FlowCustomV1.Core.Services.ProtocolTemplateService: 初始化默认协议模板: 3 个
[2025-07-31 01:46:05.529 +00:00 INF] Program: 协议模板初始化完成
[2025-07-31 01:46:05.530 +00:00 INF] Program: 开始启动NATS服务...
[2025-07-31 01:46:05.533 +00:00 INF] FlowCustomV1.Api.Services.NatsService: 正在连接到NATS服务器: nats://nats:4222 (尝试 1/10)
[2025-07-31 01:46:05.618 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS连接成功建立并验证
[2025-07-31 01:46:05.620 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅NATS主题: system.log.frontend.*
[2025-07-31 01:46:05.620 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ 已订阅前端日志主题: system.log.frontend.*
[2025-07-31 01:46:05.621 +00:00 INF] FlowCustomV1.Api.Services.NatsService: 🔄 NATS服务已恢复正常运行
[2025-07-31 01:46:05.621 +00:00 INF] Program: NATS服务启动完成
[2025-07-31 01:46:05.622 +00:00 INF] Program: 配置NATS日志发布...
[2025-07-31 01:46:05.625 +00:00 INF] Program: NATS日志发布配置完成
[2025-07-31 01:46:05.625 +00:00 INF] Program: 后台初始化完成
[2025-07-31 01:46:35.470 +00:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 01:46:35.511 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:46:49.137 +00:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 01:46:49.172 +00:00 INF] Program: 请求完成: GET /api/test/health - 200 - 30ms
[2025-07-31 01:47:05.512 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:47:11.348 +00:00 INF] Program: 请求完成: GET /api/workflows - 200 - 273ms
[2025-07-31 01:47:11.485 +00:00 INF] Program: 请求完成: GET /api/workflows - 200 - 130ms
[2025-07-31 01:47:13.180 +00:00 INF] Program: 请求完成: POST /api/workflows - 201 - 515ms
[2025-07-31 01:47:13.343 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 BuiltinPluginManager 获取到 16 个节点类型定义
[2025-07-31 01:47:13.343 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 BuiltinPluginManager 获取到 16 个节点类型
[2025-07-31 01:47:13.346 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 JsonPluginManager 获取到 0 个节点类型定义
[2025-07-31 01:47:13.347 +00:00 INF] Program: 请求完成: GET /api/plugin/category-metadata - 200 - 10ms
[2025-07-31 01:47:13.348 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 JsonPluginManager 获取到 0 个节点类型
[2025-07-31 01:47:13.352 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 DllPluginManager 获取到 0 个节点类型
[2025-07-31 01:47:13.352 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 从 DllPluginManager 获取到 0 个节点类型定义
[2025-07-31 01:47:13.353 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: GetAllNodeTypeDefinitionsAsync: 总计返回 16 个节点类型
[2025-07-31 01:47:13.354 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: GetAllNodeTypeDefinitionsAsync(category=null): 总计返回 16 个节点类型定义
[2025-07-31 01:47:13.422 +00:00 INF] Program: 请求完成: GET /api/plugin/node-types/by-category - 200 - 95ms
[2025-07-31 01:47:13.422 +00:00 INF] Program: 请求完成: GET /api/plugin/node-types - 200 - 96ms
[2025-07-31 01:47:13.911 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:v30f6d5e] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://local[2025-07-31 01:47:17.861 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 01:47:22.844 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 01:47:24.604 +00:00 INF] FlowCustomV1.Api.Controllers.TestController: 代理测试参数模板API...
[2025-07-31 01:47:24.628 +00:00 INF] FlowCustomV1.Api.Controllers.TestController: 代理测试参数模板API...
e: 前端日志: [Session:v30f6d5e] [WARN] [App] 这是一条警告日志 URL: http://localhost:5173/ {"Id":"1753926433093-bb3sscr1x","SessionId":"session_1753926430868_bv30f6d5e","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-31 01:47:13.963 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:v30f6d5e] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753926433093-dourwl65f","SessionId":"session_1753926430868_bv30f6d5e","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-31 01:47:13.966 +00:00 WRN] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:v30f6d5e] [WARN] [App] 这是一条警告日志 URL: http://localhost:5173/ {"Id":"1753926433109-u7gu2dgys","SessionId":"session_1753926430868_bv30f6d5e","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-31 01:47:13.967 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:v30f6d5e] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753926433109-9rff8fk6f","SessionId":"session_1753926430868_bv30f6d5e","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-31 01:47:24.626 +00:00 INF] Program: 请求完成: GET /api/test/parameter-templates - 200 - 27ms
[2025-07-31 01:47:24.631 +00:00 INF] Program: 请求完成: GET /api/test/parameter-templates - 200 - 2ms
[2025-07-31 01:47:35.506 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:48:05.499 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:48:35.504 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:48:52.903 +00:00 INF] Program: 请求完成: GET /api/workflows - 200 - 12ms
[2025-07-31 01:49:05.494 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:49:35.492 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:50:05.491 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:50:35.491 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:51:05.487 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:51:35.487 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:52:05.492 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:52:35.482 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:53:05.479 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:53:35.477 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:54:05.476 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:54:35.479 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:55:05.471 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:55:35.470 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:56:05.470 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:56:35.467 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:57:05.464 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:57:35.464 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:58:05.469 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:58:35.465 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:59:05.459 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 01:59:35.456 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:00:05.463 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:00:35.458 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:01:05.450 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:01:35.451 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:02:05.449 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:02:35.444 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:03:05.452 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:03:35.447 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:04:05.442 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:04:35.438 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:05:05.436 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:05:35.440 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:06:05.431 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:06:35.441 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:07:05.435 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:07:35.432 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:08:05.436 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:08:35.426 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:09:05.425 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:09:35.421 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:10:05.419 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:10:35.418 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:11:05.422 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:11:35.414 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:12:05.418 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:12:35.413 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:13:05.413 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:13:35.405 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:14:05.408 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:14:35.411 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:15:05.403 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:15:35.403 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:16:05.400 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:16:35.399 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:17:05.401 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:17:35.393 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:18:05.390 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:18:35.393 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:19:05.386 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:19:35.384 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:20:05.367 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:20:35.380 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:21:05.376 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:21:35.377 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:22:05.371 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:22:35.369 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:23:05.376 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:23:35.364 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:24:05.360 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:24:35.361 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:25:05.362 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:25:35.364 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:26:05.355 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:26:35.352 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:27:05.351 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:27:35.340 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:28:05.348 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:28:35.343 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:29:05.349 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:29:35.341 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:30:05.347 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:30:35.335 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:31:05.344 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:31:35.332 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:32:05.339 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:32:35.329 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:33:05.328 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:33:35.323 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:34:05.330 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:34:35.320 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:35:05.322 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:35:35.325 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:36:05.318 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:36:35.313 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:37:05.312 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:37:35.318 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:38:05.309 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:38:35.304 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:39:05.304 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:39:35.301 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:40:05.306 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:40:35.297 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:41:05.293 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:41:35.292 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:42:05.290 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:42:35.282 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:43:05.280 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:43:35.279 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:44:05.274 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:44:35.269 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:45:05.266 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:45:35.269 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:46:05.256 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:46:35.260 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:47:05.254 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:47:35.247 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:48:05.248 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:48:35.243 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:49:05.247 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:49:35.240 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:50:05.233 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:50:35.228 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:51:05.227 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:51:35.221 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:52:05.223 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:52:35.213 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:53:05.219 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:53:35.214 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:54:05.204 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:54:35.201 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:55:05.205 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:55:35.195 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:56:05.193 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:56:35.190 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:57:05.188 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:57:35.187 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:58:05.180 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:58:35.188 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:59:05.179 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 02:59:35.177 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:00:05.172 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:00:35.168 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:01:05.173 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:01:35.161 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:02:05.160 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:02:35.163 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:03:05.156 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:03:35.153 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:04:05.148 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:04:35.155 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:05:05.145 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:05:35.149 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:06:05.141 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:06:35.140 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:07:05.132 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:07:35.130 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:08:05.129 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:08:35.126 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:09:05.122 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:09:35.119 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:10:05.116 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:10:35.117 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:11:05.120 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:11:35.109 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:12:05.106 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:12:35.102 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:13:05.104 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:13:35.097 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:14:05.094 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:14:35.091 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:15:05.098 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:15:35.088 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:16:05.087 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:16:35.083 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:17:05.079 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:17:35.078 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:18:05.077 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:18:35.071 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:19:05.076 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:19:35.066 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:20:05.062 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:20:35.069 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:21:05.064 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:21:35.056 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:22:05.053 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:22:35.049 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:23:05.049 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:23:35.048 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:24:05.042 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:24:35.042 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:25:05.041 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:25:35.045 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:26:05.038 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:26:35.037 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:27:05.033 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:27:35.027 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:28:05.035 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:28:35.025 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:29:05.021 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:29:35.021 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:30:05.019 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:30:35.016 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:31:05.014 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:31:35.013 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:32:05.008 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:32:35.006 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:33:05.010 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:33:35.005 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:34:05.002 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:34:34.999 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:35:05.006 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:35:34.997 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:36:05.002 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:36:34.996 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:37:04.991 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:37:34.986 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:38:04.987 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:38:34.985 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:39:04.980 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:39:34.988 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:40:04.978 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:40:34.974 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:41:04.972 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:41:34.969 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:42:04.964 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:42:34.966 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:43:04.964 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:43:34.964 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:44:04.964 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:44:34.969 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:45:04.956 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:45:34.964 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:46:04.953 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:46:34.950 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:47:04.947 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:47:34.952 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:48:04.942 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:48:34.952 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:49:04.941 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:49:34.938 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:50:04.936 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:50:34.933 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:51:04.936 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:51:34.931 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:52:04.938 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:52:34.930 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:53:04.924 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:53:34.924 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:54:04.919 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:54:34.919 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:55:04.916 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:55:34.915 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:56:04.921 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:56:34.911 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:57:04.910 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:57:34.905 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:58:04.906 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:58:34.901 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:59:04.896 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 03:59:34.890 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:00:04.896 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:00:34.888 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:01:04.889 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:01:34.884 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:02:04.888 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:02:34.890 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:03:04.880 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:03:34.876 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:04:04.882 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:04:34.875 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:05:04.879 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:05:34.873 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:06:04.866 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:06:34.864 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:07:04.865 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:07:34.865 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:08:04.858 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:08:34.865 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:09:04.854 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:09:34.850 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:10:04.855 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:10:34.847 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:11:04.854 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:11:34.846 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:12:04.841 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:12:34.840 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:13:04.844 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:13:34.834 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:14:04.833 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:14:34.834 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:15:04.838 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:15:34.828 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:16:04.828 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:16:34.824 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:17:04.829 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:17:34.822 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:18:04.827 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:18:34.821 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:19:04.818 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:19:34.811 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:20:04.809 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:20:34.806 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:21:04.803 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:21:34.804 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:22:04.802 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:22:34.797 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:23:04.795 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:23:34.794 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:24:04.801 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:24:34.794 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:25:04.789 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:25:34.784 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:26:04.793 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:26:34.779 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:27:04.779 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:27:34.776 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:28:04.776 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:28:34.780 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:29:04.772 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:29:34.768 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:30:04.764 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:30:34.767 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:31:04.762 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:31:34.757 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:32:04.755 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:32:34.754 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:33:04.755 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:33:34.758 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:34:04.751 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:34:34.746 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:35:04.743 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:35:34.743 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:36:04.738 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:36:34.747 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:37:04.743 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:37:34.737 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:38:04.731 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:38:34.739 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:39:04.733 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:39:34.729 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:40:04.722 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:40:34.732 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:41:04.718 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:41:34.719 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:42:04.713 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:42:34.713 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:43:04.712 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:43:34.708 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:44:04.706 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:44:34.707 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:45:04.695 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:45:34.699 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:46:04.700 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:46:34.698 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:47:04.702 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:47:34.690 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:48:04.691 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:48:34.688 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:49:04.684 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:49:34.684 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:50:04.680 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:50:34.677 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:51:04.675 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:51:34.679 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:52:04.676 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:52:34.670 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:53:04.672 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:53:34.670 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:54:04.665 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:54:34.663 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:55:04.660 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:55:34.662 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 并行处理器性能监控 - 总处理数: 0, 总失败数: 0, 活跃工作线程: 0, 队列长度: 0
[2025-07-31 04:55:59.142 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已停止
[2025-07-31 04:55:59.146 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 正在停止日志清理服务...
[2025-07-31 04:55:59.152 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-07-31 04:55:59.155 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已停止
[2025-07-31 04:55:59.182 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor: 高吞吐量数据处理器已释放
[2025-07-31 04:55:59.183 +00:00 INF] FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor: 高性能并行处理器已释放
[2025-07-31 04:55:59.201 +00:00 INF] FlowCustomV1.Api.Services.NatsService: 正在停止NATS服务...
[2025-07-31 04:55:59.252 +00:00 INF] FlowCustomV1.Api.Services.NatsService: ✅ NATS服务已停止
[2025-07-31 15:11:01.390 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 开始创建 UnifiedPluginManager - 步骤1: 基础字段初始化完成
[2025-07-31 15:11:01.499 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2: 开始获取可选服务
[2025-07-31 15:11:01.504 +00:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存服务已初始化，清理间隔: "00:10:00"
[2025-07-31 15:11:01.514 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2a: IPluginCacheService 获取完成
[2025-07-31 15:11:01.519 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2b: IPerformanceMonitoringService 获取完成
[2025-07-31 15:11:01.523 +00:00 INF] FlowCustomV1.Engine.Services.LocalExecutorFactory: 跳过内置执行器类型注册（暂未实现）
[2025-07-31 15:11:01.525 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤2c: IExecutionModeSelector 获取完成
[2025-07-31 15:11:01.526 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3: 开始创建子管理器
[2025-07-31 15:11:01.527 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: 创建 BuiltinPluginManager
[2025-07-31 15:11:01.530 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3a: BuiltinPluginManager 创建成功
[2025-07-31 15:11:01.531 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: 创建 JsonPluginManager
[2025-07-31 15:11:01.538 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3b: JsonPluginManager 创建成功
[2025-07-31 15:11:01.539 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: 创建 DllPluginManager
[2025-07-31 15:11:01.541 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: 步骤3c: DllPluginManager 创建成功
[2025-07-31 15:11:01.541 +00:00 INF] FlowCustomV1.PluginHost.UnifiedPluginManager: UnifiedPluginManager 构造函数完成
[2025-07-31 15:11:01.572 +00:00 INF] FlowCustomV1.Engine.Concurrency.AdaptiveSemaphore: AdaptiveSemaphore initialized: InitialCount=100, MaxCount=100
[2025-07-31 15:11:01.584 +00:00 INF] FlowCustomV1.Engine.Concurrency.BackpressureController: BackpressureController initialized with settings: Watermarks: [20.00 %-50.00 %-80.00 %], Delay: [5-500]ms, Threshold: 2, Monitoring: True
[2025-07-31 15:11:01.591 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎初始化完成: 最大并发=100, 队列容量=10000, 配置=WorkflowEngineConfiguration
[2025-07-31 15:11:01.596 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务已初始化，清理间隔: "1.00:00:00"，保留天数: 30
[2025-07-31 15:11:01.609 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitor: 资源监控已启动，初始内存使用: 8,041,592 字节
[2025-07-31 15:11:01.611 +00:00 INF] FlowCustomV1.Engine.ObjectPool.ResourceMonitoringService: 资源监控服务已启动
[2025-07-31 15:11:01.616 +00:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群服务启动检查 - EnableClusterMode: false, NodeMode: "Standalone", NodeId: 06777577e9fc
[2025-07-31 15:11:01.618 +00:00 INF] FlowCustomV1.Api.Services.ClusterService: 🔧 集群模式未启用，运行在独立模式
[2025-07-31 15:11:01.619 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理服务开始运行
[2025-07-31 15:11:01.622 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 开始执行日志清理任务
[2025-07-31 15:11:01.629 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/debug
[2025-07-31 15:11:01.634 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/info
[2025-07-31 15:11:01.639 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/warn
[2025-07-31 15:11:01.643 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/error
[2025-07-31 15:11:01.648 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 创建日志目录: /app/logs/frontend/trace
[2025-07-31 15:11:01.650 +00:00 INF] FlowCustomV1.Api.Services.FrontendLogService: 前端日志清理完成，保留期限: 30 天
[2025-07-31 15:11:01.623 +00:00 ERR] Program: 后台初始化失败
MySqlConnector.MySqlException (0x80004005): Unable to connect to any of the specified MySQL hosts.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1063
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at FlowCustomV1.Data.Providers.MySqlProvider.ConfigureDbContext(DbContextOptionsBuilder builder, String connectionString) in /src/src/FlowCustomV1.Data/Providers/MySqlProvider.cs:line 17
   at Program.<>c__DisplayClass0_0.<<Main>$>b__23(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in /src/src/FlowCustomV1.Api/Program.cs:line 120
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__12>d.MoveNext() in /src/src/FlowCustomV1.Api/Program.cs:line 355
[2025-07-31 15:11:01.879 +00:00 INF] FlowCustomV1.Api.Services.LogCleanupService: 日志清理任务完成
[2025-07-31 15:11:01.881 +00:00 INF] FlowCustomV1.Api.Services.NodeStatusNatsPublisher: 节点状态NATS发布服务已启动
[2025-07-31 15:11:01.884 +00:00 WRN] Microsoft.AspNetCore.Hosting.Diagnostics: Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:80'.
[2025-07-31 15:11:06.606 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎执行处理器已启动，完全初始化完成
[2025-07-31 15:11:11.584 +00:00 INF] FlowCustomV1.Engine.WorkflowEngine: 工作流引擎定时器已启动
[2025-07-31 15:11:31.398 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:11:31.517 +00:00 INF] FlowCustomV1.Engine.Services.PluginCacheService: 插件缓存清理定时器已启动
[2025-07-31 15:12:01.373 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:12:31.366 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:13:01.368 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:13:31.360 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:14:01.359 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:14:31.357 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:15:01.357 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:15:31.352 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:16:01.350 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:16:31.348 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:17:01.355 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:17:31.350 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:18:01.348 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:18:31.347 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:19:01.340 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:19:31.336 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:20:01.336 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:20:31.336 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:21:01.331 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:21:31.327 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:22:01.333 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:22:31.323 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:23:01.320 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:23:31.319 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:24:01.308 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:24:10.570 +00:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-07-31 15:24:10.591 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: 开始初始化内置参数模板...
[2025-07-31 15:24:10.594 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-simple-get-template - 简单GET请求
[2025-07-31 15:24:10.594 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-rest-api-template - REST API调用
[2025-07-31 15:24:10.595 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-post-data-template - POST数据提交
[2025-07-31 15:24:10.595 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-file-upload-template - 文件上传
[2025-07-31 15:24:10.596 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-webhook-template - Webhook调用
[2025-07-31 15:24:10.596 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: Created parameter template: http-third-party-api-template - 第三方API集成
[2025-07-31 15:24:10.596 +00:00 INF] FlowCustomV1.Core.Services.ParameterTemplateService: 成功初始化内置参数模板，共创建 5 个模板
[2025-07-31 15:24:10.609 +00:00 INF] Program: 请求完成: GET /api/test/health - 200 - 33ms
[2025-07-31 15:24:31.313 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:25:01.315 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:25:31.311 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:26:01.308 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:26:31.309 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:27:01.304 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
[2025-07-31 15:27:31.300 +00:00 WRN] FlowCustomV1.Api.Services.NatsService: ⚠️ NATS连接不可用，无法发布消息: workflow.execution.system.node.resource-monitor.status
