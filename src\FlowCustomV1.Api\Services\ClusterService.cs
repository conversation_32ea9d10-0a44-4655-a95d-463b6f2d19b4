using FlowCustomV1.Api.Configuration;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Engine.Services;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Collections.Concurrent;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// 集群服务实现
/// </summary>
public class ClusterService : IClusterService, IHostedService
{
    private readonly ILogger<ClusterService> _logger;
    private readonly ClusterConfiguration _config;
    private readonly INatsService _natsService;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IServiceProvider _serviceProvider;
    
    private readonly ConcurrentDictionary<string, NodeInfo> _clusterNodes;
    private readonly ConcurrentDictionary<string, WorkflowTaskRequest> _activeTasks;
    private Timer? _heartbeatTimer;
    private Timer? _healthCheckTimer;
    private bool _isStarted;

    public NodeMode NodeMode => _config.NodeMode;
    public bool IsClusterEnabled => _config.EnableClusterMode;

    public ClusterService(
        ILogger<ClusterService> logger,
        IOptions<ClusterConfiguration> config,
        INatsService natsService,
        IWorkflowEngine workflowEngine,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _config = config.Value;
        _natsService = natsService;
        _workflowEngine = workflowEngine;
        _serviceProvider = serviceProvider;
        
        _clusterNodes = new ConcurrentDictionary<string, NodeInfo>();
        _activeTasks = new ConcurrentDictionary<string, WorkflowTaskRequest>();
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🔧 集群服务启动检查 - EnableClusterMode: {EnableClusterMode}, NodeMode: {NodeMode}, NodeId: {NodeId}",
            _config.EnableClusterMode, _config.NodeMode, _config.NodeId);

        if (!_config.EnableClusterMode)
        {
            _logger.LogInformation("🔧 集群模式未启用，运行在独立模式");
            return;
        }

        try
        {
            _logger.LogInformation("🚀 启动集群服务 - 节点模式: {NodeMode}, 节点ID: {NodeId}", 
                _config.NodeMode, _config.NodeId);

            // 验证配置
            _config.Validate();

            // 初始化NATS订阅 - 添加重试机制
            await InitializeNatsSubscriptionsWithRetryAsync();

            // 启动心跳定时器
            _heartbeatTimer = new Timer(SendHeartbeatAsync, null, 
                TimeSpan.Zero, _config.Communication.HeartbeatInterval);

            // 启动健康检查定时器
            _healthCheckTimer = new Timer(PerformHealthCheckAsync, null, 
                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

            // 注册当前节点
            await RegisterCurrentNodeAsync();

            _isStarted = true;
            _logger.LogInformation("✅ 集群服务启动成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 集群服务启动失败");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted) return;

        try
        {
            _logger.LogInformation("🛑 停止集群服务...");

            // 停止定时器
            _heartbeatTimer?.Dispose();
            _healthCheckTimer?.Dispose();

            // 注销当前节点
            await UnregisterCurrentNodeAsync();

            _isStarted = false;
            _logger.LogInformation("✅ 集群服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 集群服务停止失败");
        }
    }

    public async Task<ClusterStatus> GetClusterStatusAsync()
    {
        var currentNode = await GetCurrentNodeInfoAsync();
        var nodesList = _clusterNodes.Values.ToList();

        _logger.LogInformation("🔍 集群状态查询 - 节点字典大小: {Count}, 节点列表: [{Nodes}]",
            _clusterNodes.Count,
            string.Join(", ", _clusterNodes.Keys));

        return new ClusterStatus
        {
            ClusterName = _config.ClusterName,
            CurrentNode = currentNode,
            Nodes = nodesList,
            ActiveTasks = _activeTasks.Count,
            QueuedTasks = 0, // TODO: 从NATS获取队列长度
            IsHealthy = _isStarted && currentNode.Status == NodeHealthStatus.Healthy,
            LastUpdated = DateTime.UtcNow
        };
    }

    public async Task<string> DispatchWorkflowTaskAsync(WorkflowTaskRequest request)
    {
        if (_config.NodeMode != NodeMode.Master)
        {
            throw new InvalidOperationException("只有Master节点可以分发任务");
        }

        try
        {
            _logger.LogInformation("📤 分发工作流任务: {TaskId} -> 工作流: {WorkflowId}", 
                request.TaskId, request.WorkflowId);

            // 选择最佳Worker节点
            var targetWorker = await SelectBestWorkerAsync(request);
            if (targetWorker != null)
            {
                request.TargetWorkerId = targetWorker.NodeId;
                _logger.LogDebug("🎯 选择Worker节点: {WorkerId} (负载: {LoadScore:F2})", 
                    targetWorker.NodeId, targetWorker.Load.LoadScore);
            }

            // 发布任务到NATS - 使用系统通知方式
            var taskJson = JsonSerializer.Serialize(request);
            await _natsService.PublishSystemNotificationAsync("workflow_task", "New workflow task", request);

            // 记录活跃任务
            _activeTasks.TryAdd(request.TaskId, request);

            _logger.LogInformation("✅ 任务分发成功: {TaskId}", request.TaskId);
            return request.TaskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 任务分发失败: {TaskId}", request.TaskId);
            throw;
        }
    }

    public async Task ProcessWorkflowTaskAsync(WorkflowTaskRequest request)
    {
        if (_config.NodeMode != NodeMode.Worker)
        {
            throw new InvalidOperationException("只有Worker节点可以处理任务");
        }

        try
        {
            _logger.LogInformation("🔧 处理工作流任务: {TaskId} -> 工作流: {WorkflowId}", 
                request.TaskId, request.WorkflowId);

            // 检查是否指定了目标Worker
            if (!string.IsNullOrEmpty(request.TargetWorkerId) && 
                request.TargetWorkerId != _config.NodeId)
            {
                _logger.LogDebug("⏭️ 任务指定给其他Worker: {TargetWorkerId}, 跳过处理", 
                    request.TargetWorkerId);
                return;
            }

            // 检查Worker容量
            if (_activeTasks.Count >= _config.Worker.MaxConcurrentExecutions)
            {
                _logger.LogWarning("⚠️ Worker节点已达到最大并发数，拒绝任务: {TaskId}", request.TaskId);
                return;
            }

            // 记录活跃任务
            _activeTasks.TryAdd(request.TaskId, request);

            try
            {
                // 发布任务开始状态
                await PublishWorkflowStatusAsync(new WorkflowStatusUpdate
                {
                    ExecutionId = request.ExecutionId,
                    WorkflowId = request.WorkflowId,
                    Status = "running",
                    Message = $"任务开始执行 - Worker: {_config.NodeId}",
                    ProcessedBy = _config.NodeId
                });

                // 执行工作流 - 使用WorkflowDefinitionId
                var workflowId = Guid.Parse(request.WorkflowId);
                var executionResult = await _workflowEngine.ExecuteAsync(
                    workflowId, request.InputData, request.TriggeredBy);

                // 发布完成状态
                await PublishWorkflowStatusAsync(new WorkflowStatusUpdate
                {
                    ExecutionId = request.ExecutionId,
                    WorkflowId = request.WorkflowId,
                    Status = executionResult.Status == Core.Models.WorkflowExecutionStatus.Completed ? "completed" : "failed",
                    Progress = 100,
                    Message = executionResult.Status == Core.Models.WorkflowExecutionStatus.Completed ? "工作流执行完成" : "工作流执行失败",
                    OutputData = executionResult.OutputData,
                    ErrorMessage = executionResult.ErrorMessage,
                    ProcessedBy = _config.NodeId
                });

                _logger.LogInformation("✅ 工作流任务处理完成: {TaskId}", request.TaskId);
            }
            finally
            {
                // 移除活跃任务
                _activeTasks.TryRemove(request.TaskId, out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 工作流任务处理失败: {TaskId}", request.TaskId);

            // 发布失败状态
            await PublishWorkflowStatusAsync(new WorkflowStatusUpdate
            {
                ExecutionId = request.ExecutionId,
                WorkflowId = request.WorkflowId,
                Status = "failed",
                ErrorMessage = ex.Message,
                ProcessedBy = _config.NodeId
            });

            // 移除活跃任务
            _activeTasks.TryRemove(request.TaskId, out _);
        }
    }

    public async Task PublishWorkflowStatusAsync(WorkflowStatusUpdate statusUpdate)
    {
        try
        {
            await _natsService.PublishWorkflowStatusAsync(statusUpdate.ExecutionId, statusUpdate.Status, statusUpdate);

            _logger.LogDebug("📡 发布工作流状态: {ExecutionId} -> {Status}",
                statusUpdate.ExecutionId, statusUpdate.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发布工作流状态失败: {ExecutionId}", statusUpdate.ExecutionId);
        }
    }

    public async Task PublishNodeStatusAsync(NodeStatusUpdate statusUpdate)
    {
        try
        {
            await _natsService.PublishNodeStatusAsync(statusUpdate.ExecutionId, statusUpdate.NodeId, statusUpdate.Status, statusUpdate);

            _logger.LogDebug("📡 发布节点状态: {NodeId} -> {Status}",
                statusUpdate.NodeId, statusUpdate.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 发布节点状态失败: {NodeId}", statusUpdate.NodeId);
        }
    }

    // 私有辅助方法
    private async Task InitializeNatsSubscriptionsWithRetryAsync()
    {
        const int maxRetries = 10;
        const int retryDelayMs = 1000;

        for (int i = 0; i < maxRetries; i++)
        {
            try
            {
                if (_natsService.IsConnected)
                {
                    await InitializeNatsSubscriptionsAsync();
                    _logger.LogInformation("✅ NATS订阅初始化成功");
                    return;
                }
                else
                {
                    _logger.LogWarning("⏳ NATS未连接，等待连接... (尝试 {Attempt}/{MaxRetries})", i + 1, maxRetries);
                    await Task.Delay(retryDelayMs);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "❌ NATS订阅初始化失败，重试中... (尝试 {Attempt}/{MaxRetries})", i + 1, maxRetries);
                await Task.Delay(retryDelayMs);
            }
        }

        _logger.LogError("❌ NATS订阅初始化最终失败，已达到最大重试次数");
    }

    private async Task InitializeNatsSubscriptionsAsync()
    {
        if (_config.NodeMode == NodeMode.Worker)
        {
            // Worker节点订阅系统通知来接收任务
            await _natsService.SubscribeAsync("system.notification", async (data) =>
            {
                try
                {
                    var notification = JsonSerializer.Deserialize<Dictionary<string, object>>(data);
                    if (notification != null && notification.ContainsKey("type") &&
                        notification["type"].ToString() == "workflow_task")
                    {
                        var requestJson = JsonSerializer.Serialize(notification["data"]);
                        var request = JsonSerializer.Deserialize<WorkflowTaskRequest>(requestJson);
                        if (request != null)
                        {
                            await ProcessWorkflowTaskAsync(request);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理NATS任务消息失败");
                }
            });
        }

        // 订阅集群相关主题
        await _natsService.SubscribeAsync("cluster.heartbeat", HandleHeartbeatMessage);
        await _natsService.SubscribeAsync("cluster.events", HandleClusterEventMessage);
    }

    // 私有辅助方法实现
    private async void SendHeartbeatAsync(object? state)
    {
        try
        {
            var heartbeat = new
            {
                NodeId = _config.NodeId,
                DisplayName = _config.NodeDisplayName,
                NodeMode = _config.NodeMode.ToString(),
                Timestamp = DateTime.UtcNow,
                ActiveTasks = _activeTasks.Count,
                ClusterName = _config.ClusterName
            };
            // 发送到集群心跳主题，而不是系统通知
            var heartbeatJson = JsonSerializer.Serialize(heartbeat);
            await _natsService.PublishAsync("cluster.heartbeat", heartbeatJson);
            _logger.LogInformation("📡 发送心跳: {NodeId} -> cluster.heartbeat", _config.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送心跳失败");
        }
    }

    private async void PerformHealthCheckAsync(object? state)
    {
        try
        {
            // 检查节点健康状态
            var isHealthy = _natsService.IsConnected && _isStarted;
            _logger.LogDebug("节点健康检查: {IsHealthy}", isHealthy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查失败");
        }
    }

    private async Task RegisterCurrentNodeAsync()
    {
        try
        {
            var registration = new
            {
                NodeId = _config.NodeId,
                DisplayName = _config.NodeDisplayName,
                NodeMode = _config.NodeMode.ToString(),
                ClusterName = _config.ClusterName,
                RegisteredAt = DateTime.UtcNow
            };
            await _natsService.PublishSystemNotificationAsync("node_registration", "Node registered", registration);
            _logger.LogInformation("节点注册成功: {NodeId}", _config.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "节点注册失败");
        }
    }

    private async Task UnregisterCurrentNodeAsync()
    {
        try
        {
            var unregistration = new
            {
                NodeId = _config.NodeId,
                UnregisteredAt = DateTime.UtcNow
            };
            await _natsService.PublishSystemNotificationAsync("node_unregistration", "Node unregistered", unregistration);
            _logger.LogInformation("节点注销成功: {NodeId}", _config.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "节点注销失败");
        }
    }

    private async Task<NodeInfo> GetCurrentNodeInfoAsync()
    {
        return new NodeInfo
        {
            NodeId = _config.NodeId,
            DisplayName = _config.NodeDisplayName,
            Mode = _config.NodeMode,
            Status = _isStarted && _natsService.IsConnected ? NodeHealthStatus.Healthy : NodeHealthStatus.Unhealthy,
            LastHeartbeat = DateTime.UtcNow,
            StartTime = DateTime.UtcNow, // 应该记录实际启动时间
            Load = new NodeLoadInfo
            {
                ActiveTasks = _activeTasks.Count,
                MaxConcurrency = _config.NodeMode == NodeMode.Worker ? _config.Worker.MaxConcurrentExecutions : 0
            }
        };
    }

    private async Task<NodeInfo?> SelectBestWorkerAsync(WorkflowTaskRequest request)
    {
        // 简单实现：返回第一个可用的Worker节点
        var availableWorkers = _clusterNodes.Values
            .Where(n => n.Mode == NodeMode.Worker && n.Status == NodeHealthStatus.Healthy)
            .OrderBy(n => n.Load.LoadScore)
            .ToList();

        return availableWorkers.FirstOrDefault();
    }

    private async Task HandleHeartbeatMessage(string data)
    {
        try
        {
            var heartbeat = JsonSerializer.Deserialize<Dictionary<string, object>>(data);
            if (heartbeat != null && heartbeat.ContainsKey("NodeId"))
            {
                var nodeId = heartbeat["NodeId"].ToString();
                if (nodeId != null && nodeId != _config.NodeId) // 不处理自己的心跳
                {
                    _logger.LogInformation("💓 收到心跳: {NodeId} <- cluster.heartbeat", nodeId);

                    // 更新或添加节点信息
                    var nodeInfo = new NodeInfo
                    {
                        NodeId = nodeId,
                        DisplayName = heartbeat.ContainsKey("DisplayName") ? heartbeat["DisplayName"]?.ToString() ?? nodeId : nodeId,
                        Mode = Enum.TryParse<NodeMode>(heartbeat.ContainsKey("NodeMode") ? heartbeat["NodeMode"]?.ToString() : "Standalone", out var mode) ? mode : NodeMode.Standalone,
                        Status = NodeHealthStatus.Healthy,
                        LastHeartbeat = DateTime.UtcNow,
                        StartTime = DateTime.UtcNow, // 这里应该从心跳消息中获取，暂时使用当前时间
                        Load = new NodeLoadInfo
                        {
                            ActiveTasks = heartbeat.ContainsKey("ActiveTasks") && int.TryParse(heartbeat["ActiveTasks"]?.ToString(), out var tasks) ? tasks : 0,
                            MaxConcurrency = 100 // 默认值，应该从配置中获取
                        }
                    };

                    var result = _clusterNodes.AddOrUpdate(nodeId, nodeInfo, (key, oldValue) =>
                    {
                        oldValue.LastHeartbeat = nodeInfo.LastHeartbeat;
                        oldValue.Status = nodeInfo.Status;
                        oldValue.Load = nodeInfo.Load;
                        return oldValue;
                    });

                    _logger.LogInformation("🔄 更新节点信息: {NodeId}, 模式: {Mode}, 字典大小: {Count}",
                        nodeId, nodeInfo.Mode, _clusterNodes.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理心跳消息失败");
        }
    }

    private Task HandleClusterEventMessage(string data)
    {
        try
        {
            var eventData = JsonSerializer.Deserialize<Dictionary<string, object>>(data);
            if (eventData != null)
            {
                _logger.LogDebug("收到集群事件: {Event}", eventData);
                // 处理集群事件
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理集群事件失败");
        }
        return Task.CompletedTask;
    }
}
