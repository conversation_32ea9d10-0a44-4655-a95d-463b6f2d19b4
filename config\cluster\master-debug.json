{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "FlowCustomV1.Cluster": "Debug", "FlowCustomV1.Engine": "Debug", "FlowCustomV1.Core": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "AllowedHosts": "*", "NodeConfiguration": {"NodeId": "master-001", "DisplayName": "调试Master节点", "ClusterName": "flowcustom-debug-cluster", "EnableMasterRole": true, "EnableWorkerRole": false, "Description": "FlowCustomV1调试环境Master节点", "Labels": {"environment": "debug", "region": "local", "version": "v0.9.8", "role": "master"}, "Master": {"ApiPort": 5279, "EnableSwagger": true, "EnableCors": true, "CorsOrigins": ["http://localhost:5173", "http://localhost:3000"], "TriggerManagement": {"EnableTimerTriggers": true, "EnableWebhookTriggers": true, "EnableManualTriggers": true, "MaxConcurrentTriggers": 100}, "Scheduler": {"LoadBalancingStrategy": "weighted_round_robin", "TaskTimeout": "00:30:00", "MaxRetryAttempts": 3, "RetryDelay": "00:00:05", "EnableTaskPriority": true, "MaxQueueSize": 10000}}, "Communication": {"NatsConnectionString": "nats://nats:4222", "NatsOptions": {"Name": "FlowCustomV1-Master-Debug", "Verbose": true, "ConnectTimeout": "00:00:10", "MaxReconnectAttempts": -1, "ReconnectWait": "00:00:02"}, "HeartbeatInterval": "00:00:30", "RegistrationTimeout": "00:00:15"}}, "ConnectionStrings": {"DefaultConnection": "Server=mysql;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;", "NATS": "nats://nats:4222", "Redis": "redis:6379"}, "Database": {"Provider": "MySQL", "CommandTimeout": 30, "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true}, "Cache": {"Provider": "Redis", "DefaultExpiration": "01:00:00"}}