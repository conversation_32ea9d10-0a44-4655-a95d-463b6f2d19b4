[2025-07-31 19:18:04.863 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:18:04.873 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:20:49.333 +08:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "parametertemplates" (
    "Id" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL CONSTRAINT "PK_parametertemplates" PRIMARY KEY,
    "Name" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "DisplayName" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Description" varchar(255) COLLATE utf8mb4_unicode_ci NULL,
    "Category" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "Version" varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    "IsBuiltIn" tinyint(1) NOT NULL,
    "IsPublic" tinyint(1) NOT NULL,
    "Structure" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Metadata" longtext COLLATE utf8mb4_unicode_ci NOT NULL,
    "Preview" longtext COLLATE utf8mb4_unicode_ci NULL
);
[2025-07-31 19:20:49.349 +08:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such collation sequence: utf8mb4_unicode_ci'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery()
   at System.Data.Common.DbCommand.ExecuteNonQueryAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\DatabaseInitializationService.cs:line 158
[2025-07-31 19:33:55.266 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:34:04.369 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:38:59.406 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 19:42:59.473 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not start to connect nats server: nats://localhost:4222
 ---> NATS.Client.Core.Internal.SocketClosedException: Socket has been closed.
   at NATS.Client.Core.Internal.SocketReader.ReadAtLeastAsync(Int32 minimumSize)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at NATS.Client.Core.Internal.NatsReadProtocolProcessor.ReadLoopAsync()
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.SetupReaderWriterAsync(Boolean reconnect)
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   --- End of inner exception stack trace ---
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:35.411 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 1/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:44.456 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 2/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:11:58.535 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 3/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:09.473 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 4/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:33.559 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 5/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
[2025-07-31 21:12:43.533 +08:00 ERR] FlowCustomV1.Api.Services.NatsService: ❌ NATS连接失败 (尝试 6/10)
NATS.Client.Core.NatsException: can not connect uris: nats://localhost:4222
   at NATS.Client.Core.NatsConnection.InitialConnectAsync()
   at NATS.Client.Core.NatsConnection.ConnectAsync()
   at FlowCustomV1.Api.Services.NatsService.ConnectWithRetryAsync(CancellationToken cancellationToken) in C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\src\FlowCustomV1.Api\Services\NatsService.cs:line 98
