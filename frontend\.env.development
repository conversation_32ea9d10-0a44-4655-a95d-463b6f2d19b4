# FlowCustomV1 前端开发环境配置
# Master-Worker集群模式

# API配置 - 连接到Master节点
VITE_API_BASE_URL=http://localhost:5279
VITE_API_TIMEOUT=30000

# NATS WebSocket配置 - 连接到NATS WebSocket端口
VITE_NATS_WS_URL=ws://localhost:8081
VITE_NATS_CONNECTION_NAME=FlowCustomV1-Frontend-Dev

# 集群配置
VITE_CLUSTER_MODE=true
VITE_MASTER_API_URL=http://localhost:5279
VITE_WORKER_HEALTH_URL=http://localhost:8080

# 开发模式配置
VITE_DEV_MODE=true
VITE_LOG_LEVEL=debug
VITE_ENABLE_MOCK_DATA=false

# 功能开关
VITE_ENABLE_CLUSTER_MONITORING=true
VITE_ENABLE_REAL_TIME_STATUS=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
