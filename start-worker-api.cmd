@echo off
echo 🚀 启动FlowCustomV1 Worker节点 (基于FlowCustomV1.Api)
echo =================================================

echo ℹ️  检查基础设施服务...
docker ps --filter "name=flowcustom-mysql-debug" --filter "name=flowcustom-redis-debug" --filter "name=flowcustom-nats-debug" --format "table {{.Names}}\t{{.Status}}"

echo.
echo ℹ️  启动Worker节点...
echo ℹ️  节点模式: Worker
echo ℹ️  配置文件: appsettings.Worker.json
echo ℹ️  健康检查: http://localhost:8080/api/cluster/health
echo ℹ️  按 Ctrl+C 停止服务
echo.

set ASPNETCORE_ENVIRONMENT=Worker
dotnet run --project src/FlowCustomV1.Api -- --urls "http://localhost:8080"
