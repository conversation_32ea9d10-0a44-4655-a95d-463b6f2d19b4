using System;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 通用对象池接口
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    public interface IObjectPool<T> : IDisposable where T : class
    {
        /// <summary>
        /// 从对象池获取对象
        /// </summary>
        /// <returns>对象实例</returns>
        T Get();

        /// <summary>
        /// 将对象返回到对象池
        /// </summary>
        /// <param name="item">要返回的对象</param>
        void Return(T item);

        /// <summary>
        /// 当前池中对象数量
        /// </summary>
        int Count { get; }

        /// <summary>
        /// 池的最大容量
        /// </summary>
        int MaxSize { get; }

        /// <summary>
        /// 已创建的对象总数（包括池中和使用中的）
        /// </summary>
        int TotalCreated { get; }

        /// <summary>
        /// 清空对象池
        /// </summary>
        void Clear();
    }
}
