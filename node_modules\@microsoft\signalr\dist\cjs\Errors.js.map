{"version": 3, "file": "Errors.js", "sourceRoot": "", "sources": ["../../src/Errors.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAIvE,+CAA+C;AAC/C,MAAa,SAAU,SAAQ,KAAK;IAQhC;;;;OAIG;IACH,YAAY,YAAoB,EAAE,UAAkB;QAChD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,GAAG,YAAY,kBAAkB,UAAU,GAAG,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAtBD,8BAsBC;AAED,2CAA2C;AAC3C,MAAa,YAAa,SAAQ,KAAK;IAKnC;;;OAGG;IACH,YAAY,eAAuB,qBAAqB;QACpD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAjBD,oCAiBC;AAED,8CAA8C;AAC9C,MAAa,UAAW,SAAQ,KAAK;IAKjC;;;OAGG;IACH,YAAY,eAAuB,oBAAoB;QACnD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAjBD,gCAiBC;AAED,8EAA8E;AAC9E,eAAe;AACf,MAAa,yBAA0B,SAAQ,KAAK;IAWhD;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC;QAE7C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AA1BD,8DA0BC;AAED,2EAA2E;AAC3E,eAAe;AACf,MAAa,sBAAuB,SAAQ,KAAK;IAW7C;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,wBAAwB,CAAC;QAE1C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AA1BD,wDA0BC;AAED,kEAAkE;AAClE,eAAe;AACf,MAAa,2BAA4B,SAAQ,KAAK;IAWlD;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,6BAA6B,CAAC;QAE/C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AA1BD,kEA0BC;AAED,4EAA4E;AAC5E,eAAe;AACf,MAAa,gCAAiC,SAAQ,KAAK;IAQvD;;;OAGG;IACH,YAAY,OAAe;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,kCAAkC,CAAC;QAEpD,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AArBD,4EAqBC;AAED,uDAAuD;AACvD,eAAe;AACf,MAAa,eAAgB,SAAQ,KAAK;IAQtC;;;;OAIG;IACH,YAAY,OAAe,EAAE,WAAoB;QAC7C,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAvBD,0CAuBC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n"]}