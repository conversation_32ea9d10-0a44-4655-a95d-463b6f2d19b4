using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Events;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.ObjectPool
{
    /// <summary>
    /// 资源监控服务接口
    /// </summary>
    public interface IResourceMonitoringService : IDisposable
    {
        /// <summary>
        /// 启动资源监控
        /// </summary>
        Task StartAsync();

        /// <summary>
        /// 停止资源监控
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// 获取资源监控器
        /// </summary>
        /// <returns>资源监控器实例</returns>
        IResourceMonitor GetResourceMonitor();

        /// <summary>
        /// 手动触发资源状态发布
        /// </summary>
        Task PublishResourceStatusAsync();

        /// <summary>
        /// 手动触发内存泄漏检测
        /// </summary>
        Task CheckMemoryLeaksAsync();
    }

    /// <summary>
    /// 资源监控服务实现
    /// </summary>
    public class ResourceMonitoringService : BackgroundService, IResourceMonitoringService
    {
        private readonly IResourceMonitor _resourceMonitor;
        private readonly INodeStatusEventPublisher? _eventPublisher;
        private readonly ILogger<ResourceMonitoringService> _logger;
        private readonly Timer? _publishTimer;
        private readonly Timer? _leakCheckTimer;
        private volatile bool _disposed;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="resourceMonitor">资源监控器</param>
        /// <param name="eventPublisher">事件发布器</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="loggerFactory">日志工厂</param>
        public ResourceMonitoringService(
            IResourceMonitor? resourceMonitor = null,
            INodeStatusEventPublisher? eventPublisher = null,
            ILogger<ResourceMonitoringService>? logger = null,
            ILoggerFactory? loggerFactory = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _resourceMonitor = resourceMonitor ?? new ResourceMonitor(loggerFactory?.CreateLogger<ResourceMonitor>());
            _eventPublisher = eventPublisher;

            // 设置定时器 - 每30秒发布一次资源状态
            _publishTimer = new Timer(PublishResourceStatusCallback, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            // 设置定时器 - 每5分钟检查一次内存泄漏
            _leakCheckTimer = new Timer(CheckMemoryLeaksCallback, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// 启动资源监控
        /// </summary>
        public Task StartAsync()
        {
            if (_disposed)
                return Task.CompletedTask;

            _resourceMonitor.StartMonitoring();
            _logger.LogInformation("资源监控服务已启动");
            return Task.CompletedTask;
        }

        /// <summary>
        /// 停止资源监控
        /// </summary>
        public Task StopAsync()
        {
            if (_disposed)
                return Task.CompletedTask;

            _resourceMonitor.StopMonitoring();
            _logger.LogInformation("资源监控服务已停止");
            return Task.CompletedTask;
        }

        /// <summary>
        /// 获取资源监控器
        /// </summary>
        public IResourceMonitor GetResourceMonitor()
        {
            return _resourceMonitor;
        }

        /// <summary>
        /// 手动触发资源状态发布
        /// </summary>
        public Task PublishResourceStatusAsync()
        {
            if (_disposed || _eventPublisher == null)
                return Task.CompletedTask;

            try
            {
                var resourceUsage = _resourceMonitor.GetResourceUsage();
                var statusData = new
                {
                    Type = "ResourceUsage",
                    Timestamp = DateTime.UtcNow,
                    Data = resourceUsage
                };

                var jsonData = JsonSerializer.Serialize(statusData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false
                });

                _eventPublisher.PublishNodeStatusChanged(new NodeStatusChangedEventArgs
                {
                    WorkflowExecutionId = "system",
                    NodeId = "resource-monitor",
                    NodeName = "Resource Monitor",
                    NodeType = "System",
                    Status = "monitoring",
                    StartTime = DateTime.UtcNow,
                    AdditionalData = resourceUsage
                });

                _logger.LogDebug("已发布资源使用状态 - 内存: {Memory:N0} 字节", resourceUsage.CurrentMemoryUsage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布资源状态时发生异常");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 手动触发内存泄漏检测
        /// </summary>
        public Task CheckMemoryLeaksAsync()
        {
            if (_disposed)
                return Task.CompletedTask;

            try
            {
                var leakResult = _resourceMonitor.DetectMemoryLeaks();
                
                if (leakResult.HasPotentialLeak)
                {
                    _logger.LogWarning("检测到潜在内存泄漏 - 增长率: {GrowthRate:N0} 字节/分钟", leakResult.MemoryGrowthRate);
                    
                    foreach (var detail in leakResult.Details)
                    {
                        _logger.LogWarning("内存泄漏详情: {Detail}", detail);
                    }

                    foreach (var recommendation in leakResult.Recommendations)
                    {
                        _logger.LogInformation("建议: {Recommendation}", recommendation);
                    }

                    // 发布内存泄漏警告到NATS
                    if (_eventPublisher != null)
                    {
                        var alertData = new
                        {
                            Type = "MemoryLeakAlert",
                            Timestamp = DateTime.UtcNow,
                            Data = leakResult
                        };

                        var jsonData = JsonSerializer.Serialize(alertData, new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                            WriteIndented = false
                        });

                        _eventPublisher.PublishNodeStatusChanged(new NodeStatusChangedEventArgs
                        {
                            WorkflowExecutionId = "system",
                            NodeId = "memory-leak-detector",
                            NodeName = "Memory Leak Detector",
                            NodeType = "System",
                            Status = "warning",
                            StartTime = DateTime.UtcNow,
                            ErrorMessage = "Potential memory leak detected",
                            AdditionalData = leakResult
                        });
                    }
                }
                else
                {
                    _logger.LogDebug("内存泄漏检测完成，未发现异常");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "内存泄漏检测时发生异常");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 后台服务执行
        /// </summary>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await StartAsync();

            try
            {
                while (!stoppingToken.IsCancellationRequested && !_disposed)
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                    
                    // 定期检查资源监控器状态
                    var resourceUsage = _resourceMonitor.GetResourceUsage();
                    
                    // 如果内存使用量超过阈值，记录警告
                    const long memoryWarningThreshold = 500 * 1024 * 1024; // 500MB
                    if (resourceUsage.CurrentMemoryUsage > memoryWarningThreshold)
                    {
                        _logger.LogWarning("内存使用量较高: {Memory:N0} 字节", resourceUsage.CurrentMemoryUsage);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不需要记录错误
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "资源监控后台服务执行时发生异常");
            }
            finally
            {
                await StopAsync();
            }
        }

        /// <summary>
        /// 发布资源状态回调
        /// </summary>
        private async void PublishResourceStatusCallback(object? state)
        {
            try
            {
                await PublishResourceStatusAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时发布资源状态时发生异常");
            }
        }

        /// <summary>
        /// 内存泄漏检测回调
        /// </summary>
        private async void CheckMemoryLeaksCallback(object? state)
        {
            try
            {
                await CheckMemoryLeaksAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时内存泄漏检测时发生异常");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public override void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            _publishTimer?.Dispose();
            _leakCheckTimer?.Dispose();
            _resourceMonitor?.Dispose();

            _logger.LogInformation("资源监控服务已释放");
            
            base.Dispose();
        }
    }

    /// <summary>
    /// 资源监控扩展方法
    /// </summary>
    public static class ResourceMonitoringExtensions
    {
        /// <summary>
        /// 添加资源监控服务到依赖注入容器
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static Microsoft.Extensions.DependencyInjection.IServiceCollection AddResourceMonitoring(
            this Microsoft.Extensions.DependencyInjection.IServiceCollection services)
        {
            // 注册资源监控器
            services.AddSingleton<IResourceMonitor>(provider =>
            {
                var logger = provider.GetService<ILogger<ResourceMonitor>>();
                var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
                return new ResourceMonitor(loggerFactory.CreateLogger<ResourceMonitor>());
            });

            // 注册资源监控服务
            services.AddSingleton<IResourceMonitoringService>(provider =>
            {
                var resourceMonitor = provider.GetRequiredService<IResourceMonitor>();
                var eventPublisher = provider.GetService<INodeStatusEventPublisher>();
                var logger = provider.GetRequiredService<ILogger<ResourceMonitoringService>>();
                var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
                return new ResourceMonitoringService(resourceMonitor, eventPublisher, logger, loggerFactory);
            });

            // 注册为后台服务
            services.AddHostedService<ResourceMonitoringService>(provider =>
                (ResourceMonitoringService)provider.GetRequiredService<IResourceMonitoringService>());

            return services;
        }
    }
}
