{"version": 3, "file": "AbortController.js", "sourceRoot": "", "sources": ["../../src/AbortController.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,qFAAqF;AACrF,0FAA0F;AAC1F,iCAAiC;AAEjC,2BAA2B;AAC3B,eAAe;AACf,MAAM,OAAO,eAAe;IAA5B;QACY,eAAU,GAAY,KAAK,CAAC;QAC7B,YAAO,GAAwB,IAAI,CAAC;IAkB/C,CAAC;IAhBU,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;SACJ;IACL,CAAC;IAED,IAAI,MAAM;QACN,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n"]}