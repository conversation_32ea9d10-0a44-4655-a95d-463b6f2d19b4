{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "FlowCustomV1.Cluster": "Debug", "FlowCustomV1.Engine": "Debug", "FlowCustomV1.Core": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "AllowedHosts": "*", "NodeConfiguration": {"NodeId": "worker-template", "DisplayName": "调试Worker节点模板", "ClusterName": "flowcustom-debug-cluster", "EnableMasterRole": false, "EnableWorkerRole": true, "Description": "FlowCustomV1调试环境Worker节点", "Labels": {"environment": "debug", "region": "local", "version": "v0.9.8", "role": "worker"}, "Worker": {"MaxConcurrentExecutions": 4, "WorkerType": "general", "SupportedNodeTypes": ["script", "http", "timer", "webhook", "condition", "loop"], "ResourceLimits": {"MaxCpuUsagePercent": 80.0, "MaxMemoryUsagePercent": 80.0, "MaxDiskUsageBytes": **********}, "HealthCheck": {"CheckInterval": "00:00:30", "UnhealthyThreshold": "00:02:00", "EnableDetailedHealthCheck": true}, "ExecutionSettings": {"DefaultTimeout": "00:05:00", "MaxExecutionTime": "00:30:00", "EnableExecutionLogging": true, "LogExecutionDetails": true}}, "Communication": {"NatsConnectionString": "nats://nats:4222", "NatsOptions": {"Name": "FlowCustomV1-Worker-Debug", "Verbose": true, "ConnectTimeout": "00:00:10", "MaxReconnectAttempts": -1, "ReconnectWait": "00:00:02"}, "HeartbeatInterval": "00:00:30", "RegistrationTimeout": "00:00:15"}}, "ConnectionStrings": {"DefaultConnection": "Server=mysql;Database=flowcustom_debug;Uid=flowcustom;Pwd=flowcustom123;", "NATS": "nats://nats:4222", "Redis": "redis:6379"}, "Database": {"Provider": "MySQL", "CommandTimeout": 30, "EnableSensitiveDataLogging": true, "EnableDetailedErrors": true}, "Cache": {"Provider": "Redis", "DefaultExpiration": "01:00:00"}}