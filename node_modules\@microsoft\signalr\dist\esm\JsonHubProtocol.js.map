{"version": 3, "file": "JsonHubProtocol.js", "sourceRoot": "", "sources": ["../../src/JsonHubProtocol.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAA8E,WAAW,EAAsC,MAAM,gBAAgB,CAAC;AAC7J,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACvC,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,MAAM,sBAAsB,GAAW,MAAM,CAAC;AAE9C,wCAAwC;AACxC,MAAM,OAAO,eAAe;IAA5B;QAEI,kBAAkB;QACF,SAAI,GAAW,sBAAsB,CAAC;QACtD,kBAAkB;QACF,YAAO,GAAW,CAAC,CAAC;QAEpC,kBAAkB;QACF,mBAAc,GAAmB,cAAc,CAAC,IAAI,CAAC;IAqHzE,CAAC;IAnHG;;;;OAIG;IACI,aAAa,CAAC,KAAa,EAAE,MAAe;QAC/C,2HAA2H;QAC3H,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,CAAC;SACb;QAED,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC;SAChC;QAED,qBAAqB;QACrB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEhD,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAe,CAAC;YACxD,IAAI,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aACvC;YACD,QAAQ,aAAa,CAAC,IAAI,EAAE;gBACxB,KAAK,WAAW,CAAC,UAAU;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,WAAW,CAAC,UAAU;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,WAAW,CAAC,UAAU;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,WAAW,CAAC,IAAI;oBACjB,oCAAoC;oBACpC,MAAM;gBACV,KAAK,WAAW,CAAC,KAAK;oBAClB,2CAA2C;oBAC3C,MAAM;gBACV,KAAK,WAAW,CAAC,GAAG;oBAChB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;oBAClC,MAAM;gBACV,KAAK,WAAW,CAAC,QAAQ;oBACrB,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;oBACvC,MAAM;gBACV;oBACI,6EAA6E;oBAC7E,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,wBAAwB,GAAG,aAAa,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC;oBAC/F,SAAS;aAChB;YACD,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACnC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,OAAmB;QACnC,OAAO,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;QAEtF,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;YACpC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;SAC/F;IACL,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;QAE5F,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;IACL,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;YAClC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAC;SACxF;QAED,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;IAChG,CAAC;IAEO,aAAa,CAAC,OAAmB;QACrC,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SAC1D;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAwB;QAC/C,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC/D;IACL,CAAC;IAEO,qBAAqB,CAAC,KAAU,EAAE,YAAoB;QAC1D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,EAAE,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;IACL,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AckMessage, CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, SequenceMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 2;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                case MessageType.Ack:\r\n                    this._isAckMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Sequence:\r\n                    this._isSequenceMessage(parsedMessage);\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _isAckMessage(message: AckMessage): void {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Ack message.\");\r\n        }\r\n    }\r\n\r\n    private _isSequenceMessage(message: SequenceMessage): void {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Sequence message.\");\r\n        }\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n"]}