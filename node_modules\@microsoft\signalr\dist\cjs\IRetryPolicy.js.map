{"version": 3, "file": "IRetryPolicy.js", "sourceRoot": "", "sources": ["../../src/IRetryPolicy.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n/** An abstraction that controls when the client attempts to reconnect and how many times it does so. */\r\nexport interface IRetryPolicy {\r\n    /** Called after the transport loses the connection.\r\n     *\r\n     * @param {RetryContext} retryContext Details related to the retry event to help determine how long to wait for the next retry.\r\n     *\r\n     * @returns {number | null} The amount of time in milliseconds to wait before the next retry. `null` tells the client to stop retrying.\r\n     */\r\n    nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null;\r\n}\r\n\r\nexport interface RetryContext {\r\n    /**\r\n     * The number of consecutive failed tries so far.\r\n     */\r\n    readonly previousRetryCount: number;\r\n\r\n    /**\r\n     * The amount of time in milliseconds spent retrying so far.\r\n     */\r\n    readonly elapsedMilliseconds: number;\r\n\r\n    /**\r\n     * The error that forced the upcoming retry.\r\n     */\r\n    readonly retryReason: Error;\r\n}\r\n"]}